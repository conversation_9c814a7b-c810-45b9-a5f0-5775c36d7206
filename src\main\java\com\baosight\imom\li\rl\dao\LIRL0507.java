/**
* Generate time : 2025-07-30 10:59:11
* Version : 1.0
*/
package com.baosight.imom.li.rl.dao;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* Tlirl0507
* 
*/
public class LIRL0507 extends DaoEPBase {
    public static final String QUERY = "LIRL0507.query";
    public static final String QUERY_ALL = "LIRL0507.queryAll";
    public static final String COUNT = "LIRL0507.count";
    public static final String UPDATE = "LIRL0507.update";
    public static final String UPDATE_STATUS = "LIRL0507.updateStatus";
    public static final String INSERT = "LIRL0507.insert";
                private String segNo = " ";		/* 业务单元代代码*/
                private String unitCode = " ";		/* 业务单元代代码*/
                private String uuid = " ";		/* uuid*/
                private String status = " ";		/* 状态(撤销：00、新增：10、确认：20)*/
                private String customerId = " ";		/* 承运商/客户代码*/
                private String customerName = " ";		/* 承运商/客户名称*/
                private String driverTel = " ";		/* 司机手机号*/
                private String driverName = " ";		/* 司机姓名*/
                private String driverIdentity = " ";		/* 司机身份证号*/
                private String ladingBillId = " ";		/* 提单号*/
                private String transPlanId = " ";		/* 物流运输计划号*/
                private String recCreator = " ";		/* 记录创建人*/
                private String recCreatorName = " ";		/* 记录创建人姓名*/
                private String recCreateTime = " ";		/* 记录创建时间*/
                private String recRevisor = " ";		/* 记录修改人姓名*/
                private String recRevisorName = " ";		/* 记录修改人*/
                private String recReviseTime = " ";		/* 记录修改时间*/
                private Integer archiveFlag = Integer.valueOf(0);		/* 归档标记*/
                private Integer delFlag = Integer.valueOf(0);		/* 记录删除标记*/
                private String remark = " ";		/* 备注*/
                private String tenantId = " ";		/* 租户ID*/
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("uuid");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态(撤销：00、新增：10、确认：20)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerId");
        eiColumn.setDescName("承运商/客户代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerName");
        eiColumn.setDescName("承运商/客户名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverTel");
        eiColumn.setDescName("司机手机号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverName");
        eiColumn.setDescName("司机姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverIdentity");
        eiColumn.setDescName("司机身份证号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ladingBillId");
        eiColumn.setDescName("提单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("transPlanId");
        eiColumn.setDescName("物流运输计划号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);


}
/**
* the constructor
*/
public LIRL0507() {
initMetaData();
}

        /**
        * get the segNo - 业务单元代代码
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 业务单元代代码
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
        * get the unitCode - 业务单元代代码
        * @return the unitCode
        */
        public String getUnitCode() {
        return this.unitCode;
        }

        /**
        * set the unitCode - 业务单元代代码
        */
        public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        }
        /**
        * get the uuid - uuid
        * @return the uuid
        */
        public String getUuid() {
        return this.uuid;
        }

        /**
        * set the uuid - uuid
        */
        public void setUuid(String uuid) {
        this.uuid = uuid;
        }
        /**
        * get the status - 状态(撤销：00、新增：10、确认：20)
        * @return the status
        */
        public String getStatus() {
        return this.status;
        }

        /**
        * set the status - 状态(撤销：00、新增：10、确认：20)
        */
        public void setStatus(String status) {
        this.status = status;
        }
        /**
        * get the customerId - 承运商/客户代码
        * @return the customerId
        */
        public String getCustomerId() {
        return this.customerId;
        }

        /**
        * set the customerId - 承运商/客户代码
        */
        public void setCustomerId(String customerId) {
        this.customerId = customerId;
        }
        /**
        * get the customerName - 承运商/客户名称
        * @return the customerName
        */
        public String getCustomerName() {
        return this.customerName;
        }

        /**
        * set the customerName - 承运商/客户名称
        */
        public void setCustomerName(String customerName) {
        this.customerName = customerName;
        }
        /**
        * get the driverTel - 司机手机号
        * @return the driverTel
        */
        public String getDriverTel() {
        return this.driverTel;
        }

        /**
        * set the driverTel - 司机手机号
        */
        public void setDriverTel(String driverTel) {
        this.driverTel = driverTel;
        }
        /**
        * get the driverName - 司机姓名
        * @return the driverName
        */
        public String getDriverName() {
        return this.driverName;
        }

        /**
        * set the driverName - 司机姓名
        */
        public void setDriverName(String driverName) {
        this.driverName = driverName;
        }
        /**
        * get the driverIdentity - 司机身份证号
        * @return the driverIdentity
        */
        public String getDriverIdentity() {
        return this.driverIdentity;
        }

        /**
        * set the driverIdentity - 司机身份证号
        */
        public void setDriverIdentity(String driverIdentity) {
        this.driverIdentity = driverIdentity;
        }
        /**
        * get the ladingBillId - 提单号
        * @return the ladingBillId
        */
        public String getLadingBillId() {
        return this.ladingBillId;
        }

        /**
        * set the ladingBillId - 提单号
        */
        public void setLadingBillId(String ladingBillId) {
        this.ladingBillId = ladingBillId;
        }
        /**
        * get the transPlanId - 物流运输计划号
        * @return the transPlanId
        */
        public String getTransPlanId() {
        return this.transPlanId;
        }

        /**
        * set the transPlanId - 物流运输计划号
        */
        public void setTransPlanId(String transPlanId) {
        this.transPlanId = transPlanId;
        }
        /**
        * get the recCreator - 记录创建人
        * @return the recCreator
        */
        public String getRecCreator() {
        return this.recCreator;
        }

        /**
        * set the recCreator - 记录创建人
        */
        public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
        }
        /**
        * get the recCreatorName - 记录创建人姓名
        * @return the recCreatorName
        */
        public String getRecCreatorName() {
        return this.recCreatorName;
        }

        /**
        * set the recCreatorName - 记录创建人姓名
        */
        public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
        }
        /**
        * get the recCreateTime - 记录创建时间
        * @return the recCreateTime
        */
        public String getRecCreateTime() {
        return this.recCreateTime;
        }

        /**
        * set the recCreateTime - 记录创建时间
        */
        public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
        }
        /**
        * get the recRevisor - 记录修改人姓名
        * @return the recRevisor
        */
        public String getRecRevisor() {
        return this.recRevisor;
        }

        /**
        * set the recRevisor - 记录修改人姓名
        */
        public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
        }
        /**
        * get the recRevisorName - 记录修改人
        * @return the recRevisorName
        */
        public String getRecRevisorName() {
        return this.recRevisorName;
        }

        /**
        * set the recRevisorName - 记录修改人
        */
        public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
        }
        /**
        * get the recReviseTime - 记录修改时间
        * @return the recReviseTime
        */
        public String getRecReviseTime() {
        return this.recReviseTime;
        }

        /**
        * set the recReviseTime - 记录修改时间
        */
        public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
        }
        /**
        * get the archiveFlag - 归档标记
        * @return the archiveFlag
        */
        public Integer getArchiveFlag() {
        return this.archiveFlag;
        }

        /**
        * set the archiveFlag - 归档标记
        */
        public void setArchiveFlag(Integer archiveFlag) {
        this.archiveFlag = archiveFlag;
        }
        /**
        * get the delFlag - 记录删除标记
        * @return the delFlag
        */
        public Integer getDelFlag() {
        return this.delFlag;
        }

        /**
        * set the delFlag - 记录删除标记
        */
        public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
        }
        /**
        * get the remark - 备注
        * @return the remark
        */
        public String getRemark() {
        return this.remark;
        }

        /**
        * set the remark - 备注
        */
        public void setRemark(String remark) {
        this.remark = remark;
        }
        /**
        * get the tenantId - 租户ID
        * @return the tenantId
        */
        public String getTenantId() {
        return this.tenantId;
        }

        /**
        * set the tenantId - 租户ID
        */
        public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
        }
/**
* get the value from Map
*/
public void fromMap(Map map) {

                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
                setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
                setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
                setCustomerId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerId")), customerId));
                setCustomerName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerName")), customerName));
                setDriverTel(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("driverTel")), driverTel));
                setDriverName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("driverName")), driverName));
                setDriverIdentity(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("driverIdentity")), driverIdentity));
                setLadingBillId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ladingBillId")), ladingBillId));
                setTransPlanId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("transPlanId")), transPlanId));
                setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
                setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
                setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
                setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
                setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
                setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
                setArchiveFlag(NumberUtils.toInteger(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
                setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
                setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
                setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
                map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
                map.put("status",StringUtils.toString(status, eiMetadata.getMeta("status")));
                map.put("customerId",StringUtils.toString(customerId, eiMetadata.getMeta("customerId")));
                map.put("customerName",StringUtils.toString(customerName, eiMetadata.getMeta("customerName")));
                map.put("driverTel",StringUtils.toString(driverTel, eiMetadata.getMeta("driverTel")));
                map.put("driverName",StringUtils.toString(driverName, eiMetadata.getMeta("driverName")));
                map.put("driverIdentity",StringUtils.toString(driverIdentity, eiMetadata.getMeta("driverIdentity")));
                map.put("ladingBillId",StringUtils.toString(ladingBillId, eiMetadata.getMeta("ladingBillId")));
                map.put("transPlanId",StringUtils.toString(transPlanId, eiMetadata.getMeta("transPlanId")));
                map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
                map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
                map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
                map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
                map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
                map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
                map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
                map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
                map.put("remark",StringUtils.toString(remark, eiMetadata.getMeta("remark")));
                map.put("tenantId",StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));

return map;

}
}