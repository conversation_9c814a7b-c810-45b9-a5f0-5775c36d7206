/**
* Generate time : 2025-07-04 09:28:12
* Version : 1.0
*/
package com.baosight.imom.li.rl.service;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.imom.common.li.domain.Tlirl0599;

public class ServiceLIRL0599 extends ServiceBase {

    public EiInfo initLoad(EiInfo inInfo) {
        return super.initLoad(inInfo,new Tlirl0599());
    }

    @Override
    public EiInfo query(EiInfo inInfo) {
        return super.query(inInfo,"tlirl0599.query",new Tlirl0599());
    }

    @Override
    public EiInfo insert(EiInfo inInfo) {
        return super.insert(inInfo,"tlirl0599.insert");
    }

    @Override
    public EiInfo update(EiInfo inInfo) {
        return super.update(inInfo,"tlirl0599.update");
    }

    @Override
    public EiInfo delete(EiInfo inInfo) {
        return super.delete(inInfo,"tlirl0599.delete");
    }

}