package com.baosight.imom.li.rl.service;

import com.baosight.imom.common.utils.MapUtils;
import com.baosight.imom.li.ds.domain.LIDS0601;
import com.baosight.imom.li.ds.domain.LIDS0901;
import com.baosight.imom.li.rl.dao.*;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Collections;


/***
 * 大屏看板接口
 */
public class ServiceLIRLInterfaceKB extends ServiceBase {
    private static final Logger logger = LoggerFactory.getLogger(ServiceLIRLInterfaces.class);

    /***
     * 查询所有装卸点的装卸类型、数据
     * S_LI_RL_0045
     */
    public EiInfo queryLoadingAndUnloadingTypesAndData(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            String segNo = (String) inInfo.get("segNo");

            Map<String, String> queryHashMap = new HashMap<>();
            queryHashMap.put("segNo", segNo);
            //查询所有装卸点数据
            List<HashMap> list = dao.query(LIRL0406.QUERY_LOADING_AND_UNLOADING_TYPES_AND_DATA, queryHashMap);

            //查询所有装卸点
            List<HashMap> listHandPointId = dao.query(LIRL0406.QUERY_HAND_POINT_ID, queryHashMap);

            List<HashMap> restList = new ArrayList<>();
            int waitCount = 0;
            int workingCount = 0;
            for (HashMap hashMap : listHandPointId) {
                String hand_point_name = MapUtils.getString(hashMap, "handPointName", "");
                String handPointId = MapUtils.getString(hashMap, "handPointId", "");
                for (HashMap hashMap1 : list) {
                    String hand_point_name2 = MapUtils.getString(hashMap1, "handPointName", "");
                    String currentHandPointName = MapUtils.getString(hashMap1, "currentHandPointName", "");
                    String status = MapUtils.getString(hashMap1, "status", "");
                    String targetHandPointId = MapUtils.getString(hashMap1, "targetHandPointId", "");
                    if (hand_point_name.equals(hand_point_name2)||hand_point_name.equals(currentHandPointName)) {
                        HashMap rest = new HashMap();
                        if ("30".equals(status)){
                            rest.put("handPointId", handPointId);
                            rest.put("hashMap", hashMap1);
                            rest.put("status",20);
                            workingCount++;
                        }
                        //遍历
                        restList.add(rest);
                    }
                }
                for (HashMap hashMap1 : list) {
                    String hand_point_name2 = MapUtils.getString(hashMap1, "handPointName", "");
                    String currentHandPointName = MapUtils.getString(hashMap1, "currentHandPointName", "");
                    String status = MapUtils.getString(hashMap1, "status", "");
                    String targetHandPointId = MapUtils.getString(hashMap1, "targetHandPointId", "");
                    if (hand_point_name.equals(hand_point_name2)||hand_point_name.equals(currentHandPointName)) {
                        HashMap rest = new HashMap();
                        if ("20".equals(status)||("40".equals(status)&& StringUtils.isNotBlank(targetHandPointId))){
                            rest.put("status",10);
                            rest.put("handPointId", handPointId);
                            rest.put("hashMap", hashMap1);
                            waitCount++;
                        }
                        restList.add(rest);
                    }
                }
            }


            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.set("list", restList);
            outInfo.set("handPointIdList", listHandPointId);

            //排队等待
            Map<String, String> queryHashMapLirl0301 = new HashMap<>();
            queryHashMapLirl0301.put("segNo", segNo);
            queryHashMapLirl0301.put("status", "10");
            //查询排队等待数据 0301 20
            List<HashMap> listQueue = this.dao.query(LIRL0301.QUERY_K_BDATA, queryHashMapLirl0301);
            //查询总数
            int count = super.count(LIRL0301.QUERY_K_BDATA_COUNT, queryHashMapLirl0301);
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.set("listWait", listQueue);
            outInfo.set("waitCount", count);

            //未预约待审核
            Map<String, String> queryHashMapLirl0302 = new HashMap<>();
            queryHashMapLirl0302.put("segNo", segNo);
            //查询排队等待数据 0401
            List<HashMap> listLirl0302 = dao.query(LIRL0302.CHECK_PENDING_NON_APPOINTMENT, queryHashMapLirl0302);
            //查询总数
            int countR = super.count(LIRL0302.CHECK_PENDING_NON_APPOINTMENT_COUNT, queryHashMapLirl0302);
            LinkedHashSet<HashMap> set = new LinkedHashSet<>(listLirl0302);
            List<HashMap> hashMapQueue = new ArrayList<>(set);
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.set("listQueue", hashMapQueue);
            outInfo.set("queueCount", countR);

            //叫号
            Map<String, String> queryHashMapLirl0402 = new HashMap<>();
            queryHashMapLirl0402.put("segNo", segNo);
            //查询排队等待数据 0401
            List<HashMap> listCallNum = dao.query(LIRL0406.LINE_UP_AND_CALL_FOR_NUMBERS, queryHashMapLirl0402);
            LinkedHashSet<HashMap> set1 = new LinkedHashSet<>(listCallNum);
            List<HashMap> hashMaps = new ArrayList<>(set1);
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.set("listCallNum", hashMaps);

            //查询已作业的车辆信息
            //查询已作业的车辆
            List<HashMap> queryWorkingList = this.dao.query(LIRL0301.QUERY11, queryHashMap);
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.set("workingList", queryWorkingList);

            //查询等待作业的车辆
            List<HashMap> queryWaitingList = this.dao.query(LIRL0301.QUERY12, queryHashMap);
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.set("waitingList", queryWaitingList);


        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            logger.error(e.getMessage());
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }
        return outInfo;
    }

    /***
     * 排队等待
     * S_LI_RL_0046
     */
    public EiInfo queryQueue(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            String segNo = (String) inInfo.get("segNo");

            Map<String, String> queryHashMap = new HashMap<>();
            queryHashMap.put("segNo", segNo);
            queryHashMap.put("status", "10");
            //查询排队等待数据 0301 20
            List<HashMap> list = this.dao.query(LIRL0301.QUERY_K_BDATA, queryHashMap);
            //查询总数
            int count = super.count(LIRL0301.QUERY_K_BDATA_COUNT, queryHashMap);
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.set("list", list);
            outInfo.set("count", count);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            logger.error(e.getMessage());
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }
        return outInfo;
    }

    /***
     * 排队叫号
     * S_LI_RL_0047
     */
    public EiInfo lineUpAndCallForNumbers(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            String segNo = (String) inInfo.get("segNo");

            Map<String, String> queryHashMap = new HashMap<>();
            queryHashMap.put("segNo", segNo);
            //查询排队等待数据 0401
            List<HashMap> list = dao.query(LIRL0406.LINE_UP_AND_CALL_FOR_NUMBERS, queryHashMap);
            LinkedHashSet<HashMap> set = new LinkedHashSet<>(list);
            list = new ArrayList<>(set);
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.set("list", list);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            logger.error(e.getMessage());
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }
        return outInfo;
    }

    /***
     * 今日装卸货看板
     * S_LI_RL_0048
     */
    public EiInfo todayLoadingAndUnloadingKanban(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            String stringDate = DateUtil.curDateTimeStr14();//当前时间
            SimpleDateFormat dateFormat = new SimpleDateFormat("HH:mm");
            SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyyMMddHHmmss");
            Calendar cal = Calendar.getInstance();
            String segNo = (String) inInfo.get("segNo");
            String time = (String) inInfo.get("time");
            String type = (String) inInfo.get("type"); // 1默认视图ALL预约 2只看本日预约 3明天预约 4今日看板

            //是否完成装卸货 complete 10 已完成

            Map<String, String> queryHashMap = new HashMap<>();
            if ("4".equals(type)){
                queryHashMap.put("segNo", segNo);
                queryHashMap.put("reservationDate", time);
                //查询跟踪表数据
                List<HashMap> list = dao.query(LIRL0301.TODAY_LOADING_AND_UNLOADING_KANBAN, queryHashMap);
                List<HashMap> listWork = new ArrayList<>();
                List<HashMap> listOut = new ArrayList<>();

                List<String> stringList = new ArrayList<>();
                List<String> stringTimeList = new ArrayList<>();
                //预约单号间隔2两小时
                for (HashMap hashMap:list){
                    String reservationTime = MapUtils.getString(hashMap, "reservationTime", "");
                    String[] split = reservationTime.split("-");
                    String fTime = split[0];
                    String[] split1 = fTime.split(":");
                    time = split1[0] + ":00";
                    Date date = dateFormat.parse(time);
                    cal.setTime(date);
                    cal.add(Calendar.HOUR, 1);
                    String format = dateFormat.format(cal.getTime());
                    stringList.add(time);
                    stringList.add(format);
                    hashMap.put("intervalTime",time+"-"+format);
                    stringTimeList.add(MapUtils.getString(hashMap,"intervalTime",""));
                }
                for(HashMap hashMap:list){
                    String status = MapUtils.getString(hashMap, "status", "");
                    if ("50".equals(status)){
                        listOut.add(hashMap);
                    }else {
                        listWork.add(hashMap);
                    };
                }
                //List去重
                LinkedHashSet<String> set = new LinkedHashSet<>(stringTimeList);
                stringTimeList = new ArrayList<>(set);
                //每个时间段下组装数据
                List<HashMap> dataList = new ArrayList<>();
                for (String stringTime:stringTimeList){
                    HashMap data = new HashMap<>();
                    String[] split = stringTime.split("-");
                    String[] split1 = split[0].split(":");
                    String sTime = split1[0];
                    data.put("sTime",sTime);
                    data.put("time",stringTime);
                    List<HashMap> hashMapList = new ArrayList<>();
                    for (HashMap hashMap:listWork){
                        String intervalTime = MapUtils.getString(hashMap, "intervalTime");
                        if (stringTime.equals(intervalTime)){
                            hashMapList.add(hashMap);
                        }
                    }
                    data.put("list",hashMapList);
                    dataList.add(data);
                }

                //每个时间段下组装数据 离场车辆
                List<HashMap> dataOutList = new ArrayList<>();
                HashMap dataOutListHashMap = new HashMap();
                dataOutListHashMap.put("list",listOut);
                dataOutList.add(dataOutListHashMap);
                LinkedHashSet<HashMap> Mapset = new LinkedHashSet<>(dataList);
                dataList = new ArrayList<>(Mapset);
                //重新排序
                // 使用Comparator定义排序规则，按照"age"键对应的值进行升序排序
                Collections.sort(dataList, (map1, map2) -> {
                    BigDecimal age1 = new BigDecimal((String)map1.get("sTime"));
                    BigDecimal age2 = new BigDecimal((String)map2.get("sTime"));
                    return age1.compareTo(age2);
                });
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                outInfo.set("listWork", dataList);
                outInfo.set("listOut", dataOutList);
            }else {
                if ("3".equals(type)){
                    // 获取当前日期
                    Calendar calendar = Calendar.getInstance();
                    // 在当前日期的基础上加一天
                    calendar.add(Calendar.DAY_OF_MONTH, 1);
                    Date time2 = calendar.getTime();
                    stringDate  = dateFormat2.format(time2);
                    queryHashMap.put("reservationDate", stringDate);
                }else if ("2".equals(type)){
                    queryHashMap.put("reservationDate", stringDate);
                }
                queryHashMap.put("segNo", segNo);
                queryHashMap.put("delFlag", "0");
                //明天预约
                List<HashMap> list = dao.query(LIRL0201.QUERY_HASH_MAP, queryHashMap);
                List<HashMap> listWork = new ArrayList<>();
                List<HashMap> listOut = new ArrayList<>();

                List<String> stringList = new ArrayList<>();
                List<String> stringTimeList = new ArrayList<>();
                //预约单号间隔2两小时
                for (HashMap hashMap:list){
                    String reservationTime = MapUtils.getString(hashMap, "reservationTime", "");
                    String[] split = reservationTime.split("-");
                    String fTime = split[0];
                    String[] split1 = fTime.split(":");
                    time = split1[0] + ":00";
                    Date date = dateFormat.parse(time);
                    cal.setTime(date);
                    cal.add(Calendar.HOUR, 1);
                    String format = dateFormat.format(cal.getTime());
                    stringList.add(time);
                    stringList.add(format);
                    hashMap.put("intervalTime",time+"-"+format);
                    stringTimeList.add(MapUtils.getString(hashMap,"intervalTime",""));
                }
                for(HashMap hashMap:list){
                    String status = MapUtils.getString(hashMap, "status", "");
                    if ("50".equals(status)){
                        listOut.add(hashMap);
                    }else {
                        listWork.add(hashMap);
                    };
                }
                //List去重
                LinkedHashSet<String> set = new LinkedHashSet<>(stringTimeList);
                stringTimeList = new ArrayList<>(set);
                //每个时间段下组装数据
                List<HashMap> dataList = new ArrayList<>();
                for (String stringTime:stringTimeList){
                    HashMap data = new HashMap<>();
                    String[] split = stringTime.split("-");
                    String[] split1 = split[0].split(":");
                    String sTime = split1[0];
                    data.put("sTime",sTime);
                    data.put("time",stringTime);
                    List<HashMap> hashMapList = new ArrayList<>();
                    for (HashMap hashMap:listWork){
                        String intervalTime = MapUtils.getString(hashMap, "intervalTime");
                        if (stringTime.equals(intervalTime)){
                            hashMapList.add(hashMap);
                        }
                    }
                    data.put("list",hashMapList);
                    dataList.add(data);
                }

                //每个时间段下组装数据 离场车辆
                List<HashMap> dataOutList = new ArrayList<>();
                HashMap dataOutListHashMap = new HashMap();
                dataOutListHashMap.put("list",listOut);
                dataOutList.add(dataOutListHashMap);
                LinkedHashSet<HashMap> Mapset = new LinkedHashSet<>(dataList);
                dataList = new ArrayList<>(Mapset);
                //重新排序
                // 使用Comparator定义排序规则，按照"age"键对应的值进行升序排序
                Collections.sort(dataList, (map1, map2) -> {
                    BigDecimal age1 = new BigDecimal((String)map1.get("sTime"));
                    BigDecimal age2 = new BigDecimal((String)map2.get("sTime"));
                    return age1.compareTo(age2);
                });
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                outInfo.set("listWork", dataList);
                outInfo.set("listOut", dataOutList);
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            logger.error(e.getMessage());
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }
        return outInfo;
    }


    /***
     * 未预约待审核
     * @S_LI_RL_0102
     */
    public EiInfo reservationPendingApproval(EiInfo inInfo) {

        EiInfo outInfo = new EiInfo();
        try {
            String segNo = (String) inInfo.get("segNo");
            Map<String, String> queryHashMap = new HashMap<>();
            queryHashMap.put("segNo", segNo);
            //查询排队等待数据 0401
            List<HashMap> list = dao.query(LIRL0302.CHECK_PENDING_NON_APPOINTMENT, queryHashMap);
            //查询总数
            int count = super.count(LIRL0302.CHECK_PENDING_NON_APPOINTMENT_COUNT, queryHashMap);
            LinkedHashSet<HashMap> set = new LinkedHashSet<>(list);
            list = new ArrayList<>(set);
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.set("list", list);
            outInfo.set("count", count);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            logger.error(e.getMessage());
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }
        return outInfo;

    }

    /***
     * 查询所有装卸点的装卸类型、数据(重庆)
     * S_LI_RL_0138
     */
    public EiInfo queryLoadingAndUnloadingTypesAndDataCQ(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            String segNo = (String) inInfo.get("segNo");

            Map<String, String> queryHashMap = new HashMap<>();
            queryHashMap.put("segNo", segNo);
            //查询所有装卸点数据
            List<HashMap> list = dao.query(LIRL0406.QUERY_LOADING_AND_UNLOADING_TYPES_AND_DATA_CQ, queryHashMap);

            //查询所有装卸点
            List<HashMap> listHandPointId = dao.query(LIRL0406.QUERY_HAND_POINT_ID, queryHashMap);

            List<HashMap> restList = new ArrayList<>();
            int waitCount = 0;
            int workingCount = 0;
            for (HashMap hashMap : listHandPointId) {
                String hand_point_name = MapUtils.getString(hashMap, "handPointName", "");
                String handPointId = MapUtils.getString(hashMap, "handPointId", "");
                for (HashMap hashMap1 : list) {
                    String hand_point_name2 = MapUtils.getString(hashMap1, "handPointName", "");
                    String currentHandPointName = MapUtils.getString(hashMap1, "currentHandPointName", "");
                    String status = MapUtils.getString(hashMap1, "status", "");
                    String targetHandPointId = MapUtils.getString(hashMap1, "targetHandPointId", "");
                    if (hand_point_name.equals(hand_point_name2)||hand_point_name.equals(currentHandPointName)) {
                        HashMap rest = new HashMap();
                        if ("30".equals(status)){
                            rest.put("handPointId", handPointId);
                            rest.put("hashMap", hashMap1);
                            rest.put("status",20);
                            workingCount++;
                        }
                        //遍历
                        restList.add(rest);
                    }
                }
                for (HashMap hashMap1 : list) {
                    String hand_point_name2 = MapUtils.getString(hashMap1, "handPointName", "");
                    String currentHandPointName = MapUtils.getString(hashMap1, "currentHandPointName", "");
                    String status = MapUtils.getString(hashMap1, "status", "");
                    String targetHandPointId = MapUtils.getString(hashMap1, "targetHandPointId", "");
                    if (hand_point_name.equals(hand_point_name2)||hand_point_name.equals(currentHandPointName)) {
                        HashMap rest = new HashMap();
                        if (("20".equals(status)&& StringUtils.isNotBlank(targetHandPointId))||("40".equals(status)&& StringUtils.isNotBlank(targetHandPointId))){
                            rest.put("status",10);
                            rest.put("handPointId", handPointId);
                            rest.put("hashMap", hashMap1);
                            waitCount++;
                        }
                        restList.add(rest);
                    }
                }
            }


            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.set("list", restList);
            outInfo.set("handPointIdList", listHandPointId);

            // 查询捆包数据并添加到restList中
            for (HashMap restItem : restList) {
                HashMap vehicleData = (HashMap) restItem.get("hashMap");
                if (vehicleData != null) {
                    String vehicleNo = MapUtils.getString(vehicleData, "vehicleNo", "");
                    String carTraceNo = MapUtils.getString(vehicleData, "carTraceNo", "");
                    
                    if (StringUtils.isNotBlank(vehicleNo) && StringUtils.isNotBlank(carTraceNo)) {
                        // 查询捆包数据
                        Map<String, String> packQueryParam = new HashMap<>();
                        packQueryParam.put("segNo", segNo);
                        packQueryParam.put("vehicleNo", vehicleNo);
                        packQueryParam.put("carTraceNo", carTraceNo);
                        
                        List<HashMap> packDataList = dao.query(LIRL0502.QUERY_PACK_DATA_FOR_KANBAN_CQ, packQueryParam);
                        
                        if (CollectionUtils.isNotEmpty(packDataList)) {
                            // 获取捆包个数*5的值，放到车辆数据中
                            HashMap packData = packDataList.get(0);
                            Integer packCount = MapUtils.getInt(packData, "packCount");
                            vehicleData.put("packCount", packCount);
                        } else {
                            vehicleData.put("packCount", 0);
                        }
                    } else {
                        vehicleData.put("packCount", 0);
                    }
                }
            }


            //叫号
            Map<String, String> queryHashMapLirl0402 = new HashMap<>();
            queryHashMapLirl0402.put("segNo", segNo);
            //查询排队等待数据 0401
            List<HashMap> listCallNum = dao.query(LIRL0406.LINE_UP_AND_CALL_FOR_NUMBERS_CQ, queryHashMapLirl0402);
            LinkedHashSet<HashMap> set1 = new LinkedHashSet<>(listCallNum);
            List<HashMap> hashMaps = new ArrayList<>(set1);
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.set("listCallNum", hashMaps);

            //查询已作业的车辆信息
            //查询已作业的车辆
            List<HashMap> queryWorkingList = this.dao.query(LIRL0301.QUERY11, queryHashMap);
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.set("workingList", queryWorkingList);

            //查询等待作业的车辆
            List<HashMap> queryWaitingList = this.dao.query(LIRL0301.QUERY12, queryHashMap);
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.set("waitingList", queryWaitingList);

            //已配单未叫号车辆
            List<HashMap> queryAllocVehicleList = this.dao.query(LIRL0503.GET_PUTIN_PLAN_KB, queryHashMap);
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.set("allocVehicleList", queryAllocVehicleList);


        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            logger.error(e.getMessage());
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }
        return outInfo;
    }

    /***
     * 查询所有装卸点的装卸类型、数据(重庆)----司机
     * S_LI_RL_01381
     */
    public EiInfo queryLoadingAndUnloadingTypesAndDataCQInDriver(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            String segNo = (String) inInfo.get("segNo");

            Map<String, String> queryHashMap = new HashMap<>();
            queryHashMap.put("segNo", segNo);
            //查询所有装卸点数据
            List<HashMap> list = dao.query(LIRL0406.QUERY_LOADING_AND_UNLOADING_TYPES_AND_DATA_CQ_DRIVER, queryHashMap);

            //查询所有装卸点
            List<HashMap> listHandPointId = dao.query(LIRL0406.QUERY_HAND_POINT_ID_DRIVER, queryHashMap);

            List<HashMap> restList = new ArrayList<>();
            int waitCount = 0;
            int workingCount = 0;
            for (HashMap hashMap : listHandPointId) {
                String hand_point_name = MapUtils.getString(hashMap, "handPointName", "");
                String handPointId = MapUtils.getString(hashMap, "handPointId", "");
                for (HashMap hashMap1 : list) {
                    String hand_point_name2 = MapUtils.getString(hashMap1, "handPointName", "");
                    String currentHandPointName = MapUtils.getString(hashMap1, "currentHandPointName", "");
                    String status = MapUtils.getString(hashMap1, "status", "");
                    String targetHandPointId = MapUtils.getString(hashMap1, "targetHandPointId", "");
                    if (hand_point_name.equals(hand_point_name2)||hand_point_name.equals(currentHandPointName)) {
                        HashMap rest = new HashMap();
                        if ("30".equals(status)){
                            rest.put("handPointId", handPointId);
                            rest.put("hashMap", hashMap1);
                            rest.put("status",20);
                            workingCount++;
                        }
                        //遍历
                        restList.add(rest);
                    }
                }
                for (HashMap hashMap1 : list) {
                    String hand_point_name2 = MapUtils.getString(hashMap1, "handPointName", "");
                    String currentHandPointName = MapUtils.getString(hashMap1, "currentHandPointName", "");
                    String status = MapUtils.getString(hashMap1, "status", "");
                    String targetHandPointId = MapUtils.getString(hashMap1, "targetHandPointId", "");
                    if (hand_point_name.equals(hand_point_name2)||hand_point_name.equals(currentHandPointName)) {
                        HashMap rest = new HashMap();
                        if (("20".equals(status)&& StringUtils.isNotBlank(targetHandPointId))||("40".equals(status)&& StringUtils.isNotBlank(targetHandPointId))){
                            rest.put("status",10);
                            rest.put("handPointId", handPointId);
                            rest.put("hashMap", hashMap1);
                            waitCount++;
                        }
                        restList.add(rest);
                    }
                }
            }


            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.set("list", restList);
            outInfo.set("handPointIdList", listHandPointId);

            // 查询捆包数据并添加到restList中
            // TODO: 查询这辆车在配车单子表的装卸点和当前装卸点做关联，查询这个装卸点下的捆包*5分钟(待讨论)，形提默认1小时
            // TODO: 正在作业的车辆显示在第一个, 排队的车根据车辆排队表排序
            for (HashMap restItem : restList) {
                HashMap vehicleData = (HashMap) restItem.get("hashMap");
                if (vehicleData != null) {
                    String vehicleNo = MapUtils.getString(vehicleData, "vehicleNo", "");
                    String carTraceNo = MapUtils.getString(vehicleData, "carTraceNo", "");

                    if (StringUtils.isNotBlank(vehicleNo) && StringUtils.isNotBlank(carTraceNo)) {
                        // 查询捆包数据
                        Map<String, String> packQueryParam = new HashMap<>();
                        packQueryParam.put("segNo", segNo);
                        packQueryParam.put("vehicleNo", vehicleNo);
                        packQueryParam.put("carTraceNo", carTraceNo);

                        List<HashMap> packDataList = dao.query(LIRL0502.QUERY_PACK_DATA_FOR_KANBAN_CQ, packQueryParam);

                        if (CollectionUtils.isNotEmpty(packDataList)) {
                            // 获取捆包个数*5的值，放到车辆数据中
                            HashMap packData = packDataList.get(0);
                            Integer packCount = MapUtils.getInt(packData, "packCount");
                            vehicleData.put("packCount", packCount);
                        } else {
                            vehicleData.put("packCount", 0);
                        }
                    } else {
                        vehicleData.put("packCount", 0);
                    }
                }
            }


            //叫号
            Map<String, String> queryHashMapLirl0402 = new HashMap<>();
            queryHashMapLirl0402.put("segNo", segNo);
            //查询排队等待数据 0401
            List<HashMap> listCallNum = dao.query(LIRL0406.LINE_UP_AND_CALL_FOR_NUMBERS_CQ, queryHashMapLirl0402);
            LinkedHashSet<HashMap> set1 = new LinkedHashSet<>(listCallNum);
            List<HashMap> hashMaps = new ArrayList<>(set1);
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.set("listCallNum", hashMaps);

            //查询已作业的车辆信息
            //查询已作业的车辆
            List<HashMap> queryWorkingList = this.dao.query(LIRL0301.QUERY11, queryHashMap);
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.set("workingList", queryWorkingList);

            //查询等待作业的车辆
            List<HashMap> queryWaitingList = this.dao.query(LIRL0301.QUERY12, queryHashMap);
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.set("waitingList", queryWaitingList);

            //已配单未叫号车辆
            List<HashMap> queryAllocVehicleList = this.dao.query(LIRL0503.GET_PUTIN_PLAN_KB, queryHashMap);
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.set("allocVehicleList", queryAllocVehicleList);


        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            logger.error(e.getMessage());
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }
        return outInfo;
    }

    /***
     * 重庆大屏看板查询车辆数
     *
     * S_LI_RL_0181
     */
        public EiInfo queryVehicleCountCQ(EiInfo inInfo) {
        String segNo= (String) inInfo.get("segNo");
        if (StringUtils.isBlank(segNo)){
            segNo = "JC000000";
        }
        Map<String, Object> result = new HashMap<>();
        Map<String, String> queryHashMap = new HashMap<>();
        queryHashMap.put("segNo", segNo);
        try {
            //查询当天预约车辆数
            List<String> queryLIRL0201 = this.dao.query(LIRL0201.QUERY_VEHICLE_COUNT_CQ, queryHashMap);
            result.put("appointmentCount", queryLIRL0201.get(0));
            inInfo.set("appointmentCount", Integer.parseInt(queryLIRL0201.get(0)));
            //查询当前排队车辆数
            List<String> queryLIRL0401 = this.dao.query(LIRL0401.QUERY_VEHICLE_COUNT_CQ, queryHashMap);
            inInfo.set("waitCount", Integer.parseInt(queryLIRL0401.get(0)));
            result.put("waitCount", queryLIRL0401.get(0));
            //查询当前叫号车辆信息
            List<HashMap> queryLIRL0402 = this.dao.query(LIRL0402.QUERY_VEHICLE_COUNT_CQ, queryHashMap);
            if (CollectionUtils.isEmpty(queryLIRL0402)){
                inInfo.set("callNumCount", 0);
                result.put("callNumCount", 0);
            }else {
                inInfo.set("callNumCount", queryLIRL0402.size());
                result.put("callNumCount", queryLIRL0402.size());
            }
            //查询正在作业车辆数
            List<String> queryLIRL0301 =  this.dao.query(LIRL0301.QUERY_VEHICLE_COUNT_CQ, queryHashMap);
            inInfo.set("workingCount", Integer.parseInt(queryLIRL0301.get(0)));
            result.put("workingCount", queryLIRL0301.get(0));
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            logger.error(e.getMessage());
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

//    /***
//     * 重庆原料看板，动态查询库位
//     *
//     * 	S_LI_RL_0182
//     */
//    public EiInfo queryLocationCQ(EiInfo inInfo) {
//        String segNo= (String) inInfo.get("segNo");
//        if (StringUtils.isBlank(segNo)){
//            segNo = "JC000000";
//        }
//        try {
//            HashMap<String, Object> hashMap = new HashMap<>();
//            hashMap.put("segNo",  segNo);
//            hashMap.put("status",  "10");
//            hashMap.put("factoryBuilding",  "F1");
//            hashMap.put("locationMark",  "10");
//            List<HashMap> query = this.dao.query(LIDS0601.QUERY_LOCATION_CQ, hashMap);
//            inInfo.set("listL", query);
//            //查询原料库位是否被占用
//            List<HashMap> queryLIDS0901 = this.dao.query(LIDS0901.QUERY_LOCATION_OCCUPY_C_Q, hashMap);
//            inInfo.set("listK", queryLIDS0901);
//
//            //计算一期总占用量
//            List<HashMap> queryLIDS0901Sum=new ArrayList<>();
//            BigDecimal sumCount = new BigDecimal("0");
//            BigDecimal sumCrossAreaCount = new BigDecimal("0");
//            List<HashMap> querySumOccupy = this.dao.query(LIDS0601.QUERY_LOCATION_OCCUPY_C_Q_SUM, hashMap);
//            if (CollectionUtils.isNotEmpty(querySumOccupy)){
//                for (HashMap map : querySumOccupy) {
//                    String crossArea = (String) map.get("crossArea");
//                    BigDecimal singleCount = (BigDecimal) map.get("count");
//                    if ("A".equals(crossArea)){
//                        //A跨
//                        hashMap.put("crossArea",crossArea);
//                        queryLIDS0901Sum = this.dao.query(LIDS0901.QUERY_LOCATION_OCCUPY_C_Q_SUM, hashMap);
//                        if (CollectionUtils.isNotEmpty(queryLIDS0901Sum)){
//                            Long singCount = (Long) queryLIDS0901Sum.get(0).get("count");
//                            BigDecimal countA = new BigDecimal(singCount.toString());
//                            //计算A跨当前占用量占用
//                            countA = countA.divide(singleCount);
//                            sumCrossAreaCount.add(countA);
//                            inInfo.set("listD",countA);
//                        }
//                        sumCount.add(singleCount);
//                    }else if ("B".equals(crossArea)){
//                        hashMap.put("crossArea",crossArea);
//                        queryLIDS0901Sum = this.dao.query(LIDS0901.QUERY_LOCATION_OCCUPY_C_Q_SUM, hashMap);
//                        if (CollectionUtils.isNotEmpty(queryLIDS0901Sum)){
//                            Long singCount = (Long) queryLIDS0901Sum.get(0).get("count");
//                            BigDecimal countB = new BigDecimal(singCount.toString());
//                            countB = countB.divide(singleCount).setScale(2, BigDecimal.ROUND_HALF_UP);
//                            sumCrossAreaCount.add(countB);
//                            inInfo.set("listD",countB);
//                        }
//                        sumCount.add(singleCount);
//
//                    }else if ("C".equals(crossArea)){
//                        hashMap.put("crossArea",crossArea);
//                        queryLIDS0901Sum = this.dao.query(LIDS0901.QUERY_LOCATION_OCCUPY_C_Q_SUM, hashMap);
//                        if (CollectionUtils.isNotEmpty(queryLIDS0901Sum)){
//                            Long singCount = (Long) queryLIDS0901Sum.get(0).get("count");
//                            BigDecimal countC = new BigDecimal(singCount.toString());
//                            countC = countC.divide(singleCount).setScale(2, BigDecimal.ROUND_HALF_UP);
//                            sumCrossAreaCount.add(countC);
//                            inInfo.set("listD",countC);
//                        }
//                        sumCount.add(singleCount);
//
//                    }else if ("D".equals(crossArea)){
//                        hashMap.put("crossArea",crossArea);
//                        queryLIDS0901Sum = this.dao.query(LIDS0901.QUERY_LOCATION_OCCUPY_C_Q_SUM, hashMap);
//                        if (CollectionUtils.isNotEmpty(queryLIDS0901Sum)){
//                            Long singCount = (Long) queryLIDS0901Sum.get(0).get("count");
//                            BigDecimal countD = new BigDecimal(singCount.toString());
//                            countD = countD.divide(singleCount).setScale(2, BigDecimal.ROUND_HALF_UP);
//                            sumCrossAreaCount.add(countD);
//                            inInfo.set("listD",countD);
//                        }
//                        sumCount.add(singleCount);
//                    }
//                }
//                //计算总的
//                inInfo.set("listSum",sumCrossAreaCount.divide(sumCrossAreaCount));
//            }
//            inInfo.set("status", EiConstant.STATUS_SUCCESS);
//        } catch (Exception e) {
//            inInfo.setStatus(EiConstant.STATUS_FAILURE);
//            logger.error(e.getMessage());
//            inInfo.setMsg(e.getMessage());
//        }
//        return inInfo;
//    }

    /***
     * 根据原料库位查询库位是否被占用
     *
     * S_LI_RL_0183
     */
    public EiInfo queryLocationOccupyCQ(EiInfo inInfo) {
        String segNo= (String) inInfo.get("segNo");
        if (StringUtils.isBlank(segNo)){
            segNo = "JC000000";
        }
        try {
            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("segNo",  segNo);
            List<HashMap> query = this.dao.query(LIDS0901.QUERY_LOCATION_OCCUPY_C_Q, hashMap);
            inInfo.set("list", query);
            inInfo.set("status", EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            logger.error(e.getMessage());
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

    /***
     * 重庆设备大屏看板查询人员、车辆工时
     * S_LI_RL_0184
     */
    public EiInfo queryDeviceWorkTimeCQ(EiInfo inInfo) {
        String segNo= (String) inInfo.get("segNo");
        if (StringUtils.isBlank(segNo)){
            segNo = "JC000000";
        }
        try {
            Map<String, Object> result = new HashMap<>();
            Map<String, Object> params = new HashMap<>();
            params.put("segNo", segNo);

            // 1. 当月员工平均作业量
            List<HashMap<String, Object>> avgWorkload = this.dao.query("LIRL0308.queryAvgWorkload", params);
            result.put("avgWorkload", avgWorkload);

            // 当月员工平均作业量明细
            List<HashMap<String, Object>> workloadDetails = this.dao.query("LIRL0308.queryWorkloadDetails", params);
            result.put("workloadDetails", workloadDetails);

            // 2. 当月员工平均作业时长
            List<HashMap<String, Object>> avgWorkHours = this.dao.query("LIRL0308.queryAvgWorkHours", params);
            result.put("avgWorkHours", avgWorkHours);

            // 当月员工平均作业时长明细
            List<Map<String, Object>> workHoursDetails = this.dao.query("LIRL0308.queryWorkHoursDetails", params);
            result.put("workHoursDetails", workHoursDetails);

            // 3. 当月单车平均装载时间
            List<HashMap<String, Object>> avgLoadTime = this.dao.query("LIRL0308.queryAvgLoadTime", params);
            result.put("avgLoadTime", avgLoadTime);

            // 当月单车平均装载时间明细
            List<Map<String, Object>> loadTimeDetails = this. dao.query("LIRL0308.queryLoadTimeDetails", params);
            result.put("loadTimeDetails", loadTimeDetails);

            // 4. 当月单车平均装载重量
            List<HashMap<String, Object>> avgLoadWeight = this.dao.query("LIRL0308.queryAvgLoadWeight", params);
            result.put("avgLoadWeight", avgLoadWeight);

            // 当月单车平均装载重量明细
            List<Map<String, Object>> loadWeightDetails = this. dao.query("LIRL0308.queryLoadWeightDetails", params);
            result.put("loadWeightDetails", loadWeightDetails);

            // 5. 当月单车平均等待时间
            List<HashMap<String, Object>> avgWaitTime = this.dao.query("LIRL0308.queryAvgWaitTime", params);
            result.put("avgWaitTime", avgWaitTime);

            // 当月单车平均等待时间明细
            List<Map<String, Object>> waitTimeDetails = this. dao.query("LIRL0308.queryWaitTimeDetails", params);
            result.put("waitTimeDetails", waitTimeDetails);

            // 将结果添加到EiInfo中
            inInfo.set("result", result);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);


        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            logger.error(e.getMessage());
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

    /***
     * 重庆原料看板，动态查询库位
     * @serviceId S_LI_RL_0182
     */
    public EiInfo queryLocationCQ(EiInfo inInfo) {
        String segNo = (String) inInfo.get("segNo");
        if (StringUtils.isBlank(segNo)) {
            segNo = "JC000000";
        }

        try {
            // 构建查询参数
            Map<String, Object> queryParams = buildQueryParams(segNo);

            // 查询库位基本信息
            List<HashMap> locationList = queryLocationInfo(queryParams);
            inInfo.set("listL", locationList);

            // 查询库位占用情况
            List<HashMap> occupyList = queryLocationOccupy(queryParams);
            inInfo.set("listK", occupyList);

            // 计算各区域库存统计信息（吨数）
            Map<String, CrossAreaStatistics> areaStatsMap = calculateCrossAreaStatistics(queryParams);

            // 设置各区域统计结果
            setAreaStatisticsResult(inInfo, areaStatsMap);

            // 计算总体统计
            CrossAreaSummary totalSummary = calculateCrossAreaTotalSummary(areaStatsMap);
            inInfo.set("listSum", totalSummary);

            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("查询成功");

        } catch (Exception e) {
            logger.error("重庆原料看板查询库位异常", e);
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("查询失败: " + e.getMessage());
        }

        return inInfo;
    }

    /**
     * 构建查询参数
     */
    private Map<String, Object> buildQueryParams(String segNo) {
        Map<String, Object> params = new HashMap<>();
        params.put("segNo", segNo);
        params.put("status", "10");
        params.put("factoryBuilding", "F1");
        params.put("locationMark", "10");
        return params;
    }

    /**
     * 查询库位基本信息
     */
    private List<HashMap> queryLocationInfo(Map<String, Object> params) {
        return dao.query(LIDS0601.QUERY_LOCATION_CQ, params);
    }

    /**
     * 查询库位占用情况
     */
    private List<HashMap> queryLocationOccupy(Map<String, Object> params) {
        return dao.query(LIDS0901.QUERY_LOCATION_OCCUPY_C_Q, params);
    }

    /**
     * 计算各区域统计信息
     */
    private Map<String, CrossAreaStatistics> calculateCrossAreaStatistics(Map<String, Object> baseParams) {
        Map<String, CrossAreaStatistics> areaStatsMap = new HashMap<>();

        // 查询各区域总容量
        List<HashMap> totalCapacityList = dao.query(LIDS0601.QUERY_LOCATION_OCCUPY_C_Q_SUM, baseParams);

        if (CollectionUtils.isEmpty(totalCapacityList)) {
            return areaStatsMap;
        }

        // 处理每个区域
        for (HashMap capacityInfo : totalCapacityList) {
            String crossArea = org.apache.commons.collections.MapUtils.getString(capacityInfo, "crossArea", "");
            BigDecimal totalCapacity = new BigDecimal(org.apache.commons.collections.MapUtils.getString(capacityInfo, "count", "0"));

            if (StringUtils.isBlank(crossArea) || totalCapacity.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            // 查询当前区域占用情况
            Map<String, Object> areaParams = new HashMap<>(baseParams);
            areaParams.put("crossArea", crossArea);

            List<HashMap> occupyList = dao.query(LIDS0901.QUERY_LOCATION_OCCUPY_C_Q_SUM, areaParams);

            // 计算库存统计（吨数）
            CrossAreaStatistics stats = calculateAreaInventoryStats(crossArea, totalCapacity, occupyList);
            areaStatsMap.put(crossArea, stats);
        }

        return areaStatsMap;
    }

    /**
     * 计算单个区域库存统计（吨数）
     */
    private CrossAreaStatistics calculateAreaInventoryStats(String crossArea, BigDecimal totalCapacity,
                                                                     List<HashMap> occupyList) {
        CrossAreaStatistics stats = new CrossAreaStatistics();
        stats.setCrossArea(crossArea);
        stats.setTotalCapacity(totalCapacity);

        if (CollectionUtils.isNotEmpty(occupyList)) {
            Long occupyCount = org.apache.commons.collections.MapUtils.getLong(occupyList.get(0), "count", 0L);
            BigDecimal currentOccupancy = new BigDecimal(occupyCount.toString());

            stats.setCurrentOccupancy(currentOccupancy);

            // 计算剩余库存（吨数）
            BigDecimal remainingInventory = totalCapacity.subtract(currentOccupancy);
            if (remainingInventory.compareTo(BigDecimal.ZERO) < 0) {
                remainingInventory = BigDecimal.ZERO; // 确保不为负数
            }
            stats.setRemainingInventory(remainingInventory);

            // 计算占用率（百分比，保留两位小数）
            if (totalCapacity.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal occupancyRate = currentOccupancy
                        .multiply(new BigDecimal("100"))
                        .divide(totalCapacity, 2, BigDecimal.ROUND_HALF_UP);
                stats.setOccupancyRate(occupancyRate);
            }
        } else {
            // 如果没有占用，剩余库存等于总容量
            stats.setCurrentOccupancy(BigDecimal.ZERO);
            stats.setRemainingInventory(totalCapacity);
            stats.setOccupancyRate(BigDecimal.ZERO);
        }

        return stats;
    }

    /**
     * 设置各区域统计结果
     */
    private void setAreaStatisticsResult(EiInfo inInfo, Map<String, CrossAreaStatistics> areaStatsMap) {
        for (Map.Entry<String, CrossAreaStatistics> entry : areaStatsMap.entrySet()) {
            String crossArea = entry.getKey();
            CrossAreaStatistics stats = entry.getValue();

            // 创建区域统计结果Map
            Map<String, Object> areaResult = new HashMap<>();
            areaResult.put("totalCapacity", stats.getTotalCapacity());        // 总容量（吨数）
            areaResult.put("currentOccupancy", stats.getCurrentOccupancy());  // 占用库存（吨数）
            areaResult.put("remainingInventory", stats.getRemainingInventory()); // 剩余库存（吨数）
            areaResult.put("occupancyRate", stats.getOccupancyRate());        // 占用率（百分比）

            // 根据区域设置不同的结果字段
            switch (crossArea) {
                case "A":
                    inInfo.set("listA", areaResult);
                    break;
                case "B":
                    inInfo.set("listB", areaResult);
                    break;
                case "C":
                    inInfo.set("listC", areaResult);
                    break;
                case "D":
                    inInfo.set("listD", areaResult);
                    break;
                default:
                    inInfo.set("list" + crossArea, areaResult);
                    break;
            }
        }
    }

    /**
     * 计算跨区域总体统计
     */
    private CrossAreaSummary calculateCrossAreaTotalSummary(Map<String, CrossAreaStatistics> areaStatsMap) {
        CrossAreaSummary summary = new CrossAreaSummary();

        if (areaStatsMap.isEmpty()) {
            return summary;
        }

        BigDecimal totalCapacity = BigDecimal.ZERO;
        BigDecimal totalOccupancy = BigDecimal.ZERO;
        BigDecimal totalRemainingInventory = BigDecimal.ZERO;
        BigDecimal totalOccupancyRate = BigDecimal.ZERO;
        int areaCount = 0;

        for (CrossAreaStatistics stats : areaStatsMap.values()) {
            totalCapacity = totalCapacity.add(stats.getTotalCapacity());
            totalOccupancy = totalOccupancy.add(stats.getCurrentOccupancy());
            totalRemainingInventory = totalRemainingInventory.add(stats.getRemainingInventory());
            totalOccupancyRate = totalOccupancyRate.add(stats.getOccupancyRate());
            areaCount++;
        }

        summary.setTotalCapacity(totalCapacity);
        summary.setTotalOccupancy(totalOccupancy);
        summary.setTotalRemainingInventory(totalRemainingInventory);

        // 计算总体占用率（各区域占用率的平均值）
        if (areaCount > 0) {
            BigDecimal avgOccupancyRate = totalOccupancyRate
                    .divide(new BigDecimal(areaCount), 2, BigDecimal.ROUND_HALF_UP);
            summary.setTotalOccupancyRate(avgOccupancyRate);
        }

        return summary;
    }

    /**
     * 跨区域统计信息类
     */
    private static class CrossAreaStatistics {
        private String crossArea;
        private BigDecimal totalCapacity = BigDecimal.ZERO;
        private BigDecimal currentOccupancy = BigDecimal.ZERO;
        private BigDecimal occupancyRate = BigDecimal.ZERO;
        private BigDecimal remainingInventory = BigDecimal.ZERO; // 新增字段

        // Getter和Setter方法
        public String getCrossArea() { return crossArea; }
        public void setCrossArea(String crossArea) { this.crossArea = crossArea; }

        public BigDecimal getTotalCapacity() { return totalCapacity; }
        public void setTotalCapacity(BigDecimal totalCapacity) { this.totalCapacity = totalCapacity; }

        public BigDecimal getCurrentOccupancy() { return currentOccupancy; }
        public void setCurrentOccupancy(BigDecimal currentOccupancy) { this.currentOccupancy = currentOccupancy; }

        public BigDecimal getOccupancyRate() { return occupancyRate; }
        public void setOccupancyRate(BigDecimal occupancyRate) { this.occupancyRate = occupancyRate; }

        public BigDecimal getRemainingInventory() { return remainingInventory; } // 新增Getter
        public void setRemainingInventory(BigDecimal remainingInventory) { this.remainingInventory = remainingInventory; } // 新增Setter
    }

    /**
     * 跨区域总体统计类
     */
    private static class CrossAreaSummary {
        private BigDecimal totalCapacity = BigDecimal.ZERO;
        private BigDecimal totalOccupancy = BigDecimal.ZERO;
        private BigDecimal totalOccupancyRate = BigDecimal.ZERO;
        private BigDecimal totalRemainingInventory = BigDecimal.ZERO; // 新增字段

        // Getter和Setter方法
        public BigDecimal getTotalCapacity() { return totalCapacity; }
        public void setTotalCapacity(BigDecimal totalCapacity) { this.totalCapacity = totalCapacity; }

        public BigDecimal getTotalOccupancy() { return totalOccupancy; }
        public void setTotalOccupancy(BigDecimal totalOccupancy) { this.totalOccupancy = totalOccupancy; }

        public BigDecimal getTotalOccupancyRate() { return totalOccupancyRate; }
        public void setTotalOccupancyRate(BigDecimal totalOccupancyRate) { this.totalOccupancyRate = totalOccupancyRate; }

        public BigDecimal getTotalRemainingInventory() { return totalRemainingInventory; } // 新增Getter
        public void setTotalRemainingInventory(BigDecimal totalRemainingInventory) { this.totalRemainingInventory = totalRemainingInventory; } // 新增Setter
    }
}
