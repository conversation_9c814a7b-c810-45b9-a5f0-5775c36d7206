<?xml version="1.0" encoding="UTF-8"?>
<!DOC<PERSON><PERSON><PERSON> sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<!--      table information
    Generate time : 2024-12-02 14:00:53
       Version :  1.0
    tableName :meli.tlids0901
     SEG_NO  VARCHAR   NOT NULL,
     UNIT_CODE  VARCHAR   NOT NULL,
     WAREHOUSE_CODE  VARCHAR,
     WAREHOUSE_NAME  VARCHAR,
     AREA_TYPE  VARCHAR,
     AREA_CODE  VARCHAR,
     AREA_NAME  VARCHAR,
     PACK_ID  VARCHAR,
     UNITED_PACK_ID  VARCHAR,
     LABEL_ID  VARCHAR,
     NET_WEIGHT  DECIMAL,
     GRO<PERSON>_WEIGHT  DECIMAL,
     CRANE_OPERATION_WEIGHT  DECIMAL,
     QUANTITY  INTEGER,
     POS_DIR_CODE  VARCHAR,
     CR<PERSON>E_RESULT_ID  VARCHAR,
     ACTION_FLAG  VARCHAR,
     INNER_OUTTER_PLATE_FLAG  VARCHAR,
     STATUS  VARCHAR   NOT NULL,
     X_POSITION  VARCHAR,
     Y_POSITION  VARCHAR,
     Z_POSITION  VARCHAR,
     REC_CREATOR  VARCHAR,
     REC_CREATOR_NAME  VARCHAR,
     REC_CREATE_TIME  VARCHAR,
     REC_REVISOR  VARCHAR,
     REC_REVISOR_NAME  VARCHAR,
     REC_REVISE_TIME  VARCHAR,
     ARCHIVE_FLAG  VARCHAR,
     TENANT_USER  VARCHAR,
     DEL_FLAG  SMALLINT,
     UUID  VARCHAR   NOT NULL   primarykey
-->
<sqlMap namespace="LIDS0901">

    <sql id="condition">
          AND SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="warehouseCode">
            WAREHOUSE_CODE = #warehouseCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="warehouseName">
            WAREHOUSE_NAME LIKE '%$warehouseName$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="imcCrossArea">
            IMC_CROSS_AREA = #imcCrossArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="imcCrossAreaName">
            IMC_CROSS_AREA_NAME = #imcCrossAreaName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="mesCrossArea">
            MES_CROSS_AREA = #mesCrossArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="mesCrossAreaName">
            MES_CROSS_AREA_NAME = #mesCrossAreaName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="posDirCode">
            POS_DIR_CODE = #posDirCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="managementStyle">
            MANAGEMENT_STYLE = #managementStyle#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="actionFlag">
            ACTION_FLAG = #actionFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="ifPlanFlag">
            IF_PLAN_FLAG = #ifPlanFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="useStatus">
            USE_STATUS = #useStatus#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="loadingChannelNo">
            LOADING_CHANNEL_NO = #loadingChannelNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="loadingChannelName">
            LOADING_CHANNEL_NAME = #loadingChannelName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="loadingPointNo">
            LOADING_POINT_NO = #loadingPointNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="loadingPointName">
            LOADING_POINT_NAME = #loadingPointName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="x_initialPoint">
            X_INITIAL_POINT = #x_initialPoint#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="x_destination">
            X_DESTINATION = #x_destination#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="y_initialPoint">
            Y_INITIAL_POINT = #y_initialPoint#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="y_destination">
            Y_DESTINATION = #y_destination#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantUser">
            TENANT_USER = #tenantUser#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.ds.domain.LIDS0901">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        (select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where tt.SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        WAREHOUSE_CODE as "warehouseCode",  <!-- 仓库代码 -->
        WAREHOUSE_NAME as "warehouseName",  <!-- 仓库名称 -->
        AREA_TYPE as "areaType",  <!-- 区域类型 -->
        AREA_CODE as "areaCode",  <!-- 区域代码/库位代码 -->
        AREA_NAME as "areaName",  <!-- 区域名称/库位名称 -->
        PACK_ID as "packId",  <!-- 捆包号 -->
        UNITED_PACK_ID as "unitedPackId",  <!-- 并包号 -->
        LABEL_ID as "labelId",  <!-- 标签号 -->
        NET_WEIGHT as "netWeight",  <!-- 净重 -->
        GROSS_WEIGHT as "grossWeight",  <!-- 毛重 -->
        CRANE_OPERATION_WEIGHT as "craneOperationWeight",  <!-- 吊装重量 -->
        QUANTITY as "quantity",  <!-- 数量 -->
        POS_DIR_CODE as "posDirCode",  <!-- 层数标记 -->
        CRANE_RESULT_ID as "craneResultId",  <!-- 行车实绩单号 -->
        ACTION_FLAG as "actionFlag",  <!-- 板卷标记(0板 1卷) -->
        INNER_OUTTER_PLATE_FLAG as "innerOutterPlateFlag",  <!-- 内外版标记 -->
        STATUS as "status",  <!-- 状态 -->
        X_POSITION as "x_position",  <!-- X轴坐标 -->
        Y_POSITION as "y_position",  <!-- X轴坐标 -->
        Z_POSITION as "z_position",  <!-- Y轴坐标 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid" <!-- ID -->
        FROM meli.tlids0901 t WHERE 1=1
        <include refid="condition"/>
        <isNotEmpty prepend=" AND " property="packId">
            PACK_ID LIKE '%$packId$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitedPackId">
            UNITED_PACK_ID LIKE '%$unitedPackId$%'
        </isNotEmpty>
<!--        <isNotEmpty prepend=" AND " property="labelId">
            LABEL_ID LIKE '%$labelId$%'
        </isNotEmpty>-->
        <isNotEmpty prepend="AND" property="areaType">
            AREA_TYPE = #areaType#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="locationId">
            AREA_CODE = #locationId#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="locationName">
            AREA_NAME = #locationName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="XPosition">
            X_POSITION = #XPosition#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="YPosition">
            Y_POSITION = #YPosition#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="ZPosition">
            Z_POSITION = #ZPosition#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="areaCode">
            AREA_CODE = #areaCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="labelIdList">
            LABEL_ID IN
            <iterate open="(" close=")" conjunction="," property="labelIdList">
                #labelIdList[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="packIdList">
            PACK_ID IN
            <iterate open="(" close=")" conjunction="," property="packIdList">
                #packIdList[]#
            </iterate>
        </isNotEmpty>


        <isNotEmpty prepend=" AND " property="upLeftViewPackId">
            PACK_ID IN (#upLeftViewPackId#
            <isNotEmpty prepend="," property="upRightViewPackId">
                #upRightViewPackId#)
            </isNotEmpty>
            <isEmpty property="upRightViewPackId">
                )
            </isEmpty>
        </isNotEmpty>
        AND (PACK_ID IS NOT NULL AND PACK_ID != '')
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME DESC
            </isEmpty>
        </dynamic>
    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM meli.tlids0901 WHERE 1=1
        <include refid="condition"/>
        <isNotEmpty prepend=" AND " property="packId">
            PACK_ID LIKE '%$packId$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="labelIdList">
            LABEL_ID IN
            <iterate open="(" close=")" conjunction="," property="labelIdList">
                #labelIdList[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="packIdList">
            PACK_ID IN
            <iterate open="(" close=")" conjunction="," property="packIdList">
                #packIdList[]#
            </iterate>
        </isNotEmpty>
    </select>

    <!--根据UUID用来校验数据状态-->
    <select id="count_uuid" resultClass="int">
        SELECT COUNT(*) FROM meli.tlids0901 WHERE 1=1
        <include refid="condition"/>
        AND UUID = #uuid#
    </select>

    <!--
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="warehouseCode">
            WAREHOUSE_CODE = #warehouseCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="warehouseName">
            WAREHOUSE_NAME = #warehouseName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="areaType">
            AREA_TYPE = #areaType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="areaCode">
            AREA_CODE = #areaCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="areaName">
            AREA_NAME = #areaName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="packId">
            PACK_ID = #packId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitedPackId">
            UNITED_PACK_ID = #unitedPackId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="labelId">
            LABEL_ID = #labelId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="netWeight">
            NET_WEIGHT = #netWeight#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="grossWeight">
            GROSS_WEIGHT = #grossWeight#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="craneOperationWeight">
            CRANE_OPERATION_WEIGHT = #craneOperationWeight#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="quantity">
            QUANTITY = #quantity#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="posDirCode">
            POS_DIR_CODE = #posDirCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="craneResultId">
            CRANE_RESULT_ID = #craneResultId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="actionFlag">
            ACTION_FLAG = #actionFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="innerOutterPlateFlag">
            INNER_OUTTER_PLATE_FLAG = #innerOutterPlateFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="x_position">
            X_POSITION = #x_position#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="y_position">
            Y_POSITION = #y_position#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="z_position">
            Z_POSITION = #z_position#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantUser">
            TENANT_USER = #tenantUser#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
    -->

    <insert id="insert">
        INSERT INTO meli.tlids0901 (SEG_NO,  <!-- 系统账套 -->
        UNIT_CODE,  <!-- 业务单元代代码 -->
        WAREHOUSE_CODE,  <!-- 仓库代码 -->
        WAREHOUSE_NAME,  <!-- 仓库名称 -->
        AREA_TYPE,  <!-- 区域类型 -->
        AREA_CODE,  <!-- 区域代码/库位代码 -->
        AREA_NAME,  <!-- 区域名称/库位名称 -->
        PACK_ID,  <!-- 捆包号 -->
        UNITED_PACK_ID,  <!-- 并包号 -->
        LABEL_ID,  <!-- 标签号 -->
        NET_WEIGHT,  <!-- 净重 -->
        GROSS_WEIGHT,  <!-- 毛重 -->
        CRANE_OPERATION_WEIGHT,  <!-- 吊装重量 -->
        QUANTITY,  <!-- 数量 -->
        POS_DIR_CODE,  <!-- 层数标记 -->
        CRANE_RESULT_ID,  <!-- 行车实绩单号 -->
        ACTION_FLAG,  <!-- 板卷标记(0板 1卷) -->
        INNER_OUTTER_PLATE_FLAG,  <!-- 内外版标记 -->
        STATUS,  <!-- 状态 -->
        X_POSITION,  <!-- X轴坐标 -->
        Y_POSITION,  <!-- X轴坐标 -->
        Z_POSITION,  <!-- Y轴坐标 -->
        REC_CREATOR,  <!-- 记录创建人 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时间 -->
        REC_REVISOR,  <!-- 记录修改人 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时间 -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        TENANT_USER,  <!-- 租户 -->
        DEL_FLAG,  <!-- 删除标记 -->
        UUID  <!-- ID -->
        )
        VALUES (#segNo#, #unitCode#, #warehouseCode#, #warehouseName#, #areaType#, #areaCode#, #areaName#, #packId#,
        #unitedPackId#, #labelId#, #netWeight#, #grossWeight#, #craneOperationWeight#, #quantity#, #posDirCode#,
        #craneResultId#, #actionFlag#, #innerOutterPlateFlag#, #status#, #x_position#, #y_position#, #z_position#,
        #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#,
        #tenantUser#, #delFlag#, #uuid#)
    </insert>

    <delete id="delete">
        DELETE FROM meli.tlids0901 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE meli.tlids0901
        SET
        SEG_NO = #segNo#,   <!-- 系统账套 -->
        UNIT_CODE = #unitCode#,   <!-- 业务单元代代码 -->
        WAREHOUSE_CODE = #warehouseCode#,   <!-- 仓库代码 -->
        WAREHOUSE_NAME = #warehouseName#,   <!-- 仓库名称 -->
        AREA_TYPE = #areaType#,   <!-- 区域类型 -->
        AREA_CODE = #areaCode#,   <!-- 区域代码/库位代码 -->
        AREA_NAME = #areaName#,   <!-- 区域名称/库位名称 -->
        PACK_ID = #packId#,   <!-- 捆包号 -->
        UNITED_PACK_ID = #unitedPackId#,   <!-- 并包号 -->
        LABEL_ID = #labelId#,   <!-- 标签号 -->
        NET_WEIGHT = #netWeight#,   <!-- 净重 -->
        GROSS_WEIGHT = #grossWeight#,   <!-- 毛重 -->
        CRANE_OPERATION_WEIGHT = #craneOperationWeight#,   <!-- 吊装重量 -->
        QUANTITY = #quantity#,   <!-- 数量 -->
        POS_DIR_CODE = #posDirCode#,   <!-- 层数标记 -->
        CRANE_RESULT_ID = #craneResultId#,   <!-- 行车实绩单号 -->
        ACTION_FLAG = #actionFlag#,   <!-- 板卷标记(0板 1卷) -->
        INNER_OUTTER_PLATE_FLAG = #innerOutterPlateFlag#,   <!-- 内外版标记 -->
        STATUS = #status#,   <!-- 状态 -->
        X_POSITION = #x_position#,   <!-- X轴坐标 -->
        Y_POSITION = #y_position#,   <!-- X轴坐标 -->
        Z_POSITION = #z_position#,   <!-- Y轴坐标 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建人 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时间 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        TENANT_USER = #tenantUser#,   <!-- 租户 -->
        DEL_FLAG = #delFlag#   <!-- 删除标记 -->
        WHERE 1=1
        AND SEG_NO = #segNo#
        AND PACK_ID = #packId#
        AND UUID = #uuid#
        AND STATUS > '00'
        AND DEL_FLAG = '0'
    </update>

    <update id="updateStatus">
        UPDATE meli.tlids0901
        SET
        STATUS = #status#,   <!-- 状态 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时间 -->
        <isNotEmpty prepend=" , " property="delFlag">
            DEL_FLAG = #delFlag#   <!-- 删除标记 -->
        </isNotEmpty>
        WHERE 1=1
        AND SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="packIds">
             pack_id IN ($packIds$)
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="packId">
             pack_id = #packId#
        </isNotEmpty>
        <isEmpty property="packIds">
            <isEmpty property="packId">
                AND 1 = 0 <!-- 永远不满足的条件 -->
            </isEmpty>
        </isEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        AND STATUS > '00'
        AND DEL_FLAG = '0'
    </update>

    <select id="queryXPosition" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.ds.domain.LIDS0901">
        (SELECT X_POSITION as "x_position",
        PACK_ID as "packId"
        FROM meli.tlids0901
        WHERE X_POSITION &gt; #XPosition#
        AND AREA_CODE = #locationId#
        AND SEG_NO = #segNo#
        AND TRIM(IFNULL(PACK_ID,'')) &lt;> ''
        ORDER BY X_POSITION ASC
        LIMIT 1)
        UNION ALL
        (SELECT X_POSITION as "x_position",
        PACK_ID as "packId"
        FROM meli.tlids0901
        WHERE X_POSITION &lt; #XPosition#
        AND AREA_CODE = #locationId#
        AND SEG_NO = #segNo#
        AND TRIM(IFNULL(PACK_ID,'')) &lt;> ''
        ORDER BY X_POSITION DESC
        LIMIT 1)
    </select>

    <!--    依据行车实绩单号更新库存捆包信息-->
    <update id="updateInventoryPackInfo">
        UPDATE meli.tlids0901
        SET
        REC_REVISOR = #recRevisor#,
        REC_REVISOR_NAME = #recRevisorName#,
        REC_REVISE_TIME = #recReviseTime#
        <isNotEmpty prepend="," property="packId">
            PACK_ID = IF(PACK_ID IS NULL OR PACK_ID = '', #packId#, PACK_ID)   <!-- 捆包号 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="unitedPackId">
            UNITED_PACK_ID = #unitedPackId#   <!-- 并包号 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="labelId">
            LABEL_ID = #labelId#   <!-- 标签号 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="netWeight">
            NET_WEIGHT = #netWeight#   <!-- 净重 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="grossWeight">
            GROSS_WEIGHT = #grossWeight#   <!-- 毛重 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="craneOperationWeight">
            CRANE_OPERATION_WEIGHT = #craneOperationWeight#   <!-- 吊装重量 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="quantity">
            QUANTITY = #quantity#   <!-- 数量 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="actionFlag">
            ACTION_FLAG = #actionFlag#   <!-- 板卷标记(0板 1卷) -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="craneResultId">
            CRANE_RESULT_ID = #craneResultId#   <!-- 行车实绩单号 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="x_position">
            X_POSITION = #x_position#   <!-- X轴坐标 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="y_position">
            Y_POSITION = #y_position#   <!-- Y轴坐标 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="z_position">
            Z_POSITION = #z_position#   <!-- Z轴坐标 -->
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="posDirCode">
            <isEmpty property="maxPosDirCode">
                POS_DIR_CODE = POS_DIR_CODE
            </isEmpty>
            <isNotEmpty property="maxPosDirCode">
                POS_DIR_CODE = POS_DIR_CODE + #maxPosDirCode#
            </isNotEmpty>
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="warehouseCode">
            WAREHOUSE_CODE = IF(WAREHOUSE_CODE IS NULL OR WAREHOUSE_CODE = '', #warehouseCode#, WAREHOUSE_CODE)
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="warehouseName">
            WAREHOUSE_NAME = IF(WAREHOUSE_NAME IS NULL OR WAREHOUSE_NAME = '', #warehouseName#, WAREHOUSE_NAME)
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="areaType">
            AREA_TYPE = #areaType#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="areaCode">
            AREA_CODE = #areaCode#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="areaName">
            AREA_NAME = #areaName#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="delFlag">
            DEL_FLAG = #delFlag#   <!-- 删除标记 -->
        </isNotEmpty>
        WHERE 1=1
        AND SEG_NO = #segNo#
        AND CRANE_RESULT_ID = #craneResultId#
        AND TRIM(CRANE_RESULT_ID) &lt;> ''
        AND CRANE_RESULT_ID IS NOT NULL
        AND STATUS > '00'
        AND DEL_FLAG = '0'
    </update>

    <!--    根据库位附属信息跨层相邻捆包号查询对应捆包信息-->
    <select id="queryRelatedPacksByAdjacentIds" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.ds.domain.LIDS0901">
        SELECT
        ia.SEG_NO as "segNo",  <!-- 系统账套 -->
        ia.UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        ia.WAREHOUSE_CODE as "warehouseCode",  <!-- 仓库代码 -->
        ia.WAREHOUSE_NAME as "warehouseName",  <!-- 仓库名称 -->
        ia.AREA_TYPE as "areaType",  <!-- 区域类型 -->
        ia.AREA_CODE as "areaCode",  <!-- 区域代码/库位代码 -->
        ia.AREA_NAME as "areaName",  <!-- 区域名称/库位名称 -->
        ia.PACK_ID as "packId",  <!-- 捆包号 -->
        ia.UNITED_PACK_ID as "unitedPackId",  <!-- 并包号 -->
        ia.LABEL_ID as "labelId",  <!-- 标签号 -->
        ia.NET_WEIGHT as "netWeight",  <!-- 净重 -->
        ia.GROSS_WEIGHT as "grossWeight",  <!-- 毛重 -->
        ia.CRANE_OPERATION_WEIGHT as "craneOperationWeight",  <!-- 吊装重量 -->
        ia.QUANTITY as "quantity",  <!-- 数量 -->
        ia.POS_DIR_CODE as "posDirCode",  <!-- 层数标记 -->
        ia.CRANE_RESULT_ID as "craneResultId",  <!-- 行车实绩单号 -->
        ia.ACTION_FLAG as "actionFlag",  <!-- 板卷标记(0板 1卷) -->
        ia.INNER_OUTTER_PLATE_FLAG as "innerOutterPlateFlag",  <!-- 内外版标记 -->
        ia.STATUS as "status",  <!-- 状态 -->
        ia.X_POSITION as "x_position",  <!-- X轴坐标 -->
        ia.Y_POSITION as "y_position",  <!-- X轴坐标 -->
        ia.Z_POSITION as "z_position",  <!-- Y轴坐标 -->
        ia.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        ia.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        ia.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        ia.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        ia.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        ia.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ia.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        ia.TENANT_USER as "tenantUser",  <!-- 租户 -->
        ia.DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        ia.UUID as "uuid" <!-- ID -->
        FROM MELI.tlids0901 ia
        WHERE ia.SEG_NO = #segNo#
        AND ia.WAREHOUSE_CODE = #warehouseCode#
        AND ia.AREA_CODE = #areaCode#
        AND ia.X_POSITION = (
        <!-- 找到大于 im.X_POSITION 的上层最相邻捆包记录-->
        SELECT MIN(ib.X_POSITION)
        FROM MELI.tlids0901 ib
        WHERE ib.SEG_NO = ia.SEG_NO
        AND ib.WAREHOUSE_CODE = ia.WAREHOUSE_CODE
        AND ib.AREA_CODE = ia.AREA_CODE
        AND ib.X_POSITION > (SELECT X_POSITION
        FROM MELI.tlids0901 ic
        WHERE ic.SEG_NO = ib.SEG_NO
        AND ic.WAREHOUSE_CODE = ib.WAREHOUSE_CODE
        AND ic.AREA_CODE = ib.AREA_CODE
        AND ic.PACK_ID = #packId#
        AND ic.POS_DIR_CODE &lt; ib.POS_DIR_CODE))
        AND NOT EXISTS(SELECT 1
        FROM MELI.tlids0901 t
        WHERE t.SEG_NO = ia.SEG_NO
        AND t.WAREHOUSE_CODE = ia.WAREHOUSE_CODE
        AND t.AREA_CODE = ia.AREA_CODE
        AND t.X_POSITION > #XPosition#
        AND t.X_POSITION &lt; ia.X_POSITION)
        AND ia.STATUS = '10'
        AND ia.DEL_FLAG = '0'

        UNION

        SELECT
        ia.SEG_NO as "segNo",  <!-- 系统账套 -->
        ia.UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        ia.WAREHOUSE_CODE as "warehouseCode",  <!-- 仓库代码 -->
        ia.WAREHOUSE_NAME as "warehouseName",  <!-- 仓库名称 -->
        ia.AREA_TYPE as "areaType",  <!-- 区域类型 -->
        ia.AREA_CODE as "areaCode",  <!-- 区域代码/库位代码 -->
        ia.AREA_NAME as "areaName",  <!-- 区域名称/库位名称 -->
        ia.PACK_ID as "packId",  <!-- 捆包号 -->
        ia.UNITED_PACK_ID as "unitedPackId",  <!-- 并包号 -->
        ia.LABEL_ID as "labelId",  <!-- 标签号 -->
        ia.NET_WEIGHT as "netWeight",  <!-- 净重 -->
        ia.GROSS_WEIGHT as "grossWeight",  <!-- 毛重 -->
        ia.CRANE_OPERATION_WEIGHT as "craneOperationWeight",  <!-- 吊装重量 -->
        ia.QUANTITY as "quantity",  <!-- 数量 -->
        ia.POS_DIR_CODE as "posDirCode",  <!-- 层数标记 -->
        ia.CRANE_RESULT_ID as "craneResultId",  <!-- 行车实绩单号 -->
        ia.ACTION_FLAG as "actionFlag",  <!-- 板卷标记(0板 1卷) -->
        ia.INNER_OUTTER_PLATE_FLAG as "innerOutterPlateFlag",  <!-- 内外版标记 -->
        ia.STATUS as "status",  <!-- 状态 -->
        ia.X_POSITION as "x_position",  <!-- X轴坐标 -->
        ia.Y_POSITION as "y_position",  <!-- X轴坐标 -->
        ia.Z_POSITION as "z_position",  <!-- Y轴坐标 -->
        ia.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        ia.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        ia.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        ia.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        ia.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        ia.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ia.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        ia.TENANT_USER as "tenantUser",  <!-- 租户 -->
        ia.DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        ia.UUID as "uuid" <!-- ID -->
        FROM MELI.tlids0901 ia
        WHERE ia.SEG_NO = #segNo#
        AND ia.WAREHOUSE_CODE = #warehouseCode#
        AND ia.AREA_CODE = #areaCode#
        AND ia.X_POSITION = (
        <!-- 找到小于 im.X_POSITION 的上层最相邻捆包记录-->
        SELECT MAX(ib.X_POSITION)
        FROM MELI.tlids0901 ib
        WHERE ib.SEG_NO = ia.SEG_NO
        AND ib.WAREHOUSE_CODE = ia.WAREHOUSE_CODE
        AND ib.AREA_CODE = ia.AREA_CODE
        AND ib.X_POSITION &lt; (SELECT X_POSITION
        FROM MELI.tlids0901 ic
        WHERE ic.SEG_NO = ib.SEG_NO
        AND ic.WAREHOUSE_CODE = ib.WAREHOUSE_CODE
        AND ic.AREA_CODE = ib.AREA_CODE
        AND ic.PACK_ID = #packId#
        AND ic.POS_DIR_CODE &lt; ib.POS_DIR_CODE))
        AND NOT EXISTS(SELECT 1
        FROM MELI.tlids0901 t
        WHERE t.SEG_NO = ia.SEG_NO
        AND t.WAREHOUSE_CODE = ia.WAREHOUSE_CODE
        AND t.AREA_CODE = ia.AREA_CODE
        AND t.X_POSITION &lt; #XPosition#
        AND t.X_POSITION > ia.X_POSITION)
        AND ia.STATUS = '10'
        AND ia.DEL_FLAG = '0'
    </select>

    <!--    根据XY坐标分组查询所有捆包-->
    <select id="queryPacksByXyCoordinates" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.ds.domain.LIDS0901">
        SELECT *
        FROM meli.tlids0901 im
        WHERE SEG_NO = #segNo#
        AND im.WAREHOUSE_CODE = #warehouseCode#
        AND im.AREA_CODE = #locationId#
        AND im.X_POSITION - 50 &lt;= #XPosition#
        AND im.X_POSITION + 50 &gt;= #XPosition#
        AND im.Y_POSITION - 50 &lt;= #YPosition#
        AND im.Y_POSITION + 50 &gt;= #YPosition#
        AND EXISTS(SELECT 1
        FROM meli.tlids0901 id
        WHERE id.SEG_NO = im.SEG_NO
        AND id.PACK_ID = #packId#
        AND id.WAREHOUSE_CODE = im.WAREHOUSE_CODE
        AND id.AREA_CODE = im.AREA_CODE
        AND im.POS_DIR_CODE > id.POS_DIR_CODE
        AND id.STATUS = '10'
        AND id.DEL_FLAG = 0)
        AND im.STATUS = '10'
        AND im.DEL_FLAG = 0
    </select>

    <!--    根据XY坐标,区域类型，区域代码查询捆包库存-->
    <select id="queryPacksByArea" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.ds.domain.LIDS0901">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        WAREHOUSE_CODE as "warehouseCode",  <!-- 仓库代码 -->
        WAREHOUSE_NAME as "warehouseName",  <!-- 仓库名称 -->
        AREA_TYPE as "areaType",  <!-- 区域类型 -->
        AREA_CODE as "areaCode",  <!-- 区域代码/库位代码 -->
        AREA_NAME as "areaName",  <!-- 区域名称/库位名称 -->
        PACK_ID as "packId",  <!-- 捆包号 -->
        UNITED_PACK_ID as "unitedPackId",  <!-- 并包号 -->
        LABEL_ID as "labelId",  <!-- 标签号 -->
        NET_WEIGHT as "netWeight",  <!-- 净重 -->
        GROSS_WEIGHT as "grossWeight",  <!-- 毛重 -->
        CRANE_OPERATION_WEIGHT as "craneOperationWeight",  <!-- 吊装重量 -->
        QUANTITY as "quantity",  <!-- 数量 -->
        POS_DIR_CODE as "posDirCode",  <!-- 层数标记 -->
        CRANE_RESULT_ID as "craneResultId",  <!-- 行车实绩单号 -->
        ACTION_FLAG as "actionFlag",  <!-- 板卷标记(0板 1卷) -->
        INNER_OUTTER_PLATE_FLAG as "innerOutterPlateFlag",  <!-- 内外版标记 -->
        STATUS as "status",  <!-- 状态 -->
        X_POSITION as "x_position",  <!-- X轴坐标 -->
        Y_POSITION as "y_position",  <!-- X轴坐标 -->
        Z_POSITION as "z_position",  <!-- Y轴坐标 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid" <!-- ID -->
        FROM meli.tlids0901 t
        WHERE SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="warehouseCode">
            t.WAREHOUSE_CODE = #warehouseCode#
        </isNotEmpty>
        AND t.AREA_TYPE = #areaType#
        AND t.AREA_CODE = #areaCode#
        <!--区域为过跨通道时，XYZ坐标位置可能不准确-->
        <isNotEqual property="areaType" compareValue="20">
            AND t.X_POSITION - 50 &lt;= #XPosition#
            AND t.X_POSITION + 50 &gt;= #XPosition#
            AND t.Y_POSITION - 50 &lt;= #YPosition#
            AND t.Y_POSITION + 50 &gt;= #YPosition#
            <!--Z轴减5%<UWB抓取捆包的Z轴<捆包MES库存的Z轴加5%(只有捆包板卷标记为卷时)-->
            <isNotEmpty prepend="AND" property="ZPosition">
                IF(t.ACTION_FLAG = '1', t.Z_POSITION - 50 &lt;= #ZPosition#
                AND t.Z_POSITION + 50 >= #ZPosition#, 1 = 1)
            </isNotEmpty>
        </isNotEqual>
        AND t.STATUS = '10'
        AND t.DEL_FLAG = 0
        ORDER BY t.POS_DIR_CODE DESC<!--层数标记倒序-->
    </select>

    <!--根据行车实绩单号查询对应所有捆包-->
   <select id="queryPacksByResultId" parameterClass="java.util.HashMap"
           resultClass="com.baosight.imom.li.ds.domain.LIDS0901">
       SELECT
       SEG_NO as "segNo",  <!-- 系统账套 -->
       (select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where tt.SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
       "segName", <!-- 业务单元简称 -->
       UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
       WAREHOUSE_CODE as "warehouseCode",  <!-- 仓库代码 -->
       WAREHOUSE_NAME as "warehouseName",  <!-- 仓库名称 -->
       AREA_TYPE as "areaType",  <!-- 区域类型 -->
       AREA_CODE as "areaCode",  <!-- 区域代码/库位代码 -->
       AREA_NAME as "areaName",  <!-- 区域名称/库位名称 -->
       PACK_ID as "packId",  <!-- 捆包号 -->
       UNITED_PACK_ID as "unitedPackId",  <!-- 并包号 -->
       LABEL_ID as "labelId",  <!-- 标签号 -->
       NET_WEIGHT as "netWeight",  <!-- 净重 -->
       GROSS_WEIGHT as "grossWeight",  <!-- 毛重 -->
       CRANE_OPERATION_WEIGHT as "craneOperationWeight",  <!-- 吊装重量 -->
       QUANTITY as "quantity",  <!-- 数量 -->
       POS_DIR_CODE as "posDirCode",  <!-- 层数标记 -->
       CRANE_RESULT_ID as "craneResultId",  <!-- 行车实绩单号 -->
       ACTION_FLAG as "actionFlag",  <!-- 板卷标记(0板 1卷) -->
       INNER_OUTTER_PLATE_FLAG as "innerOutterPlateFlag",  <!-- 内外版标记 -->
       STATUS as "status",  <!-- 状态 -->
       X_POSITION as "x_position",  <!-- X轴坐标 -->
       Y_POSITION as "y_position",  <!-- X轴坐标 -->
       Z_POSITION as "z_position",  <!-- Y轴坐标 -->
       REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
       REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
       REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
       REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
       REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
       REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
       ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
       TENANT_USER as "tenantUser",  <!-- 租户 -->
       DEL_FLAG as "delFlag",  <!-- 删除标记 -->
       UUID as "uuid" <!-- ID -->
       FROM meli.tlids0901 t WHERE 1=1
       AND t.SEG_NO = #segNo#
       AND t.CRANE_RESULT_ID = #craneResultId#
       AND t.STATUS > '00'
       AND t.DEL_FLAG = 0
   </select>

    <!--根据区域确认当前位置最大的层数标记(TODO 下料区?)-->
    <select id="queryMaxPosDirCodeArea" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.ds.domain.LIDS0901">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        (select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where tt.SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        WAREHOUSE_CODE as "warehouseCode",  <!-- 仓库代码 -->
        WAREHOUSE_NAME as "warehouseName",  <!-- 仓库名称 -->
        AREA_TYPE as "areaType",  <!-- 区域类型 -->
        AREA_CODE as "areaCode",  <!-- 区域代码/库位代码 -->
        AREA_NAME as "areaName",  <!-- 区域名称/库位名称 -->
        PACK_ID as "packId",  <!-- 捆包号 -->
        UNITED_PACK_ID as "unitedPackId",  <!-- 并包号 -->
        LABEL_ID as "labelId",  <!-- 标签号 -->
        NET_WEIGHT as "netWeight",  <!-- 净重 -->
        GROSS_WEIGHT as "grossWeight",  <!-- 毛重 -->
        CRANE_OPERATION_WEIGHT as "craneOperationWeight",  <!-- 吊装重量 -->
        QUANTITY as "quantity",  <!-- 数量 -->
        POS_DIR_CODE as "posDirCode",  <!-- 层数标记 -->
        CRANE_RESULT_ID as "craneResultId",  <!-- 行车实绩单号 -->
        ACTION_FLAG as "actionFlag",  <!-- 板卷标记(0板 1卷) -->
        INNER_OUTTER_PLATE_FLAG as "innerOutterPlateFlag",  <!-- 内外版标记 -->
        STATUS as "status",  <!-- 状态 -->
        X_POSITION as "x_position",  <!-- X轴坐标 -->
        Y_POSITION as "y_position",  <!-- X轴坐标 -->
        Z_POSITION as "z_position",  <!-- Y轴坐标 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid" <!-- ID -->
        FROM meli.tlids0901 t WHERE 1=1
        AND t.SEG_NO = #segNo#
        AND t.AREA_TYPE = #areaType#  <!-- 区域类型 -->
        AND t.AREA_CODE = #areaCode#  <!-- 区域代码/库位代码 -->
        AND t.X_POSITION - 50 &lt;= #XPosition#<!--X坐标值-->
        AND t.X_POSITION + 50 &gt;= #XPosition#<!--X坐标值-->
        AND t.Y_POSITION - 50 &lt;= #YPosition#<!--Y坐标值-->
        AND t.Y_POSITION + 50 &gt;= #YPosition#<!--Y坐标值-->
        AND t.STATUS > '00'
        AND t.DEL_FLAG = 0
        ORDER BY t.POS_DIR_CODE DESC
    </select>

    <select id="queryPackMessage" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.ds.domain.LIDS0901">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        (select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where tt.SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        WAREHOUSE_CODE as "warehouseCode",  <!-- 仓库代码 -->
        WAREHOUSE_NAME as "warehouseName",  <!-- 仓库名称 -->
        AREA_TYPE as "areaType",  <!-- 区域类型 -->
        AREA_CODE as "areaCode",  <!-- 区域代码/库位代码 -->
        AREA_NAME as "areaName",  <!-- 区域名称/库位名称 -->
        PACK_ID as "packId",  <!-- 捆包号 -->
        UNITED_PACK_ID as "unitedPackId",  <!-- 并包号 -->
        LABEL_ID as "labelId",  <!-- 标签号 -->
        NET_WEIGHT as "netWeight",  <!-- 净重 -->
        GROSS_WEIGHT as "grossWeight",  <!-- 毛重 -->
        CRANE_OPERATION_WEIGHT as "craneOperationWeight",  <!-- 吊装重量 -->
        QUANTITY as "quantity",  <!-- 数量 -->
        POS_DIR_CODE as "posDirCode",  <!-- 层数标记 -->
        CRANE_RESULT_ID as "craneResultId",  <!-- 行车实绩单号 -->
        ACTION_FLAG as "actionFlag",  <!-- 板卷标记(0板 1卷) -->
        INNER_OUTTER_PLATE_FLAG as "innerOutterPlateFlag",  <!-- 内外版标记 -->
        STATUS as "status",  <!-- 状态 -->
        X_POSITION as "x_position",  <!-- X轴坐标 -->
        Y_POSITION as "y_position",  <!-- X轴坐标 -->
        Z_POSITION as "z_position",  <!-- Y轴坐标 -->
        ORIGINAL_PACK_ID as "originalPackId",<!--原始捆包号-->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid" <!-- ID -->
        FROM meli.tlids0901 t WHERE 1=1
        <include refid="condition"/>
        <isNotEmpty prepend=" AND " property="packId">
            PACK_ID = #packId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="labelId">
            LABEL_ID = #labelId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="XPosition">
            X_POSITION = #XPosition#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="YPosition">
            Y_POSITION = #YPosition#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="ZPosition">
            Z_POSITION = #ZPosition#
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME DESC
            </isEmpty>
        </dynamic>
    </select>

    <update id="updateUnitedPackId">
        UPDATE meli.tlids0901
        SET
        UNITED_PACK_ID = #unitedPackId#,   <!-- 并包号 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#  <!-- 记录修改时间 -->
        WHERE 1=1
        AND SEG_NO = #segNo#
        AND PACK_ID = #packId#
        AND STATUS > '00'
        AND DEL_FLAG = '0'
    </update>

    <!--    PDA行车实绩补录，依据已有捆包号修改未有捆包号库存位置-->
    <update id="updatePackInfoIsInventoryPosition">
        UPDATE meli.tlids0901 t
        JOIN (SELECT SEG_NO AS "segNo",
        PACK_ID AS "packId",
        AREA_TYPE AS "areaType",
        AREA_CODE AS "areaCode",
        AREA_NAME AS "areaName",
        X_POSITION AS "xPosition",
        Y_POSITION AS "yPosition",
        Z_POSITION AS "zPosition",
        CRANE_RESULT_ID AS "craneResultId"
        FROM meli.tlids0901
        WHERE SEG_NO = #segNo#
        AND CRANE_RESULT_ID = #craneResultId#
        AND PACK_ID = #packId#) packInfo
        ON t.SEG_NO = packInfo.segNo
        SET t.AREA_TYPE = packInfo.areaType,
        t.AREA_CODE = packInfo.areaCode,
        t.AREA_NAME = packInfo.areaName,
        t.CRANE_RESULT_ID = packInfo.craneResultId,
        t.X_POSITION = packInfo.xPosition,
        t.Y_POSITION = packInfo.yPosition,
        t.Z_POSITION = packInfo.zPosition
        WHERE t.SEG_NO = #segNo#
        AND t.PACK_ID IN ($packIds$)
        AND t.DEL_FLAG = '0'
    </update>
    <!-- PDA找料根据库位查询库位下多层捆包位置 -->
    <select id="queryMatByLoaction" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        (select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where tt.SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        WAREHOUSE_CODE as "warehouseCode",  <!-- 仓库代码 -->
        WAREHOUSE_NAME as "warehouseName",  <!-- 仓库名称 -->
        AREA_TYPE as "areaType",  <!-- 区域类型 -->
        AREA_CODE as "areaCode",  <!-- 区域代码/库位代码 -->
        AREA_NAME as "areaName",  <!-- 区域名称/库位名称 -->
        PACK_ID as "packId",  <!-- 捆包号 -->
        UNITED_PACK_ID as "unitedPackId",  <!-- 并包号 -->
        LABEL_ID as "labelId",  <!-- 标签号 -->
        NET_WEIGHT as "netWeight",  <!-- 净重 -->
        GROSS_WEIGHT as "grossWeight",  <!-- 毛重 -->
        CRANE_OPERATION_WEIGHT as "craneOperationWeight",  <!-- 吊装重量 -->
        QUANTITY as "quantity",  <!-- 数量 -->
        POS_DIR_CODE as "posDirCode",  <!-- 层数标记 -->
        CRANE_RESULT_ID as "craneResultId",  <!-- 行车实绩单号 -->
        ACTION_FLAG as "actionFlag",  <!-- 板卷标记(0板 1卷) -->
        INNER_OUTTER_PLATE_FLAG as "innerOutterPlateFlag",  <!-- 内外版标记 -->
        STATUS as "status",  <!-- 状态 -->
        X_POSITION as "x_position",  <!-- X轴坐标 -->
        Y_POSITION as "y_position",  <!-- X轴坐标 -->
        Z_POSITION as "z_position",  <!-- Y轴坐标 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid" <!-- ID -->
        FROM meli.tlids0901 t WHERE 1=1
        and t.SEG_NO = #segNo#
        AND t.AREA_CODE = #areaCode#
        and t.DEL_FLAG = 0
        and t.status = '10'
        order by t.POS_DIR_CODE asc
        <dynamic prepend="  ">
            <isEqual property="positionSeq" compareValue="X">
                ,t.X_POSITION asc
            </isEqual>
            <isEqual property="positionSeq" compareValue="Y">
                ,t.Y_POSITION asc
            </isEqual>
        </dynamic>

    </select>


    <!--    更新库存捆包区域信息-->
    <update id="updateAreaByPackId">
        UPDATE meli.tlids0901
        SET
        AREA_TYPE = #areaType#,   <!-- 区域类型 -->
        AREA_CODE = #areaCode#,   <!-- 区域代码 -->
        AREA_NAME = #areaName#,   <!-- 区域名称 -->
        X_POSITION = #xPosition#,   <!-- X轴坐标 -->
        Y_POSITION = #yPosition#,   <!-- Y轴坐标 -->
        Z_POSITION = #zPosition#,   <!-- Z轴坐标 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#  <!-- 记录修改时间 -->
        <isNotEmpty prepend=" , " property="tenantUser">
            TENANT_USER = #tenantUser#
        </isNotEmpty>
        WHERE SEG_NO = #segNo#
        AND PACK_ID = #packId#
        AND STATUS > '00'
        AND DEL_FLAG = '0'
    </update>

    <select id="queryLocationOccupyCQ" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT POS_DIR_CODE as "posDirCode",MAX(PACK_ID) AS "packId",max(AREA_CODE) as "areaCode"
        FROM meli.tlids0901
        where 1 = 1
        and SEG_NO = #segNo#
        AND STATUS != '00'
        AND AREA_CODE IN (select LOCATION_NAME
        from meli.tlids0601 tlids0601
        where 1 = 1
        and tlids0601.SEG_NO = #segNo#
        AND STATUS = '10'
        AND FACTORY_BUILDING = 'F1'
        AND LOCATION_MARK = '10')
        AND PACK_ID !=' '
        AND POS_DIR_CODE !=' '
        GROUP BY POS_DIR_CODE ,PACK_ID
    </select>


    <!--   上料回退，修改状态为出库的实物库存 -->
    <update id="rollbackMaterialLoading">
        UPDATE meli.tlids0901
        SET
        AREA_TYPE = #areaType#,   <!-- 区域类型 -->
        AREA_CODE = #areaCode#,   <!-- 区域代码 -->
        AREA_NAME = #areaName#,   <!-- 区域名称 -->
        X_POSITION = #xPosition#,   <!-- X轴坐标 -->
        Y_POSITION = #yPosition#,   <!-- Y轴坐标 -->
        Z_POSITION = #zPosition#,   <!-- Z轴坐标 -->
        STATUS = #status#,   <!-- 状态 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        DEL_FLAG = #delFlag#   <!-- 删除标记 -->
        WHERE 1=1
        AND SEG_NO = #segNo#
        AND PACK_ID = #packId#
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        AND DEL_FLAG = '0'
    </update>


    <select id="queryLocationOccupyCQSum" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT FLOOR(COUNT(PACK_ID) * 10) as "count"
        FROM meli.tlids0901 t1
        WHERE 1 = 1
        AND t1.SEG_NO = #segNo#
        AND t1.STATUS != '00'
        AND EXISTS (
        SELECT 1
        FROM meli.tlids0601 t2
        WHERE t2.LOCATION_NAME = t1.AREA_CODE
        AND t2.SEG_NO = #segNo#
        AND t2.STATUS = '10'
        AND t2.FACTORY_BUILDING = #factoryBuilding#
        AND t2.LOCATION_MARK = '10'
        AND t2.CROSS_AREA = #crossArea#
        )
        AND t1.PACK_ID != ' '
        AND t1.POS_DIR_CODE != ' '
    </select>
</sqlMap>