/**
* Generate time : 2025-07-04 15:31:29
* Version : 1.0
*/
package com.baosight.imom.common.li.domain;
import com.baosight.iplat4j.core.util.NumberUtils;
import java.math.BigDecimal;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* Tlirl0598
* 
*/
public class Tlirl0598 extends DaoEPBase {

    private String uuid = " ";		/* 唯一标识*/
    private String measureId = " ";		/* 测量ID*/
    private String purOrderNum = " ";		/* 采购订单号*/
    private String archiveFlag = " ";		/* 归档标志*/
    private String recReviseTime = " ";		/* 记录修改时间*/
    private String returnStatus = " ";		/* 返回状态*/
    private String ladingBillId = " ";		/* 提单ID*/
    private String unitedFlag = " ";		/* 联合标志*/
    private BigDecimal settleQty = new BigDecimal("0");		/* 结算数量*/
    private String tenantUser = " ";		/* 租户用户*/
    private String labelId = " ";		/* 标签ID*/
    private String custPartId = " ";		/* 客户零件ID*/
    private String recCreatorName = " ";		/* 记录创建者姓名*/
    private String unitCode = " ";		/* 单位代码*/
    private String heatNum = " ";		/* 热号*/
    private String purContractNum = " ";		/* 采购合同号*/
    private String scrapType = " ";		/* 废料类型*/
    private BigDecimal thickTbthDim = new BigDecimal("0");		/* 厚度尺寸(mm)*/
    private String recRevisorName = " ";		/* 记录修改者姓名*/
    private BigDecimal packAmount = new BigDecimal("0");		/* 包装金额*/
    private String packId = " ";		/* 包装ID*/
    private BigDecimal netWeight = new BigDecimal("0");		/* 净重(kg)*/
    private String specsDesc = " ";		/* 规格描述*/
    private String recCreator = " ";		/* 记录创建者*/
    private String factoryOrderNum = " ";		/* 工厂订单号*/
    private String matInnerId = " ";		/* 物料内部ID*/
    private String prodTypeDesc = " ";		/* 产品类型描述*/
    private String prodCname = " ";		/* 产品中文名*/
    private String packType = " ";		/* 包装类型*/
    private String orderNum = " ";		/* 订单号*/
    private String ladingBillSubid = " ";		/* 提单子ID*/
    private String shopsign = " ";		/* 牌号*/
    private String delFlag = " ";		/* 删除标志(0-未删除)*/
    private String prodTypeId = " ";		/* 产品类型ID*/
    private String warehouseCode = " ";		/* 仓库代码*/
    private String m_packId = " ";		/* 主包装ID*/
    private BigDecimal pieceNum = new BigDecimal("0");		/* 件数*/
    private String locationId = " ";		/* 位置ID*/
    private String recCreateTime;	/* 记录创建时间*/
    private String producingArea = " ";		/* 生产区域*/
    private BigDecimal arrearsAmount = new BigDecimal("0");		/* 欠款金额*/
    private BigDecimal length = new BigDecimal("0");		/* 长度(mm)*/
    private String userBillId = " ";		/* 用户账单ID*/
    private String qualityGrade = " ";		/* 质量等级*/
    private String prodCode = " ";		/* 产品代码*/
    private String unitedPackId = " ";		/* 联合包装ID*/
    private String custPartName = " ";		/* 客户零件名称*/
    private BigDecimal grossWeight = new BigDecimal("0");		/* 毛重(kg)*/
    private String segNo = " ";		/* 账套*/
    private BigDecimal width = new BigDecimal("0");		/* 宽度(mm)*/

    /**
    * initialize the metadata
    */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("唯一标识");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("measureId");
        eiColumn.setDescName("测量ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("purOrderNum");
        eiColumn.setDescName("采购订单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标志");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("returnStatus");
        eiColumn.setDescName("返回状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ladingBillId");
        eiColumn.setDescName("提单ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitedFlag");
        eiColumn.setDescName("联合标志");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("settleQty");
        eiColumn.setType("N");
        eiColumn.setScaleLength(3);
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("结算数量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantUser");
        eiColumn.setDescName("租户用户");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("labelId");
        eiColumn.setDescName("标签ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("custPartId");
        eiColumn.setDescName("客户零件ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建者姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("单位代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("heatNum");
        eiColumn.setDescName("热号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("purContractNum");
        eiColumn.setDescName("采购合同号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("scrapType");
        eiColumn.setDescName("废料类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("thickTbthDim");
        eiColumn.setType("N");
        eiColumn.setScaleLength(2);
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("厚度尺寸(mm)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改者姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packAmount");
        eiColumn.setType("N");
        eiColumn.setScaleLength(2);
        eiColumn.setFieldLength(12);
        eiColumn.setDescName("包装金额");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packId");
        eiColumn.setDescName("包装ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("netWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(3);
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("净重(kg)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("specsDesc");
        eiColumn.setDescName("规格描述");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryOrderNum");
        eiColumn.setDescName("工厂订单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("matInnerId");
        eiColumn.setDescName("物料内部ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodTypeDesc");
        eiColumn.setDescName("产品类型描述");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodCname");
        eiColumn.setDescName("产品中文名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packType");
        eiColumn.setDescName("包装类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("orderNum");
        eiColumn.setDescName("订单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ladingBillSubid");
        eiColumn.setDescName("提单子ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("shopsign");
        eiColumn.setDescName("牌号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标志(0-未删除)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodTypeId");
        eiColumn.setDescName("产品类型ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("warehouseCode");
        eiColumn.setDescName("仓库代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("m_packId");
        eiColumn.setDescName("主包装ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("pieceNum");
        eiColumn.setType("N");
        eiColumn.setScaleLength(3);
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("件数");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("locationId");
        eiColumn.setDescName("位置ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("producingArea");
        eiColumn.setDescName("生产区域");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("arrearsAmount");
        eiColumn.setType("N");
        eiColumn.setScaleLength(2);
        eiColumn.setFieldLength(12);
        eiColumn.setDescName("欠款金额");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("length");
        eiColumn.setType("N");
        eiColumn.setScaleLength(3);
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("长度(mm)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("userBillId");
        eiColumn.setDescName("用户账单ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("qualityGrade");
        eiColumn.setDescName("质量等级");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodCode");
        eiColumn.setDescName("产品代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitedPackId");
        eiColumn.setDescName("联合包装ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("custPartName");
        eiColumn.setDescName("客户零件名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("grossWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(3);
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("毛重(kg)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("width");
        eiColumn.setType("N");
        eiColumn.setScaleLength(3);
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("宽度(mm)");
        eiMetadata.addMeta(eiColumn);


    }

    /**
    * the constructor
    */
    public Tlirl0598() {
        initMetaData();
    }

    /**
    * get the uuid - 唯一标识
    * @return the uuid
    */
    public String getUuid() {
        return this.uuid;
    }

    /**
    * set the uuid - 唯一标识
    */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }
    /**
    * get the measureId - 测量ID
    * @return the measureId
    */
    public String getMeasureId() {
        return this.measureId;
    }

    /**
    * set the measureId - 测量ID
    */
    public void setMeasureId(String measureId) {
        this.measureId = measureId;
    }
    /**
    * get the purOrderNum - 采购订单号
    * @return the purOrderNum
    */
    public String getPurOrderNum() {
        return this.purOrderNum;
    }

    /**
    * set the purOrderNum - 采购订单号
    */
    public void setPurOrderNum(String purOrderNum) {
        this.purOrderNum = purOrderNum;
    }
    /**
    * get the archiveFlag - 归档标志
    * @return the archiveFlag
    */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
    * set the archiveFlag - 归档标志
    */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }
    /**
    * get the recReviseTime - 记录修改时间
    * @return the recReviseTime
    */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
    * set the recReviseTime - 记录修改时间
    */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }
    /**
    * get the returnStatus - 返回状态
    * @return the returnStatus
    */
    public String getReturnStatus() {
        return this.returnStatus;
    }

    /**
    * set the returnStatus - 返回状态
    */
    public void setReturnStatus(String returnStatus) {
        this.returnStatus = returnStatus;
    }
    /**
    * get the ladingBillId - 提单ID
    * @return the ladingBillId
    */
    public String getLadingBillId() {
        return this.ladingBillId;
    }

    /**
    * set the ladingBillId - 提单ID
    */
    public void setLadingBillId(String ladingBillId) {
        this.ladingBillId = ladingBillId;
    }
    /**
    * get the unitedFlag - 联合标志
    * @return the unitedFlag
    */
    public String getUnitedFlag() {
        return this.unitedFlag;
    }

    /**
    * set the unitedFlag - 联合标志
    */
    public void setUnitedFlag(String unitedFlag) {
        this.unitedFlag = unitedFlag;
    }
    /**
    * get the settleQty - 结算数量
    * @return the settleQty
    */
    public BigDecimal getSettleQty() {
        return this.settleQty;
    }

    /**
    * set the settleQty - 结算数量
    */
    public void setSettleQty(BigDecimal settleQty) {
        this.settleQty = settleQty;
    }
    /**
    * get the tenantUser - 租户用户
    * @return the tenantUser
    */
    public String getTenantUser() {
        return this.tenantUser;
    }

    /**
    * set the tenantUser - 租户用户
    */
    public void setTenantUser(String tenantUser) {
        this.tenantUser = tenantUser;
    }
    /**
    * get the labelId - 标签ID
    * @return the labelId
    */
    public String getLabelId() {
        return this.labelId;
    }

    /**
    * set the labelId - 标签ID
    */
    public void setLabelId(String labelId) {
        this.labelId = labelId;
    }
    /**
    * get the custPartId - 客户零件ID
    * @return the custPartId
    */
    public String getCustPartId() {
        return this.custPartId;
    }

    /**
    * set the custPartId - 客户零件ID
    */
    public void setCustPartId(String custPartId) {
        this.custPartId = custPartId;
    }
    /**
    * get the recCreatorName - 记录创建者姓名
    * @return the recCreatorName
    */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
    * set the recCreatorName - 记录创建者姓名
    */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }
    /**
    * get the unitCode - 单位代码
    * @return the unitCode
    */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
    * set the unitCode - 单位代码
    */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }
    /**
    * get the heatNum - 热号
    * @return the heatNum
    */
    public String getHeatNum() {
        return this.heatNum;
    }

    /**
    * set the heatNum - 热号
    */
    public void setHeatNum(String heatNum) {
        this.heatNum = heatNum;
    }
    /**
    * get the purContractNum - 采购合同号
    * @return the purContractNum
    */
    public String getPurContractNum() {
        return this.purContractNum;
    }

    /**
    * set the purContractNum - 采购合同号
    */
    public void setPurContractNum(String purContractNum) {
        this.purContractNum = purContractNum;
    }
    /**
    * get the scrapType - 废料类型
    * @return the scrapType
    */
    public String getScrapType() {
        return this.scrapType;
    }

    /**
    * set the scrapType - 废料类型
    */
    public void setScrapType(String scrapType) {
        this.scrapType = scrapType;
    }
    /**
    * get the thickTbthDim - 厚度尺寸(mm)
    * @return the thickTbthDim
    */
    public BigDecimal getThickTbthDim() {
        return this.thickTbthDim;
    }

    /**
    * set the thickTbthDim - 厚度尺寸(mm)
    */
    public void setThickTbthDim(BigDecimal thickTbthDim) {
        this.thickTbthDim = thickTbthDim;
    }
    /**
    * get the recRevisorName - 记录修改者姓名
    * @return the recRevisorName
    */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
    * set the recRevisorName - 记录修改者姓名
    */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }
    /**
    * get the packAmount - 包装金额
    * @return the packAmount
    */
    public BigDecimal getPackAmount() {
        return this.packAmount;
    }

    /**
    * set the packAmount - 包装金额
    */
    public void setPackAmount(BigDecimal packAmount) {
        this.packAmount = packAmount;
    }
    /**
    * get the packId - 包装ID
    * @return the packId
    */
    public String getPackId() {
        return this.packId;
    }

    /**
    * set the packId - 包装ID
    */
    public void setPackId(String packId) {
        this.packId = packId;
    }
    /**
    * get the netWeight - 净重(kg)
    * @return the netWeight
    */
    public BigDecimal getNetWeight() {
        return this.netWeight;
    }

    /**
    * set the netWeight - 净重(kg)
    */
    public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
    }
    /**
    * get the specsDesc - 规格描述
    * @return the specsDesc
    */
    public String getSpecsDesc() {
        return this.specsDesc;
    }

    /**
    * set the specsDesc - 规格描述
    */
    public void setSpecsDesc(String specsDesc) {
        this.specsDesc = specsDesc;
    }
    /**
    * get the recCreator - 记录创建者
    * @return the recCreator
    */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
    * set the recCreator - 记录创建者
    */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }
    /**
    * get the factoryOrderNum - 工厂订单号
    * @return the factoryOrderNum
    */
    public String getFactoryOrderNum() {
        return this.factoryOrderNum;
    }

    /**
    * set the factoryOrderNum - 工厂订单号
    */
    public void setFactoryOrderNum(String factoryOrderNum) {
        this.factoryOrderNum = factoryOrderNum;
    }
    /**
    * get the matInnerId - 物料内部ID
    * @return the matInnerId
    */
    public String getMatInnerId() {
        return this.matInnerId;
    }

    /**
    * set the matInnerId - 物料内部ID
    */
    public void setMatInnerId(String matInnerId) {
        this.matInnerId = matInnerId;
    }
    /**
    * get the prodTypeDesc - 产品类型描述
    * @return the prodTypeDesc
    */
    public String getProdTypeDesc() {
        return this.prodTypeDesc;
    }

    /**
    * set the prodTypeDesc - 产品类型描述
    */
    public void setProdTypeDesc(String prodTypeDesc) {
        this.prodTypeDesc = prodTypeDesc;
    }
    /**
    * get the prodCname - 产品中文名
    * @return the prodCname
    */
    public String getProdCname() {
        return this.prodCname;
    }

    /**
    * set the prodCname - 产品中文名
    */
    public void setProdCname(String prodCname) {
        this.prodCname = prodCname;
    }
    /**
    * get the packType - 包装类型
    * @return the packType
    */
    public String getPackType() {
        return this.packType;
    }

    /**
    * set the packType - 包装类型
    */
    public void setPackType(String packType) {
        this.packType = packType;
    }
    /**
    * get the orderNum - 订单号
    * @return the orderNum
    */
    public String getOrderNum() {
        return this.orderNum;
    }

    /**
    * set the orderNum - 订单号
    */
    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }
    /**
    * get the ladingBillSubid - 提单子ID
    * @return the ladingBillSubid
    */
    public String getLadingBillSubid() {
        return this.ladingBillSubid;
    }

    /**
    * set the ladingBillSubid - 提单子ID
    */
    public void setLadingBillSubid(String ladingBillSubid) {
        this.ladingBillSubid = ladingBillSubid;
    }
    /**
    * get the shopsign - 牌号
    * @return the shopsign
    */
    public String getShopsign() {
        return this.shopsign;
    }

    /**
    * set the shopsign - 牌号
    */
    public void setShopsign(String shopsign) {
        this.shopsign = shopsign;
    }
    /**
    * get the delFlag - 删除标志(0-未删除)
    * @return the delFlag
    */
    public String getDelFlag() {
        return this.delFlag;
    }

    /**
    * set the delFlag - 删除标志(0-未删除)
    */
    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }
    /**
    * get the prodTypeId - 产品类型ID
    * @return the prodTypeId
    */
    public String getProdTypeId() {
        return this.prodTypeId;
    }

    /**
    * set the prodTypeId - 产品类型ID
    */
    public void setProdTypeId(String prodTypeId) {
        this.prodTypeId = prodTypeId;
    }
    /**
    * get the warehouseCode - 仓库代码
    * @return the warehouseCode
    */
    public String getWarehouseCode() {
        return this.warehouseCode;
    }

    /**
    * set the warehouseCode - 仓库代码
    */
    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }
    /**
    * get the m_packId - 主包装ID
    * @return the m_packId
    */
    public String getM_packId() {
        return this.m_packId;
    }

    /**
    * set the m_packId - 主包装ID
    */
    public void setM_packId(String m_packId) {
        this.m_packId = m_packId;
    }
    /**
    * get the pieceNum - 件数
    * @return the pieceNum
    */
    public BigDecimal getPieceNum() {
        return this.pieceNum;
    }

    /**
    * set the pieceNum - 件数
    */
    public void setPieceNum(BigDecimal pieceNum) {
        this.pieceNum = pieceNum;
    }
    /**
    * get the locationId - 位置ID
    * @return the locationId
    */
    public String getLocationId() {
        return this.locationId;
    }

    /**
    * set the locationId - 位置ID
    */
    public void setLocationId(String locationId) {
        this.locationId = locationId;
    }
    /**
    * get the recCreateTime - 记录创建时间
    * @return the recCreateTime
    */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
    * set the recCreateTime - 记录创建时间
    */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }
    /**
    * get the producingArea - 生产区域
    * @return the producingArea
    */
    public String getProducingArea() {
        return this.producingArea;
    }

    /**
    * set the producingArea - 生产区域
    */
    public void setProducingArea(String producingArea) {
        this.producingArea = producingArea;
    }
    /**
    * get the arrearsAmount - 欠款金额
    * @return the arrearsAmount
    */
    public BigDecimal getArrearsAmount() {
        return this.arrearsAmount;
    }

    /**
    * set the arrearsAmount - 欠款金额
    */
    public void setArrearsAmount(BigDecimal arrearsAmount) {
        this.arrearsAmount = arrearsAmount;
    }
    /**
    * get the length - 长度(mm)
    * @return the length
    */
    public BigDecimal getLength() {
        return this.length;
    }

    /**
    * set the length - 长度(mm)
    */
    public void setLength(BigDecimal length) {
        this.length = length;
    }
    /**
    * get the userBillId - 用户账单ID
    * @return the userBillId
    */
    public String getUserBillId() {
        return this.userBillId;
    }

    /**
    * set the userBillId - 用户账单ID
    */
    public void setUserBillId(String userBillId) {
        this.userBillId = userBillId;
    }
    /**
    * get the qualityGrade - 质量等级
    * @return the qualityGrade
    */
    public String getQualityGrade() {
        return this.qualityGrade;
    }

    /**
    * set the qualityGrade - 质量等级
    */
    public void setQualityGrade(String qualityGrade) {
        this.qualityGrade = qualityGrade;
    }
    /**
    * get the prodCode - 产品代码
    * @return the prodCode
    */
    public String getProdCode() {
        return this.prodCode;
    }

    /**
    * set the prodCode - 产品代码
    */
    public void setProdCode(String prodCode) {
        this.prodCode = prodCode;
    }
    /**
    * get the unitedPackId - 联合包装ID
    * @return the unitedPackId
    */
    public String getUnitedPackId() {
        return this.unitedPackId;
    }

    /**
    * set the unitedPackId - 联合包装ID
    */
    public void setUnitedPackId(String unitedPackId) {
        this.unitedPackId = unitedPackId;
    }
    /**
    * get the custPartName - 客户零件名称
    * @return the custPartName
    */
    public String getCustPartName() {
        return this.custPartName;
    }

    /**
    * set the custPartName - 客户零件名称
    */
    public void setCustPartName(String custPartName) {
        this.custPartName = custPartName;
    }
    /**
    * get the grossWeight - 毛重(kg)
    * @return the grossWeight
    */
    public BigDecimal getGrossWeight() {
        return this.grossWeight;
    }

    /**
    * set the grossWeight - 毛重(kg)
    */
    public void setGrossWeight(BigDecimal grossWeight) {
        this.grossWeight = grossWeight;
    }
    /**
    * get the segNo - 账套
    * @return the segNo
    */
    public String getSegNo() {
        return this.segNo;
    }

    /**
    * set the segNo - 账套
    */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }
    /**
    * get the width - 宽度(mm)
    * @return the width
    */
    public BigDecimal getWidth() {
        return this.width;
    }

    /**
    * set the width - 宽度(mm)
    */
    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    /**
    * get the value from Map
    */
    public void fromMap(Map map) {
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setMeasureId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("measureId")), measureId));
        setPurOrderNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("purOrderNum")), purOrderNum));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setReturnStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("returnStatus")), returnStatus));
        setLadingBillId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ladingBillId")), ladingBillId));
        setUnitedFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitedFlag")), unitedFlag));
        setSettleQty(NumberUtils.toBigDecimal(StringUtils.toString(map.get("settleQty")), settleQty));
        setTenantUser(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantUser")), tenantUser));
        setLabelId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("labelId")), labelId));
        setCustPartId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("custPartId")), custPartId));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setHeatNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("heatNum")), heatNum));
        setPurContractNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("purContractNum")), purContractNum));
        setScrapType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("scrapType")), scrapType));
        setThickTbthDim(NumberUtils.toBigDecimal(StringUtils.toString(map.get("thickTbthDim")), thickTbthDim));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setPackAmount(NumberUtils.toBigDecimal(StringUtils.toString(map.get("packAmount")), packAmount));
        setPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("packId")), packId));
        setNetWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("netWeight")), netWeight));
        setSpecsDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("specsDesc")), specsDesc));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setFactoryOrderNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryOrderNum")), factoryOrderNum));
        setMatInnerId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("matInnerId")), matInnerId));
        setProdTypeDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodTypeDesc")), prodTypeDesc));
        setProdCname(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodCname")), prodCname));
        setPackType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("packType")), packType));
        setOrderNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("orderNum")), orderNum));
        setLadingBillSubid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ladingBillSubid")), ladingBillSubid));
        setShopsign(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("shopsign")), shopsign));
        setDelFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("delFlag")), delFlag));
        setProdTypeId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodTypeId")), prodTypeId));
        setWarehouseCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("warehouseCode")), warehouseCode));
        setM_packId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("m_packId")), m_packId));
        setPieceNum(NumberUtils.toBigDecimal(StringUtils.toString(map.get("pieceNum")), pieceNum));
        setLocationId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("locationId")), locationId));
        //TODO recCreateTime cannot generate automatically ,LocalDateTime dont support
        setProducingArea(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("producingArea")), producingArea));
        setArrearsAmount(NumberUtils.toBigDecimal(StringUtils.toString(map.get("arrearsAmount")), arrearsAmount));
        setLength(NumberUtils.toBigDecimal(StringUtils.toString(map.get("length")), length));
        setUserBillId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("userBillId")), userBillId));
        setQualityGrade(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("qualityGrade")), qualityGrade));
        setProdCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodCode")), prodCode));
        setUnitedPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitedPackId")), unitedPackId));
        setCustPartName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("custPartName")), custPartName));
        setGrossWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("grossWeight")), grossWeight));
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setWidth(NumberUtils.toBigDecimal(StringUtils.toString(map.get("width")), width));
    }

    /**
    * set the value to Map
    */
    public Map toMap() {
        Map map = new HashMap();
        map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("measureId",StringUtils.toString(measureId, eiMetadata.getMeta("measureId")));
        map.put("purOrderNum",StringUtils.toString(purOrderNum, eiMetadata.getMeta("purOrderNum")));
        map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("returnStatus",StringUtils.toString(returnStatus, eiMetadata.getMeta("returnStatus")));
        map.put("ladingBillId",StringUtils.toString(ladingBillId, eiMetadata.getMeta("ladingBillId")));
        map.put("unitedFlag",StringUtils.toString(unitedFlag, eiMetadata.getMeta("unitedFlag")));
        map.put("settleQty",StringUtils.toString(settleQty, eiMetadata.getMeta("settleQty")));
        map.put("tenantUser",StringUtils.toString(tenantUser, eiMetadata.getMeta("tenantUser")));
        map.put("labelId",StringUtils.toString(labelId, eiMetadata.getMeta("labelId")));
        map.put("custPartId",StringUtils.toString(custPartId, eiMetadata.getMeta("custPartId")));
        map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("heatNum",StringUtils.toString(heatNum, eiMetadata.getMeta("heatNum")));
        map.put("purContractNum",StringUtils.toString(purContractNum, eiMetadata.getMeta("purContractNum")));
        map.put("scrapType",StringUtils.toString(scrapType, eiMetadata.getMeta("scrapType")));
        map.put("thickTbthDim",StringUtils.toString(thickTbthDim, eiMetadata.getMeta("thickTbthDim")));
        map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("packAmount",StringUtils.toString(packAmount, eiMetadata.getMeta("packAmount")));
        map.put("packId",StringUtils.toString(packId, eiMetadata.getMeta("packId")));
        map.put("netWeight",StringUtils.toString(netWeight, eiMetadata.getMeta("netWeight")));
        map.put("specsDesc",StringUtils.toString(specsDesc, eiMetadata.getMeta("specsDesc")));
        map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("factoryOrderNum",StringUtils.toString(factoryOrderNum, eiMetadata.getMeta("factoryOrderNum")));
        map.put("matInnerId",StringUtils.toString(matInnerId, eiMetadata.getMeta("matInnerId")));
        map.put("prodTypeDesc",StringUtils.toString(prodTypeDesc, eiMetadata.getMeta("prodTypeDesc")));
        map.put("prodCname",StringUtils.toString(prodCname, eiMetadata.getMeta("prodCname")));
        map.put("packType",StringUtils.toString(packType, eiMetadata.getMeta("packType")));
        map.put("orderNum",StringUtils.toString(orderNum, eiMetadata.getMeta("orderNum")));
        map.put("ladingBillSubid",StringUtils.toString(ladingBillSubid, eiMetadata.getMeta("ladingBillSubid")));
        map.put("shopsign",StringUtils.toString(shopsign, eiMetadata.getMeta("shopsign")));
        map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("prodTypeId",StringUtils.toString(prodTypeId, eiMetadata.getMeta("prodTypeId")));
        map.put("warehouseCode",StringUtils.toString(warehouseCode, eiMetadata.getMeta("warehouseCode")));
        map.put("m_packId",StringUtils.toString(m_packId, eiMetadata.getMeta("m_packId")));
        map.put("pieceNum",StringUtils.toString(pieceNum, eiMetadata.getMeta("pieceNum")));
        map.put("locationId",StringUtils.toString(locationId, eiMetadata.getMeta("locationId")));
        map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("producingArea",StringUtils.toString(producingArea, eiMetadata.getMeta("producingArea")));
        map.put("arrearsAmount",StringUtils.toString(arrearsAmount, eiMetadata.getMeta("arrearsAmount")));
        map.put("length",StringUtils.toString(length, eiMetadata.getMeta("length")));
        map.put("userBillId",StringUtils.toString(userBillId, eiMetadata.getMeta("userBillId")));
        map.put("qualityGrade",StringUtils.toString(qualityGrade, eiMetadata.getMeta("qualityGrade")));
        map.put("prodCode",StringUtils.toString(prodCode, eiMetadata.getMeta("prodCode")));
        map.put("unitedPackId",StringUtils.toString(unitedPackId, eiMetadata.getMeta("unitedPackId")));
        map.put("custPartName",StringUtils.toString(custPartName, eiMetadata.getMeta("custPartName")));
        map.put("grossWeight",StringUtils.toString(grossWeight, eiMetadata.getMeta("grossWeight")));
        map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("width",StringUtils.toString(width, eiMetadata.getMeta("width")));
        return map;
    }
}