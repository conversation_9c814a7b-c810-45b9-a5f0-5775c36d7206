<?xml version="1.0" encoding="UTF-8"?>
<!DOC<PERSON><PERSON><PERSON> sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2025-07-04 15:31:29
   		Version :  1.0
		tableName :${meliSchema}.tlirl0598 
		 UUID  VARCHAR   NOT NULL   primarykey, 
		 MEASURE_ID  VARCHAR, 
		 PUR_ORDER_NUM  VARCHAR, 
		 ARCHIVE_FLAG  VARCHAR, 
		 REC_REVISE_TIME  VARCHAR, 
		 RETURN_STATUS  VARCHAR, 
		 LADING_BILL_ID  VARCHAR, 
		 UNITED_FLAG  VARCHAR, 
		 SETTLE_QTY  DECIMAL, 
		 TENANT_USER  VARCHAR, 
		 LABEL_ID  VARCHAR, 
		 CUST_PART_ID  VARCHAR, 
		 REC_CREATOR_NAME  VARCHAR, 
		 UNIT_CODE  VARCHAR, 
		 HEAT_NUM  VARCHAR, 
		 PUR_CONTRACT_NUM  VARCHAR, 
		 SCRAP_TYPE  VARCHAR, 
		 THICK_TBTH_DIM  DECIMAL, 
		 REC_REVISOR_NAME  VARCHAR, 
		 PACK_AMOUNT  DECIMAL, 
		 PACK_ID  VARCHAR, 
		 NET_WEIGHT  DECIMAL, 
		 SPECS_DESC  VARCHAR, 
		 REC_CREATOR  VARCHAR, 
		 FACTORY_ORDER_NUM  VARCHAR, 
		 MAT_INNER_ID  VARCHAR, 
		 PROD_TYPE_DESC  VARCHAR, 
		 PROD_CNAME  VARCHAR, 
		 PACK_TYPE  VARCHAR, 
		 ORDER_NUM  VARCHAR, 
		 LADING_BILL_SUBID  VARCHAR, 
		 SHOPSIGN  VARCHAR, 
		 DEL_FLAG  VARCHAR, 
		 PROD_TYPE_ID  VARCHAR, 
		 WAREHOUSE_CODE  VARCHAR, 
		 M_PACK_ID  VARCHAR, 
		 PIECE_NUM  DECIMAL, 
		 LOCATION_ID  VARCHAR, 
		 REC_CREATE_TIME  DATETIME, 
		 PRODUCING_AREA  VARCHAR, 
		 ARREARS_AMOUNT  DECIMAL, 
		 LENGTH  DECIMAL, 
		 USER_BILL_ID  VARCHAR, 
		 QUALITY_GRADE  VARCHAR, 
		 PROD_CODE  VARCHAR, 
		 UNITED_PACK_ID  VARCHAR, 
		 CUST_PART_NAME  VARCHAR, 
		 GROSS_WEIGHT  DECIMAL, 
		 SEG_NO  VARCHAR, 
		 WIDTH  DECIMAL
	-->
<sqlMap namespace="tlirl0598">

	<select id="query" parameterClass="java.util.Map"
			resultClass="com.baosight.imom.common.li.domain.Tlirl0598">
		SELECT
				UUID	as "uuid",  <!-- 唯一标识 -->
				MEASURE_ID	as "measureId",  <!-- 测量ID -->
				PUR_ORDER_NUM	as "purOrderNum",  <!-- 采购订单号 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标志 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				RETURN_STATUS	as "returnStatus",  <!-- 返回状态 -->
				LADING_BILL_ID	as "ladingBillId",  <!-- 提单ID -->
				UNITED_FLAG	as "unitedFlag",  <!-- 联合标志 -->
				SETTLE_QTY	as "settleQty",  <!-- 结算数量 -->
				TENANT_USER	as "tenantUser",  <!-- 租户用户 -->
				LABEL_ID	as "labelId",  <!-- 标签ID -->
				CUST_PART_ID	as "custPartId",  <!-- 客户零件ID -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建者姓名 -->
				UNIT_CODE	as "unitCode",  <!-- 单位代码 -->
				HEAT_NUM	as "heatNum",  <!-- 热号 -->
				PUR_CONTRACT_NUM	as "purContractNum",  <!-- 采购合同号 -->
				SCRAP_TYPE	as "scrapType",  <!-- 废料类型 -->
				THICK_TBTH_DIM	as "thickTbthDim",  <!-- 厚度尺寸(mm) -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改者姓名 -->
				PACK_AMOUNT	as "packAmount",  <!-- 包装金额 -->
				PACK_ID	as "packId",  <!-- 包装ID -->
				NET_WEIGHT	as "netWeight",  <!-- 净重(kg) -->
				SPECS_DESC	as "specsDesc",  <!-- 规格描述 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建者 -->
				FACTORY_ORDER_NUM	as "factoryOrderNum",  <!-- 工厂订单号 -->
				MAT_INNER_ID	as "matInnerId",  <!-- 物料内部ID -->
				PROD_TYPE_DESC	as "prodTypeDesc",  <!-- 产品类型描述 -->
				PROD_CNAME	as "prodCname",  <!-- 产品中文名 -->
				PACK_TYPE	as "packType",  <!-- 包装类型 -->
				ORDER_NUM	as "orderNum",  <!-- 订单号 -->
				LADING_BILL_SUBID	as "ladingBillSubid",  <!-- 提单子ID -->
				SHOPSIGN	as "shopsign",  <!-- 牌号 -->
				DEL_FLAG	as "delFlag",  <!-- 删除标志(0-未删除) -->
				PROD_TYPE_ID	as "prodTypeId",  <!-- 产品类型ID -->
				WAREHOUSE_CODE	as "warehouseCode",  <!-- 仓库代码 -->
				M_PACK_ID	as "m_packId",  <!-- 主包装ID -->
				PIECE_NUM	as "pieceNum",  <!-- 件数 -->
				LOCATION_ID	as "locationId",  <!-- 位置ID -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				PRODUCING_AREA	as "producingArea",  <!-- 生产区域 -->
				ARREARS_AMOUNT	as "arrearsAmount",  <!-- 欠款金额 -->
				LENGTH	as "length",  <!-- 长度(mm) -->
				USER_BILL_ID	as "userBillId",  <!-- 用户账单ID -->
				QUALITY_GRADE	as "qualityGrade",  <!-- 质量等级 -->
				PROD_CODE	as "prodCode",  <!-- 产品代码 -->
				UNITED_PACK_ID	as "unitedPackId",  <!-- 联合包装ID -->
				CUST_PART_NAME	as "custPartName",  <!-- 客户零件名称 -->
				GROSS_WEIGHT	as "grossWeight",  <!-- 毛重(kg) -->
				SEG_NO	as "segNo",  <!-- 账套 -->
				WIDTH	as "width" <!-- 宽度(mm) -->
		FROM ${meliSchema}.tlirl0598 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
    		  UUID asc
		</isEmpty>
  		</dynamic>
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM ${meliSchema}.tlirl0598 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
	</select>

	<insert id="insert">
		INSERT INTO ${meliSchema}.tlirl0598 (UUID,  <!-- 唯一标识 -->
										MEASURE_ID,  <!-- 测量ID -->
										PUR_ORDER_NUM,  <!-- 采购订单号 -->
										ARCHIVE_FLAG,  <!-- 归档标志 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										RETURN_STATUS,  <!-- 返回状态 -->
										LADING_BILL_ID,  <!-- 提单ID -->
										UNITED_FLAG,  <!-- 联合标志 -->
										SETTLE_QTY,  <!-- 结算数量 -->
										TENANT_USER,  <!-- 租户用户 -->
										LABEL_ID,  <!-- 标签ID -->
										CUST_PART_ID,  <!-- 客户零件ID -->
										REC_CREATOR_NAME,  <!-- 记录创建者姓名 -->
										UNIT_CODE,  <!-- 单位代码 -->
										HEAT_NUM,  <!-- 热号 -->
										PUR_CONTRACT_NUM,  <!-- 采购合同号 -->
										SCRAP_TYPE,  <!-- 废料类型 -->
										THICK_TBTH_DIM,  <!-- 厚度尺寸(mm) -->
										REC_REVISOR_NAME,  <!-- 记录修改者姓名 -->
										PACK_AMOUNT,  <!-- 包装金额 -->
										PACK_ID,  <!-- 包装ID -->
										NET_WEIGHT,  <!-- 净重(kg) -->
										SPECS_DESC,  <!-- 规格描述 -->
										REC_CREATOR,  <!-- 记录创建者 -->
										FACTORY_ORDER_NUM,  <!-- 工厂订单号 -->
										MAT_INNER_ID,  <!-- 物料内部ID -->
										PROD_TYPE_DESC,  <!-- 产品类型描述 -->
										PROD_CNAME,  <!-- 产品中文名 -->
										PACK_TYPE,  <!-- 包装类型 -->
										ORDER_NUM,  <!-- 订单号 -->
										LADING_BILL_SUBID,  <!-- 提单子ID -->
										SHOPSIGN,  <!-- 牌号 -->
										DEL_FLAG,  <!-- 删除标志(0-未删除) -->
										PROD_TYPE_ID,  <!-- 产品类型ID -->
										WAREHOUSE_CODE,  <!-- 仓库代码 -->
										M_PACK_ID,  <!-- 主包装ID -->
										PIECE_NUM,  <!-- 件数 -->
										LOCATION_ID,  <!-- 位置ID -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										PRODUCING_AREA,  <!-- 生产区域 -->
										ARREARS_AMOUNT,  <!-- 欠款金额 -->
										LENGTH,  <!-- 长度(mm) -->
										USER_BILL_ID,  <!-- 用户账单ID -->
										QUALITY_GRADE,  <!-- 质量等级 -->
										PROD_CODE,  <!-- 产品代码 -->
										UNITED_PACK_ID,  <!-- 联合包装ID -->
										CUST_PART_NAME,  <!-- 客户零件名称 -->
										GROSS_WEIGHT,  <!-- 毛重(kg) -->
										SEG_NO,  <!-- 账套 -->
										WIDTH  <!-- 宽度(mm) -->
										)		 
	    VALUES (#uuid#, #measureId#, #purOrderNum#, #archiveFlag#, #recReviseTime#, #returnStatus#, #ladingBillId#, #unitedFlag#, #settleQty#, #tenantUser#, #labelId#, #custPartId#, #recCreatorName#, #unitCode#, #heatNum#, #purContractNum#, #scrapType#, #thickTbthDim#, #recRevisorName#, #packAmount#, #packId#, #netWeight#, #specsDesc#, #recCreator#, #factoryOrderNum#, #matInnerId#, #prodTypeDesc#, #prodCname#, #packType#, #orderNum#, #ladingBillSubid#, #shopsign#, #delFlag#, #prodTypeId#, #warehouseCode#, #m_packId#, #pieceNum#, #locationId#, #recCreateTime#, #producingArea#, #arrearsAmount#, #length#, #userBillId#, #qualityGrade#, #prodCode#, #unitedPackId#, #custPartName#, #grossWeight#, #segNo#, #width#) 
	</insert>
  
	<delete id="delete">
		DELETE FROM ${meliSchema}.tlirl0598 WHERE 
			1=1
		and SEG_NO = #segNo#
	</delete>

	<update id="update">
		UPDATE ${meliSchema}.tlirl0598 
		SET 
					MEASURE_ID	= #measureId#,   <!-- 测量ID -->  
					PUR_ORDER_NUM	= #purOrderNum#,   <!-- 采购订单号 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标志 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					RETURN_STATUS	= #returnStatus#,   <!-- 返回状态 -->  
					LADING_BILL_ID	= #ladingBillId#,   <!-- 提单ID -->  
					UNITED_FLAG	= #unitedFlag#,   <!-- 联合标志 -->  
					SETTLE_QTY	= #settleQty#,   <!-- 结算数量 -->  
					TENANT_USER	= #tenantUser#,   <!-- 租户用户 -->  
					LABEL_ID	= #labelId#,   <!-- 标签ID -->  
					CUST_PART_ID	= #custPartId#,   <!-- 客户零件ID -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建者姓名 -->  
					UNIT_CODE	= #unitCode#,   <!-- 单位代码 -->  
					HEAT_NUM	= #heatNum#,   <!-- 热号 -->  
					PUR_CONTRACT_NUM	= #purContractNum#,   <!-- 采购合同号 -->  
					SCRAP_TYPE	= #scrapType#,   <!-- 废料类型 -->  
					THICK_TBTH_DIM	= #thickTbthDim#,   <!-- 厚度尺寸(mm) -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改者姓名 -->  
					PACK_AMOUNT	= #packAmount#,   <!-- 包装金额 -->  
					PACK_ID	= #packId#,   <!-- 包装ID -->  
					NET_WEIGHT	= #netWeight#,   <!-- 净重(kg) -->  
					SPECS_DESC	= #specsDesc#,   <!-- 规格描述 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建者 -->  
					FACTORY_ORDER_NUM	= #factoryOrderNum#,   <!-- 工厂订单号 -->  
					MAT_INNER_ID	= #matInnerId#,   <!-- 物料内部ID -->  
					PROD_TYPE_DESC	= #prodTypeDesc#,   <!-- 产品类型描述 -->  
					PROD_CNAME	= #prodCname#,   <!-- 产品中文名 -->  
					PACK_TYPE	= #packType#,   <!-- 包装类型 -->  
					ORDER_NUM	= #orderNum#,   <!-- 订单号 -->  
					LADING_BILL_SUBID	= #ladingBillSubid#,   <!-- 提单子ID -->  
					SHOPSIGN	= #shopsign#,   <!-- 牌号 -->  
					DEL_FLAG	= #delFlag#,   <!-- 删除标志(0-未删除) -->  
					PROD_TYPE_ID	= #prodTypeId#,   <!-- 产品类型ID -->  
					WAREHOUSE_CODE	= #warehouseCode#,   <!-- 仓库代码 -->  
					M_PACK_ID	= #m_packId#,   <!-- 主包装ID -->  
					PIECE_NUM	= #pieceNum#,   <!-- 件数 -->  
					LOCATION_ID	= #locationId#,   <!-- 位置ID -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					PRODUCING_AREA	= #producingArea#,   <!-- 生产区域 -->  
					ARREARS_AMOUNT	= #arrearsAmount#,   <!-- 欠款金额 -->  
					LENGTH	= #length#,   <!-- 长度(mm) -->  
					USER_BILL_ID	= #userBillId#,   <!-- 用户账单ID -->  
					QUALITY_GRADE	= #qualityGrade#,   <!-- 质量等级 -->  
					PROD_CODE	= #prodCode#,   <!-- 产品代码 -->  
					UNITED_PACK_ID	= #unitedPackId#,   <!-- 联合包装ID -->  
					CUST_PART_NAME	= #custPartName#,   <!-- 客户零件名称 -->  
					GROSS_WEIGHT	= #grossWeight#,   <!-- 毛重(kg) -->  
					SEG_NO	= #segNo#,   <!-- 账套 -->  
					WIDTH	= #width#  <!-- 宽度(mm) -->  
			WHERE 	
			UUID = #uuid#
	</update>
  
</sqlMap>