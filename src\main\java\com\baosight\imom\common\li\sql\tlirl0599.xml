<?xml version="1.0" encoding="UTF-8"?>
<!DOCTY<PERSON><PERSON> sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2025-07-04 09:28:12
   		Version :  1.0
		tableName :${meliSchema}.tlirl0599 
		 ACT_CONSIGNEE_NAME  VARCHAR, 
		 LADING_SPOT_ADDR  VARCHAR, 
		 TOTAL_PACK_QTY  DECIMAL, 
		 TRANSFER_NOTI_SYSTEM  VARCHAR, 
		 REC_REVISE_TIME  VARCHAR, 
		 PROCESS_FACTORY_NAME  VARCHAR, 
		 PRIVATE_ROUTE_NAME  VARCHAR, 
		 LADING_BILL_ID  VARCHAR   NOT NULL, 
		 TENANT_USER  VARCHAR, 
		 DEST_SPOT_CONTACTOR  VARCHAR, 
		 DELIVERY_DATE_TIME  VARCHAR, 
		 REC_CREATOR_NAME  VARCHAR, 
		 PRINT_<PERSON><PERSON>  VARCHAR, 
		 <PERSON><PERSON>_CO<PERSON>  VARCHAR, 
		 PRINT_BATCH_ID  VARCHAR, 
		 VERIFI_SOURCE  VARCHAR, 
		 NO_PAPER_FLAG  VARCHAR, 
		 REC_REVISOR_NAME  VARCHAR, 
		 DEST_SPOT_NAME  VARCHAR, 
		 BILLING_METHOD  VARCHAR, 
		 PRINT_DATE  VARCHAR, 
		 LOGISTIC_SCHEME_NO  VARCHAR, 
		 FIN_USER_ID  VARCHAR, 
		 BILL_EXPI_PROC_MARK  VARCHAR, 
		 LADING_SPOT_ID  VARCHAR, 
		 REC_CREATOR  VARCHAR, 
		 VERIFI_DATE  VARCHAR, 
		 DRIVER  VARCHAR, 
		 DRIVER_ID  VARCHAR, 
		 D_USER_NAME  VARCHAR, 
		 SYN_FLAG  VARCHAR, 
		 VEHICLE_NAME  VARCHAR, 
		 RAIN_COAT_FLAG  VARCHAR, 
		 ACT_CONSIGNEE_ID  VARCHAR, 
		 DEL_FLAG  VARCHAR, 
		 SHIP_VERIFI_CODE  VARCHAR, 
		 VERIFI_PERSON  VARCHAR, 
		 ENTRUST_FLAG  VARCHAR, 
		 LADING_SPOT_ADDR_ID  VARCHAR, 
		 MANUAL_NO  VARCHAR   NOT NULL, 
		 BILL_BANK_BATCH_ID  VARCHAR, 
		 STORE_TYPE  VARCHAR, 
		 BL_TOT_AMT  DECIMAL, 
		 CONSIGNEE_TYPE  VARCHAR, 
		 D_USER_NUM  VARCHAR, 
		 BILLING_TIME  VARCHAR, 
		 APPLY_NUM  VARCHAR, 
		 ARREAR_TYPE  VARCHAR, 
		 SEG_NO  VARCHAR, 
		 PRINT_DATE_FIRST  VARCHAR, 
		 OUT_WEIGHT  DECIMAL, 
		 DRIVER_PHONE2  VARCHAR, 
		 CONTRACT_NUM  VARCHAR   NOT NULL, 
		 EXPIRY_DAYS  SMALLINT UNSIGNED, 
		 TRANSFER_OBJECT  VARCHAR, 
		 CONSIGNEE_NUM  VARCHAR, 
		 RESOURCE_TYPE  VARCHAR, 
		 TRANSFER_NOTI_DESC  VARCHAR, 
		 VERIFI_STATUS  VARCHAR, 
		 LADING_SPOT_CONTACTOR  VARCHAR, 
		 USER_NUM  VARCHAR, 
		 ACTIVE_END_TIME  VARCHAR, 
		 PLAN_LOAD_QTY  DECIMAL, 
		 STORE_SETTLE_TYPE  VARCHAR, 
		 ARCHIVE_FLAG  VARCHAR, 
		 CONSIGN_ID  VARCHAR, 
		 UUID  VARCHAR   NOT NULL   primarykey, 
		 CONSIGN_MARK  VARCHAR, 
		 REC_REVISOR  VARCHAR, 
		 DEST_SPOT_ADDR  VARCHAR, 
		 LADING_BILL_TYPE  VARCHAR, 
		 LADING_BILL_STATUS  VARCHAR, 
		 PROCESS_FACTORY_ID  VARCHAR, 
		 PLAN_IN_WARHOUSE_TIME  VARCHAR, 
		 USER_NAME_ABBR  VARCHAR, 
		 VERIFY_DATE  VARCHAR, 
		 STORE_FEE_SETTLE_TYPE  VARCHAR, 
		 PRIVATE_ROUTE_CODE  VARCHAR, 
		 TRADE_CODE  VARCHAR, 
		 VEHICLE_CODE  VARCHAR, 
		 BILL_BANK_STATUS  VARCHAR, 
		 VEHICLE_NO  VARCHAR, 
		 TRANS_TYPE  VARCHAR, 
		 TPROVIDER_CONTACT_ID  VARCHAR, 
		 FINANCE_CONTRACT  VARCHAR, 
		 FIN_USER_NAME  VARCHAR, 
		 ARREARS_SHIP_NO  VARCHAR, 
		 TPROVIDER_ID  VARCHAR, 
		 TRANS_FEE_SETTLE_TYPE  VARCHAR, 
		 PLAN_OUT_WARHOUSE_TIME  VARCHAR, 
		 DEST_SPOT_TELE  VARCHAR, 
		 DEST_SPOT_ADDR_ID  VARCHAR, 
		 ARREARS_SHIP_AMOUNT  DECIMAL, 
		 TPROVIDER_CONTACT  VARCHAR, 
		 ORDER_CUST_CNAME  VARCHAR, 
		 OUT_FEE_SETTLE_TYPE  VARCHAR, 
		 REMARK  VARCHAR, 
		 VERIFY_MAN  VARCHAR, 
		 REQUIRE_FINISH_DATE  VARCHAR, 
		 CONSIGN_BILL_STATUS  VARCHAR, 
		 CONSIGNEE_NAME  VARCHAR, 
		 BUSINESS_NO  VARCHAR, 
		 EMERGENCY_DELIVERY_TIME  VARCHAR, 
		 DEST_SPOT_ID  VARCHAR, 
		 PLAN_LOAD_WT  DECIMAL, 
		 REC_CREATE_TIME  DATETIME, 
		 TPROVIDER_CONTACT_PLATE  VARCHAR, 
		 VEHICLE_ID  VARCHAR, 
		 ACTIVE_START_TIME  VARCHAR, 
		 DELIVERY_TYPE  VARCHAR, 
		 OUT_PACK_QTY  DECIMAL, 
		 LADING_SPOT_NAME  VARCHAR, 
		 TPROVIDER_CONTACT_MOBILE  VARCHAR, 
		 TRANS_NO  VARCHAR, 
		 USER_NAME  VARCHAR, 
		 LADING_SPOT_TELE  VARCHAR, 
		 PRINT_NUMBER  DECIMAL, 
		 DEST_COUNTRY  VARCHAR, 
		 TPROVIDER_CONTACT_MOBILE2  VARCHAR, 
		 TPROVIDER_NAME  VARCHAR, 
		 TOTAL_WEIGHT  DECIMAL, 
		 DRIVER_PHONE  VARCHAR
	-->
<sqlMap namespace="tlirl0599">

	<select id="query" parameterClass="java.util.Map"
			resultClass="com.baosight.imom.common.li.domain.Tlirl0599">
		SELECT
				ACT_CONSIGNEE_NAME	as "actConsigneeName",  <!-- 实际收货方名称 -->
				LADING_SPOT_ADDR	as "ladingSpotAddr",  <!-- 装货地点地址 -->
				TOTAL_PACK_QTY	as "totalPackQty",  <!-- 总包装数量 -->
				TRANSFER_NOTI_SYSTEM	as "transferNotiSystem",  
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				PROCESS_FACTORY_NAME	as "processFactoryName",  
				PRIVATE_ROUTE_NAME	as "privateRouteName",  
				LADING_BILL_ID	as "ladingBillId",  <!-- 提单号（业务主键） -->
				TENANT_USER	as "tenantUser",  
				DEST_SPOT_CONTACTOR	as "destSpotContactor",  <!-- 目的地联系人 -->
				DELIVERY_DATE_TIME	as "deliveryDateTime",  <!-- 发货日期 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				PRINT_MAN	as "printMan",  
				UNIT_CODE	as "unitCode",  <!-- 单位编码 -->
				PRINT_BATCH_ID	as "printBatchId",  
				VERIFI_SOURCE	as "verifiSource",  
				NO_PAPER_FLAG	as "noPaperFlag",  <!-- 无纸化标志 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人 -->
				DEST_SPOT_NAME	as "destSpotName",  <!-- 目的地名称 -->
				BILLING_METHOD	as "billingMethod",  <!-- 计费方式代码 -->
				PRINT_DATE	as "printDate",  
				LOGISTIC_SCHEME_NO	as "logisticSchemeNo",  
				FIN_USER_ID	as "finUserId",  <!-- 财务用户ID -->
				BILL_EXPI_PROC_MARK	as "billExpiProcMark",  <!-- 票据处理标志 -->
				LADING_SPOT_ID	as "ladingSpotId",  <!-- 装货地ID -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人ID -->
				VERIFI_DATE	as "verifiDate",  
				DRIVER	as "driver",  
				DRIVER_ID	as "driverId",  
				D_USER_NAME	as "d_userName",  
				SYN_FLAG	as "synFlag",  
				VEHICLE_NAME	as "vehicleName",  
				RAIN_COAT_FLAG	as "rainCoatFlag",  
				ACT_CONSIGNEE_ID	as "actConsigneeId",  <!-- 实际收货方ID -->
				DEL_FLAG	as "delFlag",  <!-- 删除标志（0未删除） -->
				SHIP_VERIFI_CODE	as "shipVerifiCode",  
				VERIFI_PERSON	as "verifiPerson",  
				ENTRUST_FLAG	as "entrustFlag",  
				LADING_SPOT_ADDR_ID	as "ladingSpotAddrId",  <!-- 装货地址ID -->
				MANUAL_NO	as "manualNo",  <!-- 手工单号 -->
				BILL_BANK_BATCH_ID	as "billBankBatchId",  
				STORE_TYPE	as "storeType",  
				BL_TOT_AMT	as "blTotAmt",  <!-- 总金额 -->
				CONSIGNEE_TYPE	as "consigneeType",  <!-- 收货方类型 -->
				D_USER_NUM	as "d_userNum",  <!-- 用户编号 -->
				BILLING_TIME	as "billingTime",  <!-- 开单时间 -->
				APPLY_NUM	as "applyNum",  <!-- 申请数量 -->
				ARREAR_TYPE	as "arrearType",  
				SEG_NO	as "segNo",  <!-- 段号 -->
				PRINT_DATE_FIRST	as "printDateFirst",  
				OUT_WEIGHT	as "outWeight",  <!-- 出库重量 -->
				DRIVER_PHONE2	as "driverPhone2",  
				CONTRACT_NUM	as "contractNum",  <!-- 合同号 -->
				EXPIRY_DAYS	as "expiryDays",  <!-- 有效期天数 -->
				TRANSFER_OBJECT	as "transferObject",  
				CONSIGNEE_NUM	as "consigneeNum",  <!-- 收货方编号 -->
				RESOURCE_TYPE	as "resourceType",  
				TRANSFER_NOTI_DESC	as "transferNotiDesc",  
				VERIFI_STATUS	as "verifiStatus",  <!-- 核验状态 -->
				LADING_SPOT_CONTACTOR	as "ladingSpotContactor",  <!-- 装货地联系人 -->
				USER_NUM	as "userNum",  <!-- 用户编号 -->
				ACTIVE_END_TIME	as "activeEndTime",  <!-- 有效截止时间 -->
				PLAN_LOAD_QTY	as "planLoadQty",  <!-- 计划装载量 -->
				STORE_SETTLE_TYPE	as "storeSettleType",  
				ARCHIVE_FLAG	as "archiveFlag",  
				CONSIGN_ID	as "consignId",  
				UUID	as "uuid",  <!-- 唯一标识（主键） -->
				CONSIGN_MARK	as "consignMark",  
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人ID -->
				DEST_SPOT_ADDR	as "destSpotAddr",  <!-- 目的地地址 -->
				LADING_BILL_TYPE	as "ladingBillType",  <!-- 提单类型 -->
				LADING_BILL_STATUS	as "ladingBillStatus",  <!-- 提单状态 -->
				PROCESS_FACTORY_ID	as "processFactoryId",  
				PLAN_IN_WARHOUSE_TIME	as "planInWarhouseTime",  
				USER_NAME_ABBR	as "userNameAbbr",  
				VERIFY_DATE	as "verifyDate",  
				STORE_FEE_SETTLE_TYPE	as "storeFeeSettleType",  <!-- 仓储费结算类型 -->
				PRIVATE_ROUTE_CODE	as "privateRouteCode",  
				TRADE_CODE	as "tradeCode",  <!-- 贸易代码 -->
				VEHICLE_CODE	as "vehicleCode",  
				BILL_BANK_STATUS	as "billBankStatus",  
				VEHICLE_NO	as "vehicleNo",  <!-- 车牌号 -->
				TRANS_TYPE	as "transType",  <!-- 运输类型 -->
				TPROVIDER_CONTACT_ID	as "tproviderContactId",  
				FINANCE_CONTRACT	as "financeContract",  
				FIN_USER_NAME	as "finUserName",  <!-- 财务用户名称 -->
				ARREARS_SHIP_NO	as "arrearsShipNo",  
				TPROVIDER_ID	as "tproviderId",  <!-- 运输商ID -->
				TRANS_FEE_SETTLE_TYPE	as "transFeeSettleType",  <!-- 运输费结算类型 -->
				PLAN_OUT_WARHOUSE_TIME	as "planOutWarhouseTime",  
				DEST_SPOT_TELE	as "destSpotTele",  <!-- 目的地电话 -->
				DEST_SPOT_ADDR_ID	as "destSpotAddrId",  
				ARREARS_SHIP_AMOUNT	as "arrearsShipAmount",  <!-- 欠款金额 -->
				TPROVIDER_CONTACT	as "tproviderContact",  
				ORDER_CUST_CNAME	as "orderCustCname",  
				OUT_FEE_SETTLE_TYPE	as "outFeeSettleType",  <!-- 出库费结算类型 -->
				REMARK	as "remark",  <!-- 备注 -->
				VERIFY_MAN	as "verifyMan",  
				REQUIRE_FINISH_DATE	as "requireFinishDate",  
				CONSIGN_BILL_STATUS	as "consignBillStatus",  
				CONSIGNEE_NAME	as "consigneeName",  <!-- 收货方名称 -->
				BUSINESS_NO	as "businessNo",  
				EMERGENCY_DELIVERY_TIME	as "emergencyDeliveryTime",  
				DEST_SPOT_ID	as "destSpotId",  <!-- 目的地ID -->
				PLAN_LOAD_WT	as "planLoadWt",  <!-- 计划装载重量 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				TPROVIDER_CONTACT_PLATE	as "tproviderContactPlate",  
				VEHICLE_ID	as "vehicleId",  
				ACTIVE_START_TIME	as "activeStartTime",  <!-- 有效开始时间 -->
				DELIVERY_TYPE	as "deliveryType",  <!-- 交付类型 -->
				OUT_PACK_QTY	as "outPackQty",  <!-- 出库包装数量 -->
				LADING_SPOT_NAME	as "ladingSpotName",  <!-- 装货地名称 -->
				TPROVIDER_CONTACT_MOBILE	as "tproviderContactMobile",  
				TRANS_NO	as "transNo",  
				USER_NAME	as "userName",  <!-- 用户名称 -->
				LADING_SPOT_TELE	as "ladingSpotTele",  <!-- 装货地电话 -->
				PRINT_NUMBER	as "printNumber",  <!-- 打印次数 -->
				DEST_COUNTRY	as "destCountry",  
				TPROVIDER_CONTACT_MOBILE2	as "tproviderContactMobile2",  
				TPROVIDER_NAME	as "tproviderName",  <!-- 运输商名称 -->
				TOTAL_WEIGHT	as "totalWeight",  <!-- 总重量 -->
				DRIVER_PHONE	as "driverPhone" 
		FROM ${meliSchema}.tlirl0599 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
    		  UUID asc
		</isEmpty>
  		</dynamic>
	</select>

	<select id="count" resultClass="int" parameterClass="java.util.HashMap">
		SELECT COUNT(*) FROM ${meliSchema}.tlirl0599 WHERE 1=1
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
	</select>

	<insert id="insert">
		INSERT INTO ${meliSchema}.tlirl0599 (ACT_CONSIGNEE_NAME,  <!-- 实际收货方名称 -->
										LADING_SPOT_ADDR,  <!-- 装货地点地址 -->
										TOTAL_PACK_QTY,  <!-- 总包装数量 -->
										TRANSFER_NOTI_SYSTEM,
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										PROCESS_FACTORY_NAME,
										PRIVATE_ROUTE_NAME,
										LADING_BILL_ID,  <!-- 提单号（业务主键） -->
										TENANT_USER,
										DEST_SPOT_CONTACTOR,  <!-- 目的地联系人 -->
										DELIVERY_DATE_TIME,  <!-- 发货日期 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										PRINT_MAN,
										UNIT_CODE,  <!-- 单位编码 -->
										PRINT_BATCH_ID,
										VERIFI_SOURCE,
										NO_PAPER_FLAG,  <!-- 无纸化标志 -->
										REC_REVISOR_NAME,  <!-- 记录修改人 -->
										DEST_SPOT_NAME,  <!-- 目的地名称 -->
										BILLING_METHOD,  <!-- 计费方式代码 -->
										PRINT_DATE,
										LOGISTIC_SCHEME_NO,
										FIN_USER_ID,  <!-- 财务用户ID -->
										BILL_EXPI_PROC_MARK,  <!-- 票据处理标志 -->
										LADING_SPOT_ID,  <!-- 装货地ID -->
										REC_CREATOR,  <!-- 记录创建人ID -->
										VERIFI_DATE,
										DRIVER,
										DRIVER_ID,
										D_USER_NAME,
										SYN_FLAG,
										VEHICLE_NAME,
										RAIN_COAT_FLAG,
										ACT_CONSIGNEE_ID,  <!-- 实际收货方ID -->
										DEL_FLAG,  <!-- 删除标志（0未删除） -->
										SHIP_VERIFI_CODE,
										VERIFI_PERSON,
										ENTRUST_FLAG,
										LADING_SPOT_ADDR_ID,  <!-- 装货地址ID -->
										MANUAL_NO,  <!-- 手工单号 -->
										BILL_BANK_BATCH_ID,
										STORE_TYPE,
										BL_TOT_AMT,  <!-- 总金额 -->
										CONSIGNEE_TYPE,  <!-- 收货方类型 -->
										D_USER_NUM,  <!-- 用户编号 -->
										BILLING_TIME,  <!-- 开单时间 -->
										APPLY_NUM,  <!-- 申请数量 -->
										ARREAR_TYPE,
										SEG_NO,  <!-- 段号 -->
										PRINT_DATE_FIRST,
										OUT_WEIGHT,  <!-- 出库重量 -->
										DRIVER_PHONE2,
										CONTRACT_NUM,  <!-- 合同号 -->
										EXPIRY_DAYS,  <!-- 有效期天数 -->
										TRANSFER_OBJECT,
										CONSIGNEE_NUM,  <!-- 收货方编号 -->
										RESOURCE_TYPE,
										TRANSFER_NOTI_DESC,
										VERIFI_STATUS,  <!-- 核验状态 -->
										LADING_SPOT_CONTACTOR,  <!-- 装货地联系人 -->
										USER_NUM,  <!-- 用户编号 -->
										ACTIVE_END_TIME,  <!-- 有效截止时间 -->
										PLAN_LOAD_QTY,  <!-- 计划装载量 -->
										STORE_SETTLE_TYPE,
										ARCHIVE_FLAG,
										CONSIGN_ID,
										UUID,  <!-- 唯一标识（主键） -->
										CONSIGN_MARK,
										REC_REVISOR,  <!-- 记录修改人ID -->
										DEST_SPOT_ADDR,  <!-- 目的地地址 -->
										LADING_BILL_TYPE,  <!-- 提单类型 -->
										LADING_BILL_STATUS,  <!-- 提单状态 -->
										PROCESS_FACTORY_ID,
										PLAN_IN_WARHOUSE_TIME,
										USER_NAME_ABBR,
										VERIFY_DATE,
										STORE_FEE_SETTLE_TYPE,  <!-- 仓储费结算类型 -->
										PRIVATE_ROUTE_CODE,
										TRADE_CODE,  <!-- 贸易代码 -->
										VEHICLE_CODE,
										BILL_BANK_STATUS,
										VEHICLE_NO,  <!-- 车牌号 -->
										TRANS_TYPE,  <!-- 运输类型 -->
										TPROVIDER_CONTACT_ID,
										FINANCE_CONTRACT,
										FIN_USER_NAME,  <!-- 财务用户名称 -->
										ARREARS_SHIP_NO,
										TPROVIDER_ID,  <!-- 运输商ID -->
										TRANS_FEE_SETTLE_TYPE,  <!-- 运输费结算类型 -->
										PLAN_OUT_WARHOUSE_TIME,
										DEST_SPOT_TELE,  <!-- 目的地电话 -->
										DEST_SPOT_ADDR_ID,
										ARREARS_SHIP_AMOUNT,  <!-- 欠款金额 -->
										TPROVIDER_CONTACT,
										ORDER_CUST_CNAME,
										OUT_FEE_SETTLE_TYPE,  <!-- 出库费结算类型 -->
										REMARK,  <!-- 备注 -->
										VERIFY_MAN,
										REQUIRE_FINISH_DATE,
										CONSIGN_BILL_STATUS,
										CONSIGNEE_NAME,  <!-- 收货方名称 -->
										BUSINESS_NO,
										EMERGENCY_DELIVERY_TIME,
										DEST_SPOT_ID,  <!-- 目的地ID -->
										PLAN_LOAD_WT,  <!-- 计划装载重量 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										TPROVIDER_CONTACT_PLATE,
										VEHICLE_ID,
										ACTIVE_START_TIME,  <!-- 有效开始时间 -->
										DELIVERY_TYPE,  <!-- 交付类型 -->
										OUT_PACK_QTY,  <!-- 出库包装数量 -->
										LADING_SPOT_NAME,  <!-- 装货地名称 -->
										TPROVIDER_CONTACT_MOBILE,
										TRANS_NO,
										USER_NAME,  <!-- 用户名称 -->
										LADING_SPOT_TELE,  <!-- 装货地电话 -->
										PRINT_NUMBER,  <!-- 打印次数 -->
										DEST_COUNTRY,
										TPROVIDER_CONTACT_MOBILE2,
										TPROVIDER_NAME,  <!-- 运输商名称 -->
										TOTAL_WEIGHT,  <!-- 总重量 -->
										DRIVER_PHONE
										)		 
	    VALUES (#actConsigneeName#, #ladingSpotAddr#, #totalPackQty#, #transferNotiSystem#, #recReviseTime#, #processFactoryName#, #privateRouteName#, #ladingBillId#, #tenantUser#, #destSpotContactor#, #deliveryDateTime#, #recCreatorName#, #printMan#, #unitCode#, #printBatchId#, #verifiSource#, #noPaperFlag#, #recRevisorName#, #destSpotName#, #billingMethod#, #printDate#, #logisticSchemeNo#, #finUserId#, #billExpiProcMark#, #ladingSpotId#, #recCreator#, #verifiDate#, #driver#, #driverId#, #d_userName#, #synFlag#, #vehicleName#, #rainCoatFlag#, #actConsigneeId#, #delFlag#, #shipVerifiCode#, #verifiPerson#, #entrustFlag#, #ladingSpotAddrId#, #manualNo#, #billBankBatchId#, #storeType#, #blTotAmt#, #consigneeType#, #d_userNum#, #billingTime#, #applyNum#, #arrearType#, #segNo#, #printDateFirst#, #outWeight#, #driverPhone2#, #contractNum#, #expiryDays#, #transferObject#, #consigneeNum#, #resourceType#, #transferNotiDesc#, #verifiStatus#, #ladingSpotContactor#, #userNum#, #activeEndTime#, #planLoadQty#, #storeSettleType#, #archiveFlag#, #consignId#, #uuid#, #consignMark#, #recRevisor#, #destSpotAddr#, #ladingBillType#, #ladingBillStatus#, #processFactoryId#, #planInWarhouseTime#, #userNameAbbr#, #verifyDate#, #storeFeeSettleType#, #privateRouteCode#, #tradeCode#, #vehicleCode#, #billBankStatus#, #vehicleNo#, #transType#, #tproviderContactId#, #financeContract#, #finUserName#, #arrearsShipNo#, #tproviderId#, #transFeeSettleType#, #planOutWarhouseTime#, #destSpotTele#, #destSpotAddrId#, #arrearsShipAmount#, #tproviderContact#, #orderCustCname#, #outFeeSettleType#, #remark#, #verifyMan#, #requireFinishDate#, #consignBillStatus#, #consigneeName#, #businessNo#, #emergencyDeliveryTime#, #destSpotId#, #planLoadWt#, #recCreateTime#, #tproviderContactPlate#, #vehicleId#, #activeStartTime#, #deliveryType#, #outPackQty#, #ladingSpotName#, #tproviderContactMobile#, #transNo#, #userName#, #ladingSpotTele#, #printNumber#, #destCountry#, #tproviderContactMobile2#, #tproviderName#, #totalWeight#, #driverPhone#) 
	</insert>
  
	<delete id="delete">
		DELETE FROM ${meliSchema}.tlirl0599 WHERE 
			1=1
		and SEG_NO = #segNo#
	</delete>

	<update id="update">
		UPDATE ${meliSchema}.tlirl0599 
		SET 
		ACT_CONSIGNEE_NAME	= #actConsigneeName#,   <!-- 实际收货方名称 -->  
					LADING_SPOT_ADDR	= #ladingSpotAddr#,   <!-- 装货地点地址 -->  
					TOTAL_PACK_QTY	= #totalPackQty#,   <!-- 总包装数量 -->  
					TRANSFER_NOTI_SYSTEM	= #transferNotiSystem#,   
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					PROCESS_FACTORY_NAME	= #processFactoryName#,   
					PRIVATE_ROUTE_NAME	= #privateRouteName#,   
					LADING_BILL_ID	= #ladingBillId#,   <!-- 提单号（业务主键） -->  
					TENANT_USER	= #tenantUser#,   
					DEST_SPOT_CONTACTOR	= #destSpotContactor#,   <!-- 目的地联系人 -->  
					DELIVERY_DATE_TIME	= #deliveryDateTime#,   <!-- 发货日期 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					PRINT_MAN	= #printMan#,   
					UNIT_CODE	= #unitCode#,   <!-- 单位编码 -->  
					PRINT_BATCH_ID	= #printBatchId#,   
					VERIFI_SOURCE	= #verifiSource#,   
					NO_PAPER_FLAG	= #noPaperFlag#,   <!-- 无纸化标志 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人 -->  
					DEST_SPOT_NAME	= #destSpotName#,   <!-- 目的地名称 -->  
					BILLING_METHOD	= #billingMethod#,   <!-- 计费方式代码 -->  
					PRINT_DATE	= #printDate#,   
					LOGISTIC_SCHEME_NO	= #logisticSchemeNo#,   
					FIN_USER_ID	= #finUserId#,   <!-- 财务用户ID -->  
					BILL_EXPI_PROC_MARK	= #billExpiProcMark#,   <!-- 票据处理标志 -->  
					LADING_SPOT_ID	= #ladingSpotId#,   <!-- 装货地ID -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人ID -->  
					VERIFI_DATE	= #verifiDate#,   
					DRIVER	= #driver#,   
					DRIVER_ID	= #driverId#,   
					D_USER_NAME	= #d_userName#,   
					SYN_FLAG	= #synFlag#,   
					VEHICLE_NAME	= #vehicleName#,   
					RAIN_COAT_FLAG	= #rainCoatFlag#,   
					ACT_CONSIGNEE_ID	= #actConsigneeId#,   <!-- 实际收货方ID -->  
					DEL_FLAG	= #delFlag#,   <!-- 删除标志（0未删除） -->  
					SHIP_VERIFI_CODE	= #shipVerifiCode#,   
					VERIFI_PERSON	= #verifiPerson#,   
					ENTRUST_FLAG	= #entrustFlag#,   
					LADING_SPOT_ADDR_ID	= #ladingSpotAddrId#,   <!-- 装货地址ID -->  
					MANUAL_NO	= #manualNo#,   <!-- 手工单号 -->  
					BILL_BANK_BATCH_ID	= #billBankBatchId#,   
					STORE_TYPE	= #storeType#,   
					BL_TOT_AMT	= #blTotAmt#,   <!-- 总金额 -->  
					CONSIGNEE_TYPE	= #consigneeType#,   <!-- 收货方类型 -->  
					D_USER_NUM	= #d_userNum#,   <!-- 用户编号 -->  
					BILLING_TIME	= #billingTime#,   <!-- 开单时间 -->  
					APPLY_NUM	= #applyNum#,   <!-- 申请数量 -->  
					ARREAR_TYPE	= #arrearType#,   
					SEG_NO	= #segNo#,   <!-- 段号 -->  
					PRINT_DATE_FIRST	= #printDateFirst#,   
					OUT_WEIGHT	= #outWeight#,   <!-- 出库重量 -->  
					DRIVER_PHONE2	= #driverPhone2#,   
					CONTRACT_NUM	= #contractNum#,   <!-- 合同号 -->  
					EXPIRY_DAYS	= #expiryDays#,   <!-- 有效期天数 -->  
					TRANSFER_OBJECT	= #transferObject#,   
					CONSIGNEE_NUM	= #consigneeNum#,   <!-- 收货方编号 -->  
					RESOURCE_TYPE	= #resourceType#,   
					TRANSFER_NOTI_DESC	= #transferNotiDesc#,   
					VERIFI_STATUS	= #verifiStatus#,   <!-- 核验状态 -->  
					LADING_SPOT_CONTACTOR	= #ladingSpotContactor#,   <!-- 装货地联系人 -->  
					USER_NUM	= #userNum#,   <!-- 用户编号 -->  
					ACTIVE_END_TIME	= #activeEndTime#,   <!-- 有效截止时间 -->  
					PLAN_LOAD_QTY	= #planLoadQty#,   <!-- 计划装载量 -->  
					STORE_SETTLE_TYPE	= #storeSettleType#,   
					ARCHIVE_FLAG	= #archiveFlag#,   
					CONSIGN_ID	= #consignId#,   
								CONSIGN_MARK	= #consignMark#,   
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人ID -->  
					DEST_SPOT_ADDR	= #destSpotAddr#,   <!-- 目的地地址 -->  
					LADING_BILL_TYPE	= #ladingBillType#,   <!-- 提单类型 -->  
					LADING_BILL_STATUS	= #ladingBillStatus#,   <!-- 提单状态 -->  
					PROCESS_FACTORY_ID	= #processFactoryId#,   
					PLAN_IN_WARHOUSE_TIME	= #planInWarhouseTime#,   
					USER_NAME_ABBR	= #userNameAbbr#,   
					VERIFY_DATE	= #verifyDate#,   
					STORE_FEE_SETTLE_TYPE	= #storeFeeSettleType#,   <!-- 仓储费结算类型 -->  
					PRIVATE_ROUTE_CODE	= #privateRouteCode#,   
					TRADE_CODE	= #tradeCode#,   <!-- 贸易代码 -->  
					VEHICLE_CODE	= #vehicleCode#,   
					BILL_BANK_STATUS	= #billBankStatus#,   
					VEHICLE_NO	= #vehicleNo#,   <!-- 车牌号 -->  
					TRANS_TYPE	= #transType#,   <!-- 运输类型 -->  
					TPROVIDER_CONTACT_ID	= #tproviderContactId#,   
					FINANCE_CONTRACT	= #financeContract#,   
					FIN_USER_NAME	= #finUserName#,   <!-- 财务用户名称 -->  
					ARREARS_SHIP_NO	= #arrearsShipNo#,   
					TPROVIDER_ID	= #tproviderId#,   <!-- 运输商ID -->  
					TRANS_FEE_SETTLE_TYPE	= #transFeeSettleType#,   <!-- 运输费结算类型 -->  
					PLAN_OUT_WARHOUSE_TIME	= #planOutWarhouseTime#,   
					DEST_SPOT_TELE	= #destSpotTele#,   <!-- 目的地电话 -->  
					DEST_SPOT_ADDR_ID	= #destSpotAddrId#,   
					ARREARS_SHIP_AMOUNT	= #arrearsShipAmount#,   <!-- 欠款金额 -->  
					TPROVIDER_CONTACT	= #tproviderContact#,   
					ORDER_CUST_CNAME	= #orderCustCname#,   
					OUT_FEE_SETTLE_TYPE	= #outFeeSettleType#,   <!-- 出库费结算类型 -->  
					REMARK	= #remark#,   <!-- 备注 -->  
					VERIFY_MAN	= #verifyMan#,   
					REQUIRE_FINISH_DATE	= #requireFinishDate#,   
					CONSIGN_BILL_STATUS	= #consignBillStatus#,   
					CONSIGNEE_NAME	= #consigneeName#,   <!-- 收货方名称 -->  
					BUSINESS_NO	= #businessNo#,   
					EMERGENCY_DELIVERY_TIME	= #emergencyDeliveryTime#,   
					DEST_SPOT_ID	= #destSpotId#,   <!-- 目的地ID -->  
					PLAN_LOAD_WT	= #planLoadWt#,   <!-- 计划装载重量 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					TPROVIDER_CONTACT_PLATE	= #tproviderContactPlate#,   
					VEHICLE_ID	= #vehicleId#,   
					ACTIVE_START_TIME	= #activeStartTime#,   <!-- 有效开始时间 -->  
					DELIVERY_TYPE	= #deliveryType#,   <!-- 交付类型 -->  
					OUT_PACK_QTY	= #outPackQty#,   <!-- 出库包装数量 -->  
					LADING_SPOT_NAME	= #ladingSpotName#,   <!-- 装货地名称 -->  
					TPROVIDER_CONTACT_MOBILE	= #tproviderContactMobile#,   
					TRANS_NO	= #transNo#,   
					USER_NAME	= #userName#,   <!-- 用户名称 -->  
					LADING_SPOT_TELE	= #ladingSpotTele#,   <!-- 装货地电话 -->  
					PRINT_NUMBER	= #printNumber#,   <!-- 打印次数 -->  
					DEST_COUNTRY	= #destCountry#,   
					TPROVIDER_CONTACT_MOBILE2	= #tproviderContactMobile2#,   
					TPROVIDER_NAME	= #tproviderName#,   <!-- 运输商名称 -->  
					TOTAL_WEIGHT	= #totalWeight#,   <!-- 总重量 -->  
					DRIVER_PHONE	= #driverPhone#  
			WHERE 	
			UUID = #uuid#
	</update>

	<select id="querLadingBillId" resultClass="java.util.HashMap">

		select *
		from ${meliSchema}.v_bill_info t
		where t.segNo = #segNo#


	</select>

  
</sqlMap>