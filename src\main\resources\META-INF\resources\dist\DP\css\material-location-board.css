/* 原料库位看板专用样式 */

.datetime {
    position: absolute;
    right: 20px;
    bottom: 10px;
    color: #FFF;
    font-size: 18px;
    font-weight: bold;
}

.board-container {
    width: 100%;
    height: calc(89% - 20px);
    display: flex;
    flex-direction: column;
    padding: 10px;
    box-sizing: border-box;
}

.areas-container {
    flex: 1;
    display: flex;
    gap: 15px;
    overflow: hidden;
}

/* 区域样式 */
.area-section {
    flex: 1;
    background: rgba(0, 112, 192, 0.1);
    border: 2px solid #0070C0;
    border-radius: 10px;
    padding: 15px;
    display: flex;
    flex-direction: column;
    min-width: 0; /* 确保flex子元素可以收缩 */
}

/* 区域标题容器 */
.area-header {
    position: relative;
    background: #0070C0;
    border-radius: 5px;
    margin-bottom: 15px;
    padding: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.area-label {
    color: #FFF;
    font-size: 28px;
    font-weight: bold;
    text-align: center;
    letter-spacing: 2px;
    flex: 1;
}

/* 区域页面信息样式 */
.area-page-info {
    font-size: 16px;
    font-weight: normal;
    color: #E0E6ED;
    margin-left: 5px;
}

/* 区域占用量信息样式 */
.area-occupancy-info {
    position: absolute;
    top: 6px;
    right: 10px;
    /* 移除所有背景和边框样式 */
    /* background: rgba(255, 255, 255, 0.15); */
    /* border: 2px solid rgba(255, 255, 255, 0.3); */
    /* border-radius: 8px; */
    /* padding: 6px 10px; */
    /* backdrop-filter: blur(10px); */
    /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2); */
    /* transition: all 0.3s ease; */
    font-size: 12px;
    font-weight: 600;
    color: #FFFFFF;
    line-height: 1.3;
    text-align: right;
    min-width: 80px;
    font-family: 'Courier New', monospace; /* 使用等宽字体确保数字对齐 */
}

/* 移除悬停效果 */
/* .area-occupancy-info:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
} */

.occupancy-line {
    margin: 2px 0;
    white-space: nowrap;
    font-size: 11px;
    letter-spacing: 0.5px;
    text-align: right;
    font-family: 'Courier New', monospace; /* 使用等宽字体确保数字对齐 */
}

.occupancy-line:first-child {
    color: #E3F2FD; /* 淡蓝色 - 总容量 */
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.occupancy-line:nth-child(2) {
    color: #FFE082; /* 金黄色 - 占用量 */
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.occupancy-line:last-child {
    color: #A5D6A7; /* 淡绿色 - 剩余量 */
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 为占用量信息添加动画效果 */
@keyframes occupancyPulse {
    0% { opacity: 0.8; }
    50% { opacity: 1; }
    100% { opacity: 0.8; }
}

.area-occupancy-info.highlight {
    animation: occupancyPulse 2s ease-in-out infinite;
}

/* 汇总信息样式 */
.summary-info {
    position: absolute;
    top: 20px;
    right: 250px; /* 避开控制按钮区域 */
    font-size: 16px;
    font-weight: 600;
    color: #FFFFFF;
    text-align: right;
    z-index: 1000;
    font-family: 'Courier New', monospace; /* 使用等宽字体确保数字对齐 */
}

.summary-line {
    margin: 4px 0;
    white-space: nowrap;
    letter-spacing: 1px;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    text-align: right;
    font-family: 'Courier New', monospace; /* 使用等宽字体确保数字对齐 */
}

.summary-line:first-child {
    color: #E3F2FD; /* 淡蓝色 - 总库容量 */
    font-weight: 700;
}

.summary-line:nth-child(2) {
    color: #FFE082; /* 金黄色 - 当前占用 */
    font-weight: 700;
}

.summary-line:last-child {
    color: #A5D6A7; /* 淡绿色 - 剩余库容 */
    font-weight: 700;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .area-occupancy-info {
        font-size: 11px;
        padding: 4px 8px;
        min-width: 70px;
    }

    .occupancy-line {
        font-size: 10px;
    }

    .summary-info {
        font-size: 14px;
        top: 15px;
        right: 250px; /* 在小屏幕上也避开控制按钮 */
    }

    .summary-line {
        margin: 2px 0;
    }
}

.location-grid {
    flex: 1;
    display: grid;
    gap: 8px;
    align-content: start;
    justify-content: center;
}

/* 统一网格布局 - 每个区域4列 */
.location-grid {
    grid-template-columns: repeat(4, 1fr);
    grid-auto-rows: minmax(120px, auto);
    gap: 10px;
}

/* 库位方块样式 */
.location-item {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    border: 2px solid #60a5fa;
    border-radius: 12px;
    padding: 10px;
    width: 100%;
    height: 120px;
    aspect-ratio: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    min-width: 0; /* 确保可以收缩 */
}

.location-item:hover {
    transform: scale(1.02);
    box-shadow: 0 6px 20px rgba(96, 165, 250, 0.4);
    border-color: #93c5fd;
}

.location-name {
    color: #FFF;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 8px;
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 4px 8px;
    border-radius: 6px;
    min-width: 40px;
}

.location-layers {
    display: flex;
    flex-direction: column;
    gap: 4px;
    width: 100%;
}

.layer {
    height: 24px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: bold;
    color: #000;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.layer-1 {
    background: linear-gradient(135deg, #e5e7eb 0%, #f3f4f6 100%); /* 默认空闲状态 - 灰色渐变 */
}

.layer-2 {
    background: linear-gradient(135deg, #e5e7eb 0%, #f3f4f6 100%); /* 默认空闲状态 - 灰色渐变 */
}

/* 库位状态颜色 */
.layer.occupied {
    background: linear-gradient(135deg, #10b981 0%, #34d399 100%); /* 有货 - 绿色渐变 */
    color: #FFF;
    border-color: #6ee7b7;
}

.layer.reserved {
    background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%); /* 预留 - 橙色渐变 */
    color: #FFF;
    border-color: #fcd34d;
}

.layer.maintenance {
    background: linear-gradient(135deg, #ef4444 0%, #f87171 100%); /* 维护 - 红色渐变 */
    color: #FFF;
    border-color: #fca5a5;
}

.layer.empty {
    background: linear-gradient(135deg, #e5e7eb 0%, #f3f4f6 100%); /* 空闲 - 灰色渐变 */
    color: #6b7280;
    border-color: #d1d5db;
}

/* 中间过跨小车区域 */
.middle-section {
    width: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(0, 112, 192, 0.1);
    border: 2px solid #0070C0;
    border-radius: 10px;
    padding: 15px;
}

.cross-vehicle-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    width: 100%;
    margin-bottom: 20px;
}

.vehicle-lane {
    background: #2C5282;
    border: 2px solid #4A90E2;
    border-radius: 8px;
    padding: 10px;
    margin: 5px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60px;
}

.vehicle-number {
    color: #FFF;
    font-size: 14px;
    font-weight: bold;
    text-align: center;
    writing-mode: vertical-rl;
    text-orientation: mixed;
}

.cross-label {
    color: #FFF;
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    background: #0070C0;
    padding: 8px;
    border-radius: 5px;
    writing-mode: vertical-rl;
    text-orientation: mixed;
    letter-spacing: 2px;
}

/* 状态指示器 */
.status-indicator {
    position: absolute;
    top: 2px;
    right: 2px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #4CAF50;
}

.status-indicator.warning {
    background: #FF9800;
}

.status-indicator.error {
    background: #F44336;
}

/* SweetAlert自定义样式 */
.swal-custom-btn {
    background: linear-gradient(135deg, #0070C0 0%, #005a9e 100%) !important;
    border: none !important;
    border-radius: 8px !important;
    font-weight: bold !important;
}

.swal-custom-btn:hover {
    background: linear-gradient(135deg, #005a9e 0%, #004285 100%) !important;
}

/* 响应式设计 */
@media (max-width: 1400px) {
    .area-label {
        font-size: 24px;
    }
    
    .area-page-info {
        font-size: 14px;
    }
    
    .location-name {
        font-size: 12px;
    }
    
    .layer {
        height: 20px;
        font-size: 10px;
    }
    
    .location-item {
        height: 100px;
        padding: 8px;
    }
}

@media (max-width: 1200px) {
    .area-label {
        font-size: 20px;
        padding: 6px;
    }
    
    .area-page-info {
        font-size: 12px;
    }
    
    .location-name {
        font-size: 11px;
        padding: 3px 6px;
    }
    
    .layer {
        height: 18px;
        font-size: 9px;
    }
    
    .location-item {
        height: 90px;
        padding: 6px;
    }
    
    /* 在小屏幕上改为2列显示 */
    .location-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .areas-container {
        flex-direction: column;
        gap: 10px;
    }
    
    .area-section {
        min-height: 200px;
    }
    
    .location-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .location-item {
        height: 80px;
        padding: 4px;
    }
    
    .area-label {
        font-size: 18px;
        padding: 4px;
    }
    
    .location-name {
        font-size: 10px;
        padding: 2px 4px;
    }
    
    .layer {
        height: 16px;
        font-size: 8px;
    }
}

/* 控制按钮样式 */
.control-buttons {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    gap: 10px;
    z-index: 1000;
}

.control-btn {
    background: linear-gradient(135deg, #0070C0 0%, #005a9e 100%);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: linear-gradient(135deg, #005a9e 0%, #004285 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.control-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
} 