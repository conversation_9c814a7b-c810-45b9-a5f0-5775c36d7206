<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="VGDM0801">

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = '0'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="overhaulQuality">
            OVERHAUL_QUALITY = #overhaulQuality#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="overhaulPlanStatus">
            OVERHAUL_PLAN_STATUS = #overhaulPlanStatus#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="apprStatus">
            APPR_STATUS = #apprStatus#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="exceptionContactId">
            EXCEPTION_CONTACT_ID = #exceptionContactId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="voucherNum">
            VOUCHER_NUM like concat('%',#voucherNum#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="overhaulPlanId">
            OVERHAUL_PLAN_ID like concat('%',#overhaulPlanId#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="equalId">
            OVERHAUL_PLAN_ID = #equalId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="equipmentName">
            EQUIPMENT_NAME like concat('%',#equipmentName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="deviceName">
            DEVICE_NAME like concat('%',#deviceName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="overhaulStartDate">
            OVERHAUL_START_DATE &gt;= #overhaulStartDate#
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="overhaulEndDate">
            #overhaulEndDate# &gt;= OVERHAUL_END_DATE
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="recCreateTimeStart">
            substr(REC_CREATE_TIME,1,8) &gt;= replace(#recCreateTimeStart#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="recCreateTimeEnd">
            replace(#recCreateTimeEnd#,'-','') &gt;= substr(REC_CREATE_TIME,1,8)
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="minStatus">
            OVERHAUL_PLAN_STATUS &gt;= #minStatus#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="processInstanceId">
            PROCESS_INSTANCE_ID = #processInstanceId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="eArchivesNo">
            E_ARCHIVES_NO = #eArchivesNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="notUuid">
            UUID != #notUuid#
        </isNotEmpty>
        <!--根据登录人查询待办-->
        <isNotEmpty property="loginId" prepend="and">
            <isEqual property="apprStatus" compareValue="60">
                <!--待审批-->
                EXISTS(SELECT 1
                FROM ${platSchema}.TEWPT00 T
                WHERE T.PROCESS_INSTANCE_ID = ${mevgSchema}.TVGDM0801.PROCESS_INSTANCE_ID
                AND T.ASSIGNEE_ID = #loginId#
                AND T.PROCESS_KEY = 'overhaulPlanAudit'
                AND t.STATE = 'open')
            </isEqual>
            <isEqual property="apprStatus" compareValue="70">
                <!--审核通过-->
                EXISTS(SELECT 1
                FROM ${platSchema}.HEWPT00 T
                WHERE T.PROCESS_INSTANCE_ID = ${mevgSchema}.TVGDM0801.PROCESS_INSTANCE_ID
                AND T.COMPLETER_ID = #loginId#
                and T.PROCESS_KEY = 'overhaulPlanAudit'
                and T.APPROVAL_RESULT = 'grant'
                and t.STATE = 'completed'
                )
            </isEqual>
            <isEqual property="apprStatus" compareValue="7X">
                <!--审核驳回-->
                EXISTS(
                SELECT 1
                FROM ${platSchema}.TEWPT00 T
                WHERE T.PROCESS_INSTANCE_ID = ${mevgSchema}.TVGDM0801.PROCESS_INSTANCE_ID
                AND T.COMPLETER_ID = #loginId#
                AND T.PROCESS_KEY = 'overhaulPlanAudit'
                AND T.APPROVAL_RESULT ='reject'
                AND t.STATE = 'completed'
                )
            </isEqual>
            <isEqual property="apprStatus1" compareValue="10">
                <!--全部-->
                EXISTS(SELECT 1
                FROM ${platSchema}.TEWPT00 T
                WHERE T.PROCESS_INSTANCE_ID = ${mevgSchema}.TVGDM0801.PROCESS_INSTANCE_ID
                AND T.ASSIGNEE_ID = #loginId#
                AND T.PROCESS_KEY = 'overhaulPlanAudit'
                )
            </isEqual>
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM0801">
        SELECT
        OVERHAUL_PLAN_ID as "overhaulPlanId",  <!-- 检修计划编号 -->
        E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        E_ARCHIVES_NO as "e_archivesNo",  <!-- 设备档案编号 -->
        EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        OVERHAUL_QUALITY as "overhaulQuality",  <!-- 检修性质 -->
        OVERHAUL_TYPE as "overhaulType",  <!-- 检修类别 -->
        OVERHAUL_START_DATE as "overhaulStartDate",  <!-- 计划检修开始日期 -->
        OVERHAUL_END_DATE as "overhaulEndDate",  <!-- 计划检修结束日期 -->
        OVERHAUL_NUMBER as "overhaulNumber",  <!-- 计划检修人数 -->
        OVERHAUL_TIME as "overhaulTime",  <!-- 计划检修时间 -->
        OVERHAUL_PROJECT as "overhaulProject",  <!-- 计划检修项目 -->
        EXCEPTION_CONTACT_ID as "exceptionContactId",  <!-- 异常联络单号 -->
        OVERHAUL_SOURCE as "overhaulSource",  <!-- 检修来源 -->
        OUTSOURCING_CONTACT_ID as "outsourcingContactId",  <!-- 委外联络单号 -->
        SECURITY_MEASURES as "securityMeasures",  <!-- 安全措施 -->
        ACCEPTANCE_CRITERIA as "acceptanceCriteria",  <!-- 检修验收标准 -->
        IMPLEMENT_MAN_ID as "implementManId",  <!-- 实施人 -->
        IMPLEMENT_MAN_NAME as "implementManName",  <!-- 实施人姓名 -->
        OVERHAUL_PLAN_STATUS as "overhaulPlanStatus",  <!-- 检修计划状态 -->
        OVERHAUL_IMPLEMENT_DATE as "overhaulImplementDate",  <!-- 检修实施日期 -->
        ACTUAL_OVERHAUL_NUMBER as "actualOverhaulNumber",  <!-- 实际检修人数 -->
        ACTUAL_OVERHAUL_TIME as "actualOverhaulTime",  <!-- 实际检修时间 -->
        IS_COMPLETE as "isComplete",  <!-- 是否完成 -->
        OVERHAUL_LEGACY_PROJECT as "overhaulLegacyProject",  <!-- 遗留检修项目 -->
        IS_CONFORM_STANDARD as "isConformStandard",  <!-- 是否符合标准 -->
        RELEVANT_MEASURES as "relevantMeasures",  <!-- 相关措施 -->
        IS_HOT as "isHot",  <!-- 是否动火 -->
        HOT_CARD_ID as "hotCardId",  <!-- 动火证编号 -->
        OFFLINE_PARTS_GONE as "offlinePartsGone",  <!-- 下线零件去向 -->
        ACTUALS_REVISOR as "actualsRevisor",  <!-- 检修实绩操作人 -->
        ACTUALS_TIME as "actualsTime",  <!-- 检修实绩操作时间 -->
        ACTUALS_REVISOR_NAME as "actualsRevisorName",  <!-- 检修实绩操作人姓名 -->
        VOUCHER_NUM as "voucherNum",  <!-- 依据凭单 -->
        APPR_STATUS as "apprStatus",  <!-- 审批状态 -->
        ACTUAL_LEGACY_PROJECT as "actualLegacyProject",  <!-- 实际检修项目 -->
        PROCESS_INSTANCE_ID as "processInstanceId",  <!-- 工作流实例ID -->
        PROCESS_INSTANCE_ID as "processId",  <!-- 工作流实例ID -->
        OVERHAUL_SUGGESTIONS as "overhaulSuggestions",  <!-- 问题及建议 -->
        OVERHAUL_SUMMARIZE as "overhaulSummarize",  <!-- 检修总结 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        (select SEG_NAME from iplat4j.tvzbm81 t where t.SEG_NO = tvgdm0801.UNIT_CODE) as "unitName",
        DEVICE_CODE as "deviceCode",  <!-- 分部设备代码 -->
        DEVICE_NAME as "deviceName",  <!-- 分部设备名称 -->
        DELAY_REMARK as "delayRemark"  <!-- 延期备注 -->
        FROM ${mevgSchema}.TVGDM0801 WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>

    </select>

    <select id="queryForMobile" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM0801">
        SELECT
        OVERHAUL_PLAN_ID as "overhaulPlanId",  <!-- 检修计划编号 -->
        E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        E_ARCHIVES_NO as "e_archivesNo",  <!-- 设备档案编号 -->
        EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        (select ITEM_CNAME from iplat4j.tedcm01 tedcm01
        where tedcm01.ITEM_CODE = OVERHAUL_QUALITY
        and tedcm01.CODESET_CODE = 'P024') as "overhaulQuality",  <!-- 检修性质 -->
        (select ITEM_CNAME from iplat4j.tedcm01 tedcm01
        where tedcm01.ITEM_CODE = OVERHAUL_TYPE
        and tedcm01.CODESET_CODE = 'P025') as "overhaulType",  <!-- 检修类别 -->
        OVERHAUL_START_DATE as "overhaulStartDate",  <!-- 计划检修开始日期 -->
        OVERHAUL_END_DATE as "overhaulEndDate",  <!-- 计划检修结束日期 -->
        OVERHAUL_NUMBER as "overhaulNumber",  <!-- 计划检修人数 -->
        OVERHAUL_TIME as "overhaulTime",  <!-- 计划检修时间 -->
        OVERHAUL_PROJECT as "overhaulProject",  <!-- 计划检修项目 -->
        EXCEPTION_CONTACT_ID as "exceptionContactId",  <!-- 异常联络单号 -->
        (select ITEM_CNAME from iplat4j.tedcm01 tedcm01
        where tedcm01.ITEM_CODE = OVERHAUL_SOURCE
        and tedcm01.CODESET_CODE = 'P026') as "overhaulSource",  <!-- 检修来源 -->
        OUTSOURCING_CONTACT_ID as "outsourcingContactId",  <!-- 委外联络单号 -->
        SECURITY_MEASURES as "securityMeasures",  <!-- 安全措施 -->
        ACCEPTANCE_CRITERIA as "acceptanceCriteria",  <!-- 检修验收标准 -->
        IMPLEMENT_MAN_ID as "implementManId",  <!-- 实施人 -->
        IMPLEMENT_MAN_NAME as "implementManName",  <!-- 实施人姓名 -->
        (select ITEM_CNAME from iplat4j.tedcm01 tedcm01
        where tedcm01.ITEM_CODE = OVERHAUL_PLAN_STATUS
        and tedcm01.CODESET_CODE = 'P023') as "overhaulPlanStatus",  <!-- 检修计划状态 -->
        OVERHAUL_IMPLEMENT_DATE as "overhaulImplementDate",  <!-- 检修实施日期 -->
        ACTUAL_OVERHAUL_NUMBER as "actualOverhaulNumber",  <!-- 实际检修人数 -->
        ACTUAL_OVERHAUL_TIME as "actualOverhaulTime",  <!-- 实际检修时间 -->
        IS_COMPLETE as "isComplete",  <!-- 是否完成 -->
        OVERHAUL_LEGACY_PROJECT as "overhaulLegacyProject",  <!-- 遗留检修项目 -->
        IS_CONFORM_STANDARD as "isConformStandard",  <!-- 是否符合标准 -->
        RELEVANT_MEASURES as "relevantMeasures",  <!-- 相关措施 -->
        IS_HOT as "isHot",  <!-- 是否动火 -->
        HOT_CARD_ID as "hotCardId",  <!-- 动火证编号 -->
        OFFLINE_PARTS_GONE as "offlinePartsGone",  <!-- 下线零件去向 -->
        ACTUALS_REVISOR as "actualsRevisor",  <!-- 检修实绩操作人 -->
        ACTUALS_TIME as "actualsTime",  <!-- 检修实绩操作时间 -->
        ACTUALS_REVISOR_NAME as "actualsRevisorName",  <!-- 检修实绩操作人姓名 -->
        VOUCHER_NUM as "voucherNum",  <!-- 依据凭单 -->
        CASE APPR_STATUS
        WHEN 60 THEN '审批中'
        WHEN 70 THEN '审批通过'
        WHEN '7X' THEN '驳回'
        ELSE ' '
        END  as "apprStatus",  <!-- 审批状态 -->
        ACTUAL_LEGACY_PROJECT as "actualLegacyProject",  <!-- 实际检修项目 -->
        PROCESS_INSTANCE_ID as "processInstanceId",  <!-- 工作流实例ID -->
        PROCESS_INSTANCE_ID as "processId",  <!-- 工作流实例ID -->
        OVERHAUL_SUGGESTIONS as "overhaulSuggestions",  <!-- 问题及建议 -->
        OVERHAUL_SUMMARIZE as "overhaulSummarize",  <!-- 检修总结 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        (select SEG_NAME from iplat4j.tvzbm81 t where t.SEG_NO = tvgdm0801.UNIT_CODE) as "unitName",
        DEVICE_CODE as "deviceCode",  <!-- 分部设备代码 -->
        DEVICE_NAME as "deviceName",  <!-- 分部设备名称 -->
        DELAY_REMARK as "delayRemark"  <!-- 延期备注 -->
        FROM ${mevgSchema}.TVGDM0801 WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM0801 WHERE 1=1
        <include refid="condition"/>
    </select>

    <select id="queryForSchedule" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        OVERHAUL_START_DATE as "overhaulStartDate",  <!-- 计划检修开始日期 -->
        OVERHAUL_END_DATE as "overhaulEndDate"  <!-- 计划检修结束日期 -->
        FROM ${mevgSchema}.TVGDM0801 WHERE
        SEG_NO = #segNo#
        AND OVERHAUL_START_DATE &gt;= #startDate#
        AND OVERHAUL_START_DATE &lt; #endDate#
        AND E_ARCHIVES_NO = #eArchivesNo#
        AND OVERHAUL_PLAN_STATUS in ('20','30')
        ORDER BY OVERHAUL_START_DATE
    </select>

    <select id="queryForIMC" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        T1.E_ARCHIVES_NO as "e_archivesNo",  <!-- 设备档案编号 -->
        T1.OVERHAUL_START_DATE as "examineStartTime",  <!-- 计划检修开始日期 -->
        T1.OVERHAUL_END_DATE as "examineEndTime",  <!-- 计划检修结束日期 -->
        T1.UUID as "uuid",  <!-- 唯一编码 -->
        T1.SEG_NO as "segNo",  <!-- 系统帐套 -->
        T1.UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        T2.MACHINE_CODE as "machineCode"  <!-- 机组代码 -->
        FROM ${mevgSchema}.TVGDM0801 T1
        LEFT JOIN ${meliSchema}.TLIDS0701 T2 ON T1.E_ARCHIVES_NO = T2.E_ARCHIVES_NO
        AND T1.SEG_NO = T2.SEG_NO
        AND T2.STATUS = '10'
        AND T2.DEL_FLAG = 0
        WHERE
        T1.UUID in
        <iterate property="uuids" conjunction="," open="(" close=")">
            #uuids[]#
        </iterate>
    </select>

    <select id="queryEquipment" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        MAX(EQUIPMENT_NAME) as "equipmentName",  <!-- 设备名称 -->
        MAX(UUID) as "uuid"  <!-- 唯一编码 -->
        FROM ${mevgSchema}.TVGDM0801 WHERE 1=1
        AND SEG_NO = #segNo#
        AND DEL_FLAG = '0'
        AND OVERHAUL_PLAN_STATUS in ('20','30')
        GROUP BY E_ARCHIVES_NO
        ORDER BY E_ARCHIVES_NO
    </select>

    <select id="queryForPda" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        OVERHAUL_PLAN_ID as "overhaulPlanId",  <!-- 检修计划编号 -->
        E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        OVERHAUL_QUALITY as "overhaulQuality",  <!-- 检修性质 -->
        OVERHAUL_TYPE as "overhaulType",  <!-- 检修类别 -->
        OVERHAUL_START_DATE as "overhaulStartDate",  <!-- 计划检修开始日期 -->
        OVERHAUL_END_DATE as "overhaulEndDate",  <!-- 计划检修结束日期 -->
        OVERHAUL_NUMBER as "overhaulNumber",  <!-- 计划检修人数 -->
        OVERHAUL_TIME as "overhaulTime",  <!-- 计划检修时间 -->
        OVERHAUL_PROJECT as "overhaulProject",  <!-- 计划检修项目 -->
        EXCEPTION_CONTACT_ID as "exceptionContactId",  <!-- 异常联络单号 -->
        OVERHAUL_SOURCE as "overhaulSource",  <!-- 检修来源 -->
        OUTSOURCING_CONTACT_ID as "outsourcingContactId",  <!-- 委外联络单号 -->
        SECURITY_MEASURES as "securityMeasures",  <!-- 安全措施 -->
        ACCEPTANCE_CRITERIA as "acceptanceCriteria",  <!-- 检修验收标准 -->
        IMPLEMENT_MAN_ID as "implementManId",  <!-- 实施人 -->
        IMPLEMENT_MAN_NAME as "implementManName",  <!-- 实施人姓名 -->
        OVERHAUL_PLAN_STATUS as "overhaulPlanStatus",  <!-- 检修计划状态 -->
        OVERHAUL_IMPLEMENT_DATE as "overhaulImplementDate",  <!-- 检修实施日期 -->
        ACTUAL_OVERHAUL_NUMBER as "actualOverhaulNumber",  <!-- 实际检修人数 -->
        ACTUAL_OVERHAUL_TIME as "actualOverhaulTime",  <!-- 实际检修时间 -->
        IS_COMPLETE as "isComplete",  <!-- 是否完成 -->
        OVERHAUL_LEGACY_PROJECT as "overhaulLegacyProject",  <!-- 遗留检修项目 -->
        IS_CONFORM_STANDARD as "isConformStandard",  <!-- 是否符合标准 -->
        RELEVANT_MEASURES as "relevantMeasures",  <!-- 相关措施 -->
        IS_HOT as "isHot",  <!-- 是否动火 -->
        HOT_CARD_ID as "hotCardId",  <!-- 动火证编号 -->
        OFFLINE_PARTS_GONE as "offlinePartsGone",  <!-- 下线零件去向 -->
        ACTUALS_REVISOR as "actualsRevisor",  <!-- 检修实绩操作人 -->
        ACTUALS_TIME as "actualsTime",  <!-- 检修实绩操作时间 -->
        ACTUALS_REVISOR_NAME as "actualsRevisorName",  <!-- 检修实绩操作人姓名 -->
        VOUCHER_NUM as "voucherNum",  <!-- 依据凭单 -->
        ACTUAL_LEGACY_PROJECT as "actualLegacyProject",  <!-- 实际检修项目 -->
        PROCESS_INSTANCE_ID as "processInstanceId",  <!-- 工作流实例ID -->
        OVERHAUL_SUGGESTIONS as "overhaulSuggestions",  <!-- 问题及建议 -->
        OVERHAUL_SUMMARIZE as "overhaulSummarize",  <!-- 检修总结 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        DEVICE_CODE as "deviceCode",  <!-- 分部设备代码 -->
        DEVICE_NAME as "deviceName",  <!-- 分部设备名称 -->
        DELAY_REMARK as "delayRemark"  <!-- 延期备注 -->
        FROM ${mevgSchema}.TVGDM0801 WHERE 1=1
        AND SEG_NO = #segNo#
        AND E_ARCHIVES_NO = #eArchivesNo#
        AND DEL_FLAG = '0'
        AND OVERHAUL_PLAN_STATUS in ('20','30')
        ORDER BY OVERHAUL_START_DATE desc

    </select>

    <insert id="insert">
        INSERT INTO ${mevgSchema}.TVGDM0801 (OVERHAUL_PLAN_ID,  <!-- 检修计划编号 -->
        E_ARCHIVES_NO,  <!-- 设备档案编号 -->
        EQUIPMENT_NAME,  <!-- 设备名称 -->
        OVERHAUL_QUALITY,  <!-- 检修性质 -->
        OVERHAUL_TYPE,  <!-- 检修类别 -->
        OVERHAUL_START_DATE,  <!-- 计划检修开始日期 -->
        OVERHAUL_END_DATE,  <!-- 计划检修结束日期 -->
        OVERHAUL_NUMBER,  <!-- 计划检修人数 -->
        OVERHAUL_TIME,  <!-- 计划检修时间 -->
        OVERHAUL_PROJECT,  <!-- 计划检修项目 -->
        EXCEPTION_CONTACT_ID,  <!-- 异常联络单号 -->
        OVERHAUL_SOURCE,  <!-- 检修来源 -->
        OUTSOURCING_CONTACT_ID,  <!-- 委外联络单号 -->
        SECURITY_MEASURES,  <!-- 安全措施 -->
        ACCEPTANCE_CRITERIA,  <!-- 检修验收标准 -->
        IMPLEMENT_MAN_ID,  <!-- 实施人 -->
        IMPLEMENT_MAN_NAME,  <!-- 实施人姓名 -->
        OVERHAUL_PLAN_STATUS,  <!-- 检修计划状态 -->
        VOUCHER_NUM,  <!-- 依据凭单 -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE,  <!-- 业务单元代码 -->
        DEVICE_CODE,  <!-- 分部设备代码 -->
        DEVICE_NAME,  <!-- 分部设备名称 -->
        DELAY_REMARK,  <!-- 延期备注 -->
        OVERHAUL_SUGGESTIONS,  <!-- 问题及建议 -->
        OVERHAUL_SUMMARIZE  <!-- 检修总结 -->
        )
        VALUES (#overhaulPlanId#, #eArchivesNo#, #equipmentName#, #overhaulQuality#, #overhaulType#,
        #overhaulStartDate#, #overhaulEndDate#, #overhaulNumber#, #overhaulTime#, #overhaulProject#,
        #exceptionContactId#, #overhaulSource#, #outsourcingContactId#, #securityMeasures#, #acceptanceCriteria#,
        #implementManId#, #implementManName#, #overhaulPlanStatus#,#voucherNum#,
        #uuid#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#,
        #recRevisorName#, #recReviseTime#, #tenantId#, #archiveFlag#, #delFlag#, #segNo#, #unitCode#, #deviceCode#,
        #deviceName#, #delayRemark#, #overhaulSuggestions#, #overhaulSummarize#)
    </insert>

    <insert id="insertFinish">
        INSERT INTO ${mevgSchema}.TVGDM0801 (OVERHAUL_PLAN_ID,  <!-- 检修计划编号 -->
        E_ARCHIVES_NO,  <!-- 设备档案编号 -->
        EQUIPMENT_NAME,  <!-- 设备名称 -->
        OVERHAUL_QUALITY,  <!-- 检修性质 -->
        OVERHAUL_TYPE,  <!-- 检修类别 -->
        OVERHAUL_START_DATE,  <!-- 计划检修开始日期 -->
        OVERHAUL_END_DATE,  <!-- 计划检修结束日期 -->
        OVERHAUL_NUMBER,  <!-- 计划检修人数 -->
        OVERHAUL_TIME,  <!-- 计划检修时间 -->
        OVERHAUL_PROJECT,  <!-- 计划检修项目 -->
        EXCEPTION_CONTACT_ID,  <!-- 异常联络单号 -->
        OVERHAUL_SOURCE,  <!-- 检修来源 -->
        OUTSOURCING_CONTACT_ID,  <!-- 委外联络单号 -->
        SECURITY_MEASURES,  <!-- 安全措施 -->
        ACCEPTANCE_CRITERIA,  <!-- 检修验收标准 -->
        IMPLEMENT_MAN_ID,  <!-- 实施人 -->
        IMPLEMENT_MAN_NAME,  <!-- 实施人姓名 -->
        OVERHAUL_PLAN_STATUS,  <!-- 检修计划状态 -->
        OVERHAUL_IMPLEMENT_DATE,  <!-- 检修实施日期 -->
        ACTUAL_OVERHAUL_NUMBER,  <!-- 实际检修人数 -->
        ACTUAL_OVERHAUL_TIME,  <!-- 实际检修时间 -->
        IS_COMPLETE,  <!-- 是否完成 -->
        OVERHAUL_LEGACY_PROJECT,  <!-- 遗留检修项目 -->
        IS_CONFORM_STANDARD,  <!-- 是否符合标准 -->
        RELEVANT_MEASURES,  <!-- 相关措施 -->
        IS_HOT,  <!-- 是否动火 -->
        HOT_CARD_ID,  <!-- 动火证编号 -->
        OFFLINE_PARTS_GONE,  <!-- 下线零件去向 -->
        ACTUALS_REVISOR,  <!-- 检修实绩操作人 -->
        ACTUALS_TIME,  <!-- 检修实绩操作时间 -->
        ACTUALS_REVISOR_NAME,  <!-- 检修实绩操作人姓名 -->
        VOUCHER_NUM,  <!-- 依据凭单 -->
        APPR_STATUS,  <!-- 审批状态 -->
        ACTUAL_LEGACY_PROJECT,  <!-- 实际检修项目 -->
        PROCESS_INSTANCE_ID,  <!-- 工作流实例ID -->
        OVERHAUL_SUGGESTIONS,  <!-- 问题及建议 -->
        OVERHAUL_SUMMARIZE,  <!-- 检修总结 -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE,  <!-- 业务单元代码 -->
        DEVICE_CODE,  <!-- 分部设备代码 -->
        DEVICE_NAME,  <!-- 分部设备名称 -->
        DELAY_REMARK  <!-- 延期备注 -->
        )
        VALUES (#overhaulPlanId#, #eArchivesNo#, #equipmentName#, #overhaulQuality#, #overhaulType#,
        #overhaulStartDate#, #overhaulEndDate#, #overhaulNumber#, #overhaulTime#, #overhaulProject#,
        #exceptionContactId#, #overhaulSource#, #outsourcingContactId#, #securityMeasures#, #acceptanceCriteria#,
        #implementManId#, #implementManName#, #overhaulPlanStatus#, #overhaulImplementDate#, #actualOverhaulNumber#,
        #actualOverhaulTime#, #isComplete#, #overhaulLegacyProject#, #isConformStandard#, #relevantMeasures#, #isHot#,
        #hotCardId#, #offlinePartsGone#, #actualsRevisor#, #actualsTime#, #actualsRevisorName#, #voucherNum#,
        #apprStatus#, #actualLegacyProject#, #processInstanceId#, #overhaulSuggestions#, #overhaulSummarize#, #uuid#,
        #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #tenantId#,
        #archiveFlag#, #delFlag#, #segNo#, #unitCode#, #deviceCode#, #deviceName#, #delayRemark#)
    </insert>

    <delete id="delete">
        DELETE FROM ${mevgSchema}.TVGDM0801 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE ${mevgSchema}.TVGDM0801
        SET
        OVERHAUL_QUALITY = #overhaulQuality#,   <!-- 检修性质 -->
        OVERHAUL_TYPE = #overhaulType#,   <!-- 检修类别 -->
        OVERHAUL_START_DATE = #overhaulStartDate#,   <!-- 计划检修开始日期 -->
        OVERHAUL_END_DATE = #overhaulEndDate#,   <!-- 计划检修结束日期 -->
        OVERHAUL_NUMBER = #overhaulNumber#,   <!-- 计划检修人数 -->
        OVERHAUL_TIME = #overhaulTime#,   <!-- 计划检修时间 -->
        OVERHAUL_PROJECT = #overhaulProject#,   <!-- 计划检修项目 -->
        OUTSOURCING_CONTACT_ID = #outsourcingContactId#,   <!-- 委外联络单号 -->
        SECURITY_MEASURES = #securityMeasures#,   <!-- 安全措施 -->
        ACCEPTANCE_CRITERIA = #acceptanceCriteria#,   <!-- 检修验收标准 -->
        IMPLEMENT_MAN_ID = #implementManId#,   <!-- 实施人 -->
        IMPLEMENT_MAN_NAME = #implementManName#,   <!-- 实施人姓名 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        DEVICE_CODE = #deviceCode#,   <!-- 分部设备代码 -->
        DEVICE_NAME = #deviceName#,   <!-- 分部设备名称 -->
        DELAY_REMARK = #delayRemark#   <!-- 延期备注 -->
        WHERE
        UUID = #uuid#
    </update>

    <update id="updateActual">
        UPDATE ${mevgSchema}.TVGDM0801
        SET
        OVERHAUL_PLAN_STATUS = #overhaulPlanStatus#,   <!-- 检修计划状态 -->
        OVERHAUL_IMPLEMENT_DATE = #overhaulImplementDate#,   <!-- 检修实施日期 -->
        ACTUAL_OVERHAUL_NUMBER = #actualOverhaulNumber#,   <!-- 实际检修人数 -->
        ACTUAL_OVERHAUL_TIME = #actualOverhaulTime#,   <!-- 实际检修时间 -->
        IS_COMPLETE = #isComplete#,   <!-- 是否完成 -->
        OVERHAUL_LEGACY_PROJECT = #overhaulLegacyProject#,   <!-- 遗留检修项目 -->
        IS_CONFORM_STANDARD = #isConformStandard#,   <!-- 是否符合标准 -->
        RELEVANT_MEASURES = #relevantMeasures#,   <!-- 相关措施 -->
        IS_HOT = #isHot#,   <!-- 是否动火 -->
        HOT_CARD_ID = #hotCardId#,   <!-- 动火证编号 -->
        OFFLINE_PARTS_GONE = #offlinePartsGone#,   <!-- 下线零件去向 -->
        ACTUALS_REVISOR = #actualsRevisor#,   <!-- 检修实绩操作人 -->
        ACTUALS_TIME = #actualsTime#,   <!-- 检修实绩操作时间 -->
        ACTUALS_REVISOR_NAME = #actualsRevisorName#,   <!-- 检修实绩操作人姓名 -->
        ACTUAL_LEGACY_PROJECT = #actualLegacyProject#,   <!-- 实际检修项目 -->
        PROCESS_INSTANCE_ID = #processInstanceId#,   <!-- 工作流实例ID -->
        OVERHAUL_SUGGESTIONS = #overhaulSuggestions#,   <!-- 问题及建议 -->
        OVERHAUL_SUMMARIZE = #overhaulSummarize#,   <!-- 检修总结 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时刻 -->
        WHERE
        UUID = #uuid#
    </update>

    <update id="updateStatus">
        UPDATE ${mevgSchema}.TVGDM0801
        SET
        OVERHAUL_PLAN_STATUS = #overhaulPlanStatus#,   <!-- 检修计划状态 -->
        PROCESS_INSTANCE_ID = #processInstanceId#,   <!-- 工作流实例ID -->
        APPR_STATUS = #apprStatus#,   <!-- 审批状态 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        DEL_FLAG = #delFlag#,   <!-- 删除标记 -->
        OVERHAUL_SUGGESTIONS = #overhaulSuggestions#,   <!-- 问题及建议 -->
        OVERHAUL_SUMMARIZE = #overhaulSummarize#   <!-- 检修总结 -->
        WHERE
        UUID = #uuid#
    </update>

    <update id="updateStatusForMoblie">
        UPDATE ${mevgSchema}.TVGDM0801
        SET
        OVERHAUL_PLAN_STATUS = #overhaulPlanStatus#,   <!-- 检修计划状态 -->
        PROCESS_INSTANCE_ID = #processInstanceId#,   <!-- 工作流实例ID -->
        APPR_STATUS = #apprStatus#,   <!-- 审批状态 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时刻 -->
        <isNotEmpty prepend=" , " property="delFlag">
            DEL_FLAG = #delFlag#,   <!-- 删除标记 -->
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="overhaulSuggestions">
            OVERHAUL_SUGGESTIONS = #overhaulSuggestions#,   <!-- 问题及建议 -->
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="overhaulSummarize">
            OVERHAUL_SUMMARIZE = #overhaulSummarize#   <!-- 检修总结 -->
        </isNotEmpty>
        WHERE
        1=1
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="overhaulPlanId">
            OVERHAUL_PLAN_ID like concat('%',#overhaulPlanId#,'%')
        </isNotEmpty>
    </update>

</sqlMap>
