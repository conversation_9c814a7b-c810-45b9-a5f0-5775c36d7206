/**
* Generate time : 2025-07-04 21:44:04
* Version : 1.0
*/
package com.baosight.imom.common.li.domain;
import com.baosight.iplat4j.core.util.NumberUtils;
import java.math.BigDecimal;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* Tlirl0597
* 
*/
public class Tlirl0597 extends DaoEPBase {

    private String measureId = " ";		/* 测量ID*/
    private String ladingSpotAddr = " ";		/* 装货地点地址*/
    private String ladingSpotContactor = " ";		/* 装货地联系人*/
    private String settleUserName = " ";		
    private BigDecimal totalQty = new BigDecimal(0.000);		/* 总数量*/
    private String recReviseTime = " ";		/* 记录修改时间*/
    private String archiveFlag = " ";		
    private BigDecimal reqExecutePeriod = new BigDecimal(0);		/* 请求执行周期*/
    private String uuid = " ";		/* 唯一标识（主键）*/
    private String recRevisor = " ";		/* 记录修改人ID*/
    private String tenantUser = " ";		
    private String destSpotAddr = " ";		/* 目的地地址*/
    private String destSpotContactor = " ";		/* 目的地联系人*/
    private String recCreatorName = " ";		/* 记录创建人姓名*/
    private String unitCode = " ";		/* 单位编码*/
    private String reqExecuteDate = " ";		
    private String activeEndDate = " ";		
    private String companyOutBusinessType = " ";		
    private String redFlag = "0";		/* 红标标志*/
    private String recRevisorName = " ";		/* 记录修改人*/
    private String storeFeeSettleType = " ";		/* 仓储费结算类型*/
    private String destSpotName = " ";		/* 目的地名称*/
    private String consigneeAddr = " ";		/* 收货方地址*/
    private String tradeCode = " ";		
    private String finUserId = " ";		
    private String consigneeCname = " ";		/* 收货方中文名*/
    private String ladingSpotId = " ";		/* 装货地ID*/
    private String vehicleNo = " ";		/* 车牌号*/
    private String recCreator = " ";		/* 记录创建人ID*/
    private String transType = " ";		/* 运输类型*/
    private String driverId = " ";		
    private String rainCloth = " ";		
    private String consigneeCode = " ";		/* 收货方代码*/
    private String driverName = " ";		
    private String finUserName = " ";		
    private String deleiveryType = " ";		/* 交付类型*/
    private String tproviderId = " ";		/* 运输商ID*/
    private BigDecimal sumNetWeight = new BigDecimal("0");		/* 总净重*/
    private String transFeeSettleType = " ";		/* 运输费结算类型*/
    private String transBillId = " ";		/* 运输单据ID*/
    private String destSpotTele = " ";		/* 目的地电话*/
    private String d_userName = " ";		
    private String activeStartDate = " ";		
    private String destSpotAddrId = " ";		
    private String outFeeSettleType = " ";		/* 出库费结算类型*/
    private String remark = " ";		/* 备注*/
    private String transApplyId = " ";		
    private String delFlag = "0";		/* 删除标志（0未删除）*/
    private String billPassword = " ";		
    private BigDecimal planGrossWeight = new BigDecimal(0.000);		/* 计划毛重*/
    private String destSpotId = " ";		/* 目的地ID*/
    private String ladingSpotAddrId = " ";		
    private String recCreateTime = " ";		/* 记录创建时间*/
    private String freeStoreBeginDate = " ";		
    private String consigneePhone = " ";		
    private String areaLgsCenterCode = " ";		/* 物流中心代码*/
    private String ebillMark = " ";		
    private String deliveryDate = " ";		
    private String consigneeContactor = " ";		
    private String directUserNum = " ";		
    private String recordSource = " ";		/* 记录来源*/
    private String lgsSegNo = " ";		/* 物流段号*/
    private String consigneeType = " ";		/* 收货方类型*/
    private String d_userNum = " ";		
    private String transBillType = " ";		/* 运输单据类型*/
    private String billingTime = " ";		
    private String deliveryType = " ";		/* 交付类型*/
    private String directUserName = " ";		
    private String ladingSpotName = " ";		/* 装货地名称*/
    private String companyBusinessType = " ";		/* 公司业务类型*/
    private String ladingSpotTele = " ";		/* 装货地电话*/
    private String tproviderName = " ";		/* 运输商名称*/
    private String segNo = " ";		/* 段号*/
    private String haierOrderNumber = " ";		
    private String settleUserNum = " ";		
    private BigDecimal planNetWeight = new BigDecimal(0.000);		/* 计划净重*/
    private String driverPhone = " ";		
    private BigDecimal sumNtotalQty = new BigDecimal("0");		/* 总数量合计*/

    /**
    * initialize the metadata
    */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("measureId");
        eiColumn.setDescName("测量ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ladingSpotAddr");
        eiColumn.setDescName("装货地点地址");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ladingSpotContactor");
        eiColumn.setDescName("装货地联系人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("settleUserName");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("totalQty");
        eiColumn.setType("N");
        eiColumn.setScaleLength(3);
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("总数量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reqExecutePeriod");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("请求执行周期");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("唯一标识（主键）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantUser");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("destSpotAddr");
        eiColumn.setDescName("目的地地址");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("destSpotContactor");
        eiColumn.setDescName("目的地联系人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("单位编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reqExecuteDate");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("activeEndDate");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("companyOutBusinessType");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("redFlag");
        eiColumn.setDescName("红标标志");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("storeFeeSettleType");
        eiColumn.setDescName("仓储费结算类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("destSpotName");
        eiColumn.setDescName("目的地名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("consigneeAddr");
        eiColumn.setDescName("收货方地址");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tradeCode");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("finUserId");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("consigneeCname");
        eiColumn.setDescName("收货方中文名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ladingSpotId");
        eiColumn.setDescName("装货地ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("vehicleNo");
        eiColumn.setDescName("车牌号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("transType");
        eiColumn.setDescName("运输类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverId");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("rainCloth");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("consigneeCode");
        eiColumn.setDescName("收货方代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverName");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("finUserName");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deleiveryType");
        eiColumn.setDescName("交付类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tproviderId");
        eiColumn.setDescName("运输商ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sumNetWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(3);
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("总净重");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("transFeeSettleType");
        eiColumn.setDescName("运输费结算类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("transBillId");
        eiColumn.setDescName("运输单据ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("destSpotTele");
        eiColumn.setDescName("目的地电话");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("d_userName");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("activeStartDate");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("destSpotAddrId");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("outFeeSettleType");
        eiColumn.setDescName("出库费结算类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("transApplyId");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标志（0未删除）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("billPassword");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("planGrossWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(3);
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("计划毛重");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("destSpotId");
        eiColumn.setDescName("目的地ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ladingSpotAddrId");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("freeStoreBeginDate");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("consigneePhone");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("areaLgsCenterCode");
        eiColumn.setDescName("物流中心代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ebillMark");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deliveryDate");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("consigneeContactor");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("directUserNum");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recordSource");
        eiColumn.setDescName("记录来源");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("lgsSegNo");
        eiColumn.setDescName("物流段号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("consigneeType");
        eiColumn.setDescName("收货方类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("d_userNum");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("transBillType");
        eiColumn.setDescName("运输单据类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("billingTime");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deliveryType");
        eiColumn.setDescName("交付类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("directUserName");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ladingSpotName");
        eiColumn.setDescName("装货地名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("companyBusinessType");
        eiColumn.setDescName("公司业务类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ladingSpotTele");
        eiColumn.setDescName("装货地电话");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tproviderName");
        eiColumn.setDescName("运输商名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("段号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("haierOrderNumber");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("settleUserNum");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("planNetWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(3);
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("计划净重");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverPhone");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sumNtotalQty");
        eiColumn.setType("N");
        eiColumn.setScaleLength(3);
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("总数量合计");
        eiMetadata.addMeta(eiColumn);


    }

    /**
    * the constructor
    */
    public Tlirl0597() {
        initMetaData();
    }

    /**
    * get the measureId - 测量ID
    * @return the measureId
    */
    public String getMeasureId() {
        return this.measureId;
    }

    /**
    * set the measureId - 测量ID
    */
    public void setMeasureId(String measureId) {
        this.measureId = measureId;
    }
    /**
    * get the ladingSpotAddr - 装货地点地址
    * @return the ladingSpotAddr
    */
    public String getLadingSpotAddr() {
        return this.ladingSpotAddr;
    }

    /**
    * set the ladingSpotAddr - 装货地点地址
    */
    public void setLadingSpotAddr(String ladingSpotAddr) {
        this.ladingSpotAddr = ladingSpotAddr;
    }
    /**
    * get the ladingSpotContactor - 装货地联系人
    * @return the ladingSpotContactor
    */
    public String getLadingSpotContactor() {
        return this.ladingSpotContactor;
    }

    /**
    * set the ladingSpotContactor - 装货地联系人
    */
    public void setLadingSpotContactor(String ladingSpotContactor) {
        this.ladingSpotContactor = ladingSpotContactor;
    }
    /**
    * get the settleUserName 
    * @return the settleUserName
    */
    public String getSettleUserName() {
        return this.settleUserName;
    }

    /**
    * set the settleUserName 
    */
    public void setSettleUserName(String settleUserName) {
        this.settleUserName = settleUserName;
    }
    /**
    * get the totalQty - 总数量
    * @return the totalQty
    */
    public BigDecimal getTotalQty() {
        return this.totalQty;
    }

    /**
    * set the totalQty - 总数量
    */
    public void setTotalQty(BigDecimal totalQty) {
        this.totalQty = totalQty;
    }
    /**
    * get the recReviseTime - 记录修改时间
    * @return the recReviseTime
    */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
    * set the recReviseTime - 记录修改时间
    */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }
    /**
    * get the archiveFlag 
    * @return the archiveFlag
    */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
    * set the archiveFlag 
    */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }
    /**
    * get the reqExecutePeriod - 请求执行周期
    * @return the reqExecutePeriod
    */
    public BigDecimal getReqExecutePeriod() {
        return this.reqExecutePeriod;
    }

    /**
    * set the reqExecutePeriod - 请求执行周期
    */
    public void setReqExecutePeriod(BigDecimal reqExecutePeriod) {
        this.reqExecutePeriod = reqExecutePeriod;
    }
    /**
    * get the uuid - 唯一标识（主键）
    * @return the uuid
    */
    public String getUuid() {
        return this.uuid;
    }

    /**
    * set the uuid - 唯一标识（主键）
    */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }
    /**
    * get the recRevisor - 记录修改人ID
    * @return the recRevisor
    */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
    * set the recRevisor - 记录修改人ID
    */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }
    /**
    * get the tenantUser 
    * @return the tenantUser
    */
    public String getTenantUser() {
        return this.tenantUser;
    }

    /**
    * set the tenantUser 
    */
    public void setTenantUser(String tenantUser) {
        this.tenantUser = tenantUser;
    }
    /**
    * get the destSpotAddr - 目的地地址
    * @return the destSpotAddr
    */
    public String getDestSpotAddr() {
        return this.destSpotAddr;
    }

    /**
    * set the destSpotAddr - 目的地地址
    */
    public void setDestSpotAddr(String destSpotAddr) {
        this.destSpotAddr = destSpotAddr;
    }
    /**
    * get the destSpotContactor - 目的地联系人
    * @return the destSpotContactor
    */
    public String getDestSpotContactor() {
        return this.destSpotContactor;
    }

    /**
    * set the destSpotContactor - 目的地联系人
    */
    public void setDestSpotContactor(String destSpotContactor) {
        this.destSpotContactor = destSpotContactor;
    }
    /**
    * get the recCreatorName - 记录创建人姓名
    * @return the recCreatorName
    */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
    * set the recCreatorName - 记录创建人姓名
    */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }
    /**
    * get the unitCode - 单位编码
    * @return the unitCode
    */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
    * set the unitCode - 单位编码
    */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }
    /**
    * get the reqExecuteDate 
    * @return the reqExecuteDate
    */
    public String getReqExecuteDate() {
        return this.reqExecuteDate;
    }

    /**
    * set the reqExecuteDate 
    */
    public void setReqExecuteDate(String reqExecuteDate) {
        this.reqExecuteDate = reqExecuteDate;
    }
    /**
    * get the activeEndDate 
    * @return the activeEndDate
    */
    public String getActiveEndDate() {
        return this.activeEndDate;
    }

    /**
    * set the activeEndDate 
    */
    public void setActiveEndDate(String activeEndDate) {
        this.activeEndDate = activeEndDate;
    }
    /**
    * get the companyOutBusinessType 
    * @return the companyOutBusinessType
    */
    public String getCompanyOutBusinessType() {
        return this.companyOutBusinessType;
    }

    /**
    * set the companyOutBusinessType 
    */
    public void setCompanyOutBusinessType(String companyOutBusinessType) {
        this.companyOutBusinessType = companyOutBusinessType;
    }
    /**
    * get the redFlag - 红标标志
    * @return the redFlag
    */
    public String getRedFlag() {
        return this.redFlag;
    }

    /**
    * set the redFlag - 红标标志
    */
    public void setRedFlag(String redFlag) {
        this.redFlag = redFlag;
    }
    /**
    * get the recRevisorName - 记录修改人
    * @return the recRevisorName
    */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
    * set the recRevisorName - 记录修改人
    */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }
    /**
    * get the storeFeeSettleType - 仓储费结算类型
    * @return the storeFeeSettleType
    */
    public String getStoreFeeSettleType() {
        return this.storeFeeSettleType;
    }

    /**
    * set the storeFeeSettleType - 仓储费结算类型
    */
    public void setStoreFeeSettleType(String storeFeeSettleType) {
        this.storeFeeSettleType = storeFeeSettleType;
    }
    /**
    * get the destSpotName - 目的地名称
    * @return the destSpotName
    */
    public String getDestSpotName() {
        return this.destSpotName;
    }

    /**
    * set the destSpotName - 目的地名称
    */
    public void setDestSpotName(String destSpotName) {
        this.destSpotName = destSpotName;
    }
    /**
    * get the consigneeAddr - 收货方地址
    * @return the consigneeAddr
    */
    public String getConsigneeAddr() {
        return this.consigneeAddr;
    }

    /**
    * set the consigneeAddr - 收货方地址
    */
    public void setConsigneeAddr(String consigneeAddr) {
        this.consigneeAddr = consigneeAddr;
    }
    /**
    * get the tradeCode 
    * @return the tradeCode
    */
    public String getTradeCode() {
        return this.tradeCode;
    }

    /**
    * set the tradeCode 
    */
    public void setTradeCode(String tradeCode) {
        this.tradeCode = tradeCode;
    }
    /**
    * get the finUserId 
    * @return the finUserId
    */
    public String getFinUserId() {
        return this.finUserId;
    }

    /**
    * set the finUserId 
    */
    public void setFinUserId(String finUserId) {
        this.finUserId = finUserId;
    }
    /**
    * get the consigneeCname - 收货方中文名
    * @return the consigneeCname
    */
    public String getConsigneeCname() {
        return this.consigneeCname;
    }

    /**
    * set the consigneeCname - 收货方中文名
    */
    public void setConsigneeCname(String consigneeCname) {
        this.consigneeCname = consigneeCname;
    }
    /**
    * get the ladingSpotId - 装货地ID
    * @return the ladingSpotId
    */
    public String getLadingSpotId() {
        return this.ladingSpotId;
    }

    /**
    * set the ladingSpotId - 装货地ID
    */
    public void setLadingSpotId(String ladingSpotId) {
        this.ladingSpotId = ladingSpotId;
    }
    /**
    * get the vehicleNo - 车牌号
    * @return the vehicleNo
    */
    public String getVehicleNo() {
        return this.vehicleNo;
    }

    /**
    * set the vehicleNo - 车牌号
    */
    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }
    /**
    * get the recCreator - 记录创建人ID
    * @return the recCreator
    */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
    * set the recCreator - 记录创建人ID
    */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }
    /**
    * get the transType - 运输类型
    * @return the transType
    */
    public String getTransType() {
        return this.transType;
    }

    /**
    * set the transType - 运输类型
    */
    public void setTransType(String transType) {
        this.transType = transType;
    }
    /**
    * get the driverId 
    * @return the driverId
    */
    public String getDriverId() {
        return this.driverId;
    }

    /**
    * set the driverId 
    */
    public void setDriverId(String driverId) {
        this.driverId = driverId;
    }
    /**
    * get the rainCloth 
    * @return the rainCloth
    */
    public String getRainCloth() {
        return this.rainCloth;
    }

    /**
    * set the rainCloth 
    */
    public void setRainCloth(String rainCloth) {
        this.rainCloth = rainCloth;
    }
    /**
    * get the consigneeCode - 收货方代码
    * @return the consigneeCode
    */
    public String getConsigneeCode() {
        return this.consigneeCode;
    }

    /**
    * set the consigneeCode - 收货方代码
    */
    public void setConsigneeCode(String consigneeCode) {
        this.consigneeCode = consigneeCode;
    }
    /**
    * get the driverName 
    * @return the driverName
    */
    public String getDriverName() {
        return this.driverName;
    }

    /**
    * set the driverName 
    */
    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }
    /**
    * get the finUserName 
    * @return the finUserName
    */
    public String getFinUserName() {
        return this.finUserName;
    }

    /**
    * set the finUserName 
    */
    public void setFinUserName(String finUserName) {
        this.finUserName = finUserName;
    }
    /**
    * get the deleiveryType - 交付类型
    * @return the deleiveryType
    */
    public String getDeleiveryType() {
        return this.deleiveryType;
    }

    /**
    * set the deleiveryType - 交付类型
    */
    public void setDeleiveryType(String deleiveryType) {
        this.deleiveryType = deleiveryType;
    }
    /**
    * get the tproviderId - 运输商ID
    * @return the tproviderId
    */
    public String getTproviderId() {
        return this.tproviderId;
    }

    /**
    * set the tproviderId - 运输商ID
    */
    public void setTproviderId(String tproviderId) {
        this.tproviderId = tproviderId;
    }
    /**
    * get the sumNetWeight - 总净重
    * @return the sumNetWeight
    */
    public BigDecimal getSumNetWeight() {
        return this.sumNetWeight;
    }

    /**
    * set the sumNetWeight - 总净重
    */
    public void setSumNetWeight(BigDecimal sumNetWeight) {
        this.sumNetWeight = sumNetWeight;
    }
    /**
    * get the transFeeSettleType - 运输费结算类型
    * @return the transFeeSettleType
    */
    public String getTransFeeSettleType() {
        return this.transFeeSettleType;
    }

    /**
    * set the transFeeSettleType - 运输费结算类型
    */
    public void setTransFeeSettleType(String transFeeSettleType) {
        this.transFeeSettleType = transFeeSettleType;
    }
    /**
    * get the transBillId - 运输单据ID
    * @return the transBillId
    */
    public String getTransBillId() {
        return this.transBillId;
    }

    /**
    * set the transBillId - 运输单据ID
    */
    public void setTransBillId(String transBillId) {
        this.transBillId = transBillId;
    }
    /**
    * get the destSpotTele - 目的地电话
    * @return the destSpotTele
    */
    public String getDestSpotTele() {
        return this.destSpotTele;
    }

    /**
    * set the destSpotTele - 目的地电话
    */
    public void setDestSpotTele(String destSpotTele) {
        this.destSpotTele = destSpotTele;
    }
    /**
    * get the d_userName 
    * @return the d_userName
    */
    public String getD_userName() {
        return this.d_userName;
    }

    /**
    * set the d_userName 
    */
    public void setD_userName(String d_userName) {
        this.d_userName = d_userName;
    }
    /**
    * get the activeStartDate 
    * @return the activeStartDate
    */
    public String getActiveStartDate() {
        return this.activeStartDate;
    }

    /**
    * set the activeStartDate 
    */
    public void setActiveStartDate(String activeStartDate) {
        this.activeStartDate = activeStartDate;
    }
    /**
    * get the destSpotAddrId 
    * @return the destSpotAddrId
    */
    public String getDestSpotAddrId() {
        return this.destSpotAddrId;
    }

    /**
    * set the destSpotAddrId 
    */
    public void setDestSpotAddrId(String destSpotAddrId) {
        this.destSpotAddrId = destSpotAddrId;
    }
    /**
    * get the outFeeSettleType - 出库费结算类型
    * @return the outFeeSettleType
    */
    public String getOutFeeSettleType() {
        return this.outFeeSettleType;
    }

    /**
    * set the outFeeSettleType - 出库费结算类型
    */
    public void setOutFeeSettleType(String outFeeSettleType) {
        this.outFeeSettleType = outFeeSettleType;
    }
    /**
    * get the remark - 备注
    * @return the remark
    */
    public String getRemark() {
        return this.remark;
    }

    /**
    * set the remark - 备注
    */
    public void setRemark(String remark) {
        this.remark = remark;
    }
    /**
    * get the transApplyId 
    * @return the transApplyId
    */
    public String getTransApplyId() {
        return this.transApplyId;
    }

    /**
    * set the transApplyId 
    */
    public void setTransApplyId(String transApplyId) {
        this.transApplyId = transApplyId;
    }
    /**
    * get the delFlag - 删除标志（0未删除）
    * @return the delFlag
    */
    public String getDelFlag() {
        return this.delFlag;
    }

    /**
    * set the delFlag - 删除标志（0未删除）
    */
    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }
    /**
    * get the billPassword 
    * @return the billPassword
    */
    public String getBillPassword() {
        return this.billPassword;
    }

    /**
    * set the billPassword 
    */
    public void setBillPassword(String billPassword) {
        this.billPassword = billPassword;
    }
    /**
    * get the planGrossWeight - 计划毛重
    * @return the planGrossWeight
    */
    public BigDecimal getPlanGrossWeight() {
        return this.planGrossWeight;
    }

    /**
    * set the planGrossWeight - 计划毛重
    */
    public void setPlanGrossWeight(BigDecimal planGrossWeight) {
        this.planGrossWeight = planGrossWeight;
    }
    /**
    * get the destSpotId - 目的地ID
    * @return the destSpotId
    */
    public String getDestSpotId() {
        return this.destSpotId;
    }

    /**
    * set the destSpotId - 目的地ID
    */
    public void setDestSpotId(String destSpotId) {
        this.destSpotId = destSpotId;
    }
    /**
    * get the ladingSpotAddrId 
    * @return the ladingSpotAddrId
    */
    public String getLadingSpotAddrId() {
        return this.ladingSpotAddrId;
    }

    /**
    * set the ladingSpotAddrId 
    */
    public void setLadingSpotAddrId(String ladingSpotAddrId) {
        this.ladingSpotAddrId = ladingSpotAddrId;
    }
    /**
    * get the recCreateTime - 记录创建时间
    * @return the recCreateTime
    */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
    * set the recCreateTime - 记录创建时间
    */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }
    /**
    * get the freeStoreBeginDate 
    * @return the freeStoreBeginDate
    */
    public String getFreeStoreBeginDate() {
        return this.freeStoreBeginDate;
    }

    /**
    * set the freeStoreBeginDate 
    */
    public void setFreeStoreBeginDate(String freeStoreBeginDate) {
        this.freeStoreBeginDate = freeStoreBeginDate;
    }
    /**
    * get the consigneePhone 
    * @return the consigneePhone
    */
    public String getConsigneePhone() {
        return this.consigneePhone;
    }

    /**
    * set the consigneePhone 
    */
    public void setConsigneePhone(String consigneePhone) {
        this.consigneePhone = consigneePhone;
    }
    /**
    * get the areaLgsCenterCode - 物流中心代码
    * @return the areaLgsCenterCode
    */
    public String getAreaLgsCenterCode() {
        return this.areaLgsCenterCode;
    }

    /**
    * set the areaLgsCenterCode - 物流中心代码
    */
    public void setAreaLgsCenterCode(String areaLgsCenterCode) {
        this.areaLgsCenterCode = areaLgsCenterCode;
    }
    /**
    * get the ebillMark 
    * @return the ebillMark
    */
    public String getEbillMark() {
        return this.ebillMark;
    }

    /**
    * set the ebillMark 
    */
    public void setEbillMark(String ebillMark) {
        this.ebillMark = ebillMark;
    }
    /**
    * get the deliveryDate 
    * @return the deliveryDate
    */
    public String getDeliveryDate() {
        return this.deliveryDate;
    }

    /**
    * set the deliveryDate 
    */
    public void setDeliveryDate(String deliveryDate) {
        this.deliveryDate = deliveryDate;
    }
    /**
    * get the consigneeContactor 
    * @return the consigneeContactor
    */
    public String getConsigneeContactor() {
        return this.consigneeContactor;
    }

    /**
    * set the consigneeContactor 
    */
    public void setConsigneeContactor(String consigneeContactor) {
        this.consigneeContactor = consigneeContactor;
    }
    /**
    * get the directUserNum 
    * @return the directUserNum
    */
    public String getDirectUserNum() {
        return this.directUserNum;
    }

    /**
    * set the directUserNum 
    */
    public void setDirectUserNum(String directUserNum) {
        this.directUserNum = directUserNum;
    }
    /**
    * get the recordSource - 记录来源
    * @return the recordSource
    */
    public String getRecordSource() {
        return this.recordSource;
    }

    /**
    * set the recordSource - 记录来源
    */
    public void setRecordSource(String recordSource) {
        this.recordSource = recordSource;
    }
    /**
    * get the lgsSegNo - 物流段号
    * @return the lgsSegNo
    */
    public String getLgsSegNo() {
        return this.lgsSegNo;
    }

    /**
    * set the lgsSegNo - 物流段号
    */
    public void setLgsSegNo(String lgsSegNo) {
        this.lgsSegNo = lgsSegNo;
    }
    /**
    * get the consigneeType - 收货方类型
    * @return the consigneeType
    */
    public String getConsigneeType() {
        return this.consigneeType;
    }

    /**
    * set the consigneeType - 收货方类型
    */
    public void setConsigneeType(String consigneeType) {
        this.consigneeType = consigneeType;
    }
    /**
    * get the d_userNum 
    * @return the d_userNum
    */
    public String getD_userNum() {
        return this.d_userNum;
    }

    /**
    * set the d_userNum 
    */
    public void setD_userNum(String d_userNum) {
        this.d_userNum = d_userNum;
    }
    /**
    * get the transBillType - 运输单据类型
    * @return the transBillType
    */
    public String getTransBillType() {
        return this.transBillType;
    }

    /**
    * set the transBillType - 运输单据类型
    */
    public void setTransBillType(String transBillType) {
        this.transBillType = transBillType;
    }
    /**
    * get the billingTime 
    * @return the billingTime
    */
    public String getBillingTime() {
        return this.billingTime;
    }

    /**
    * set the billingTime 
    */
    public void setBillingTime(String billingTime) {
        this.billingTime = billingTime;
    }
    /**
    * get the deliveryType - 交付类型
    * @return the deliveryType
    */
    public String getDeliveryType() {
        return this.deliveryType;
    }

    /**
    * set the deliveryType - 交付类型
    */
    public void setDeliveryType(String deliveryType) {
        this.deliveryType = deliveryType;
    }
    /**
    * get the directUserName 
    * @return the directUserName
    */
    public String getDirectUserName() {
        return this.directUserName;
    }

    /**
    * set the directUserName 
    */
    public void setDirectUserName(String directUserName) {
        this.directUserName = directUserName;
    }
    /**
    * get the ladingSpotName - 装货地名称
    * @return the ladingSpotName
    */
    public String getLadingSpotName() {
        return this.ladingSpotName;
    }

    /**
    * set the ladingSpotName - 装货地名称
    */
    public void setLadingSpotName(String ladingSpotName) {
        this.ladingSpotName = ladingSpotName;
    }
    /**
    * get the companyBusinessType - 公司业务类型
    * @return the companyBusinessType
    */
    public String getCompanyBusinessType() {
        return this.companyBusinessType;
    }

    /**
    * set the companyBusinessType - 公司业务类型
    */
    public void setCompanyBusinessType(String companyBusinessType) {
        this.companyBusinessType = companyBusinessType;
    }
    /**
    * get the ladingSpotTele - 装货地电话
    * @return the ladingSpotTele
    */
    public String getLadingSpotTele() {
        return this.ladingSpotTele;
    }

    /**
    * set the ladingSpotTele - 装货地电话
    */
    public void setLadingSpotTele(String ladingSpotTele) {
        this.ladingSpotTele = ladingSpotTele;
    }
    /**
    * get the tproviderName - 运输商名称
    * @return the tproviderName
    */
    public String getTproviderName() {
        return this.tproviderName;
    }

    /**
    * set the tproviderName - 运输商名称
    */
    public void setTproviderName(String tproviderName) {
        this.tproviderName = tproviderName;
    }
    /**
    * get the segNo - 段号
    * @return the segNo
    */
    public String getSegNo() {
        return this.segNo;
    }

    /**
    * set the segNo - 段号
    */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }
    /**
    * get the haierOrderNumber 
    * @return the haierOrderNumber
    */
    public String getHaierOrderNumber() {
        return this.haierOrderNumber;
    }

    /**
    * set the haierOrderNumber 
    */
    public void setHaierOrderNumber(String haierOrderNumber) {
        this.haierOrderNumber = haierOrderNumber;
    }
    /**
    * get the settleUserNum 
    * @return the settleUserNum
    */
    public String getSettleUserNum() {
        return this.settleUserNum;
    }

    /**
    * set the settleUserNum 
    */
    public void setSettleUserNum(String settleUserNum) {
        this.settleUserNum = settleUserNum;
    }
    /**
    * get the planNetWeight - 计划净重
    * @return the planNetWeight
    */
    public BigDecimal getPlanNetWeight() {
        return this.planNetWeight;
    }

    /**
    * set the planNetWeight - 计划净重
    */
    public void setPlanNetWeight(BigDecimal planNetWeight) {
        this.planNetWeight = planNetWeight;
    }
    /**
    * get the driverPhone 
    * @return the driverPhone
    */
    public String getDriverPhone() {
        return this.driverPhone;
    }

    /**
    * set the driverPhone 
    */
    public void setDriverPhone(String driverPhone) {
        this.driverPhone = driverPhone;
    }
    /**
    * get the sumNtotalQty - 总数量合计
    * @return the sumNtotalQty
    */
    public BigDecimal getSumNtotalQty() {
        return this.sumNtotalQty;
    }

    /**
    * set the sumNtotalQty - 总数量合计
    */
    public void setSumNtotalQty(BigDecimal sumNtotalQty) {
        this.sumNtotalQty = sumNtotalQty;
    }

    /**
    * get the value from Map
    */
    public void fromMap(Map map) {
        setMeasureId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("measureId")), measureId));
        setLadingSpotAddr(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ladingSpotAddr")), ladingSpotAddr));
        setLadingSpotContactor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ladingSpotContactor")), ladingSpotContactor));
        setSettleUserName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("settleUserName")), settleUserName));
        setTotalQty(NumberUtils.toBigDecimal(StringUtils.toString(map.get("totalQty")), totalQty));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setReqExecutePeriod(NumberUtils.toBigDecimal(StringUtils.toString(map.get("reqExecutePeriod")), reqExecutePeriod));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setTenantUser(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantUser")), tenantUser));
        setDestSpotAddr(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("destSpotAddr")), destSpotAddr));
        setDestSpotContactor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("destSpotContactor")), destSpotContactor));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setReqExecuteDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("reqExecuteDate")), reqExecuteDate));
        setActiveEndDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("activeEndDate")), activeEndDate));
        setCompanyOutBusinessType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("companyOutBusinessType")), companyOutBusinessType));
        setRedFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("redFlag")), redFlag));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setStoreFeeSettleType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("storeFeeSettleType")), storeFeeSettleType));
        setDestSpotName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("destSpotName")), destSpotName));
        setConsigneeAddr(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("consigneeAddr")), consigneeAddr));
        setTradeCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tradeCode")), tradeCode));
        setFinUserId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("finUserId")), finUserId));
        setConsigneeCname(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("consigneeCname")), consigneeCname));
        setLadingSpotId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ladingSpotId")), ladingSpotId));
        setVehicleNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("vehicleNo")), vehicleNo));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setTransType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("transType")), transType));
        setDriverId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("driverId")), driverId));
        setRainCloth(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("rainCloth")), rainCloth));
        setConsigneeCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("consigneeCode")), consigneeCode));
        setDriverName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("driverName")), driverName));
        setFinUserName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("finUserName")), finUserName));
        setDeleiveryType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deleiveryType")), deleiveryType));
        setTproviderId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tproviderId")), tproviderId));
        setSumNetWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("sumNetWeight")), sumNetWeight));
        setTransFeeSettleType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("transFeeSettleType")), transFeeSettleType));
        setTransBillId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("transBillId")), transBillId));
        setDestSpotTele(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("destSpotTele")), destSpotTele));
        setD_userName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("d_userName")), d_userName));
        setActiveStartDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("activeStartDate")), activeStartDate));
        setDestSpotAddrId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("destSpotAddrId")), destSpotAddrId));
        setOutFeeSettleType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("outFeeSettleType")), outFeeSettleType));
        setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
        setTransApplyId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("transApplyId")), transApplyId));
        setDelFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("delFlag")), delFlag));
        setBillPassword(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("billPassword")), billPassword));
        setPlanGrossWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("planGrossWeight")), planGrossWeight));
        setDestSpotId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("destSpotId")), destSpotId));
        setLadingSpotAddrId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ladingSpotAddrId")), ladingSpotAddrId));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setFreeStoreBeginDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("freeStoreBeginDate")), freeStoreBeginDate));
        setConsigneePhone(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("consigneePhone")), consigneePhone));
        setAreaLgsCenterCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("areaLgsCenterCode")), areaLgsCenterCode));
        setEbillMark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ebillMark")), ebillMark));
        setDeliveryDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deliveryDate")), deliveryDate));
        setConsigneeContactor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("consigneeContactor")), consigneeContactor));
        setDirectUserNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("directUserNum")), directUserNum));
        setRecordSource(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recordSource")), recordSource));
        setLgsSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("lgsSegNo")), lgsSegNo));
        setConsigneeType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("consigneeType")), consigneeType));
        setD_userNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("d_userNum")), d_userNum));
        setTransBillType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("transBillType")), transBillType));
        setBillingTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("billingTime")), billingTime));
        setDeliveryType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deliveryType")), deliveryType));
        setDirectUserName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("directUserName")), directUserName));
        setLadingSpotName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ladingSpotName")), ladingSpotName));
        setCompanyBusinessType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("companyBusinessType")), companyBusinessType));
        setLadingSpotTele(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ladingSpotTele")), ladingSpotTele));
        setTproviderName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tproviderName")), tproviderName));
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setHaierOrderNumber(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("haierOrderNumber")), haierOrderNumber));
        setSettleUserNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("settleUserNum")), settleUserNum));
        setPlanNetWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("planNetWeight")), planNetWeight));
        setDriverPhone(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("driverPhone")), driverPhone));
        setSumNtotalQty(NumberUtils.toBigDecimal(StringUtils.toString(map.get("sumNtotalQty")), sumNtotalQty));
    }

    /**
    * set the value to Map
    */
    public Map toMap() {
        Map map = new HashMap();
        map.put("measureId",StringUtils.toString(measureId, eiMetadata.getMeta("measureId")));
        map.put("ladingSpotAddr",StringUtils.toString(ladingSpotAddr, eiMetadata.getMeta("ladingSpotAddr")));
        map.put("ladingSpotContactor",StringUtils.toString(ladingSpotContactor, eiMetadata.getMeta("ladingSpotContactor")));
        map.put("settleUserName",StringUtils.toString(settleUserName, eiMetadata.getMeta("settleUserName")));
        map.put("totalQty",StringUtils.toString(totalQty, eiMetadata.getMeta("totalQty")));
        map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("reqExecutePeriod",StringUtils.toString(reqExecutePeriod, eiMetadata.getMeta("reqExecutePeriod")));
        map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("tenantUser",StringUtils.toString(tenantUser, eiMetadata.getMeta("tenantUser")));
        map.put("destSpotAddr",StringUtils.toString(destSpotAddr, eiMetadata.getMeta("destSpotAddr")));
        map.put("destSpotContactor",StringUtils.toString(destSpotContactor, eiMetadata.getMeta("destSpotContactor")));
        map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("reqExecuteDate",StringUtils.toString(reqExecuteDate, eiMetadata.getMeta("reqExecuteDate")));
        map.put("activeEndDate",StringUtils.toString(activeEndDate, eiMetadata.getMeta("activeEndDate")));
        map.put("companyOutBusinessType",StringUtils.toString(companyOutBusinessType, eiMetadata.getMeta("companyOutBusinessType")));
        map.put("redFlag",StringUtils.toString(redFlag, eiMetadata.getMeta("redFlag")));
        map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("storeFeeSettleType",StringUtils.toString(storeFeeSettleType, eiMetadata.getMeta("storeFeeSettleType")));
        map.put("destSpotName",StringUtils.toString(destSpotName, eiMetadata.getMeta("destSpotName")));
        map.put("consigneeAddr",StringUtils.toString(consigneeAddr, eiMetadata.getMeta("consigneeAddr")));
        map.put("tradeCode",StringUtils.toString(tradeCode, eiMetadata.getMeta("tradeCode")));
        map.put("finUserId",StringUtils.toString(finUserId, eiMetadata.getMeta("finUserId")));
        map.put("consigneeCname",StringUtils.toString(consigneeCname, eiMetadata.getMeta("consigneeCname")));
        map.put("ladingSpotId",StringUtils.toString(ladingSpotId, eiMetadata.getMeta("ladingSpotId")));
        map.put("vehicleNo",StringUtils.toString(vehicleNo, eiMetadata.getMeta("vehicleNo")));
        map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("transType",StringUtils.toString(transType, eiMetadata.getMeta("transType")));
        map.put("driverId",StringUtils.toString(driverId, eiMetadata.getMeta("driverId")));
        map.put("rainCloth",StringUtils.toString(rainCloth, eiMetadata.getMeta("rainCloth")));
        map.put("consigneeCode",StringUtils.toString(consigneeCode, eiMetadata.getMeta("consigneeCode")));
        map.put("driverName",StringUtils.toString(driverName, eiMetadata.getMeta("driverName")));
        map.put("finUserName",StringUtils.toString(finUserName, eiMetadata.getMeta("finUserName")));
        map.put("deleiveryType",StringUtils.toString(deleiveryType, eiMetadata.getMeta("deleiveryType")));
        map.put("tproviderId",StringUtils.toString(tproviderId, eiMetadata.getMeta("tproviderId")));
        map.put("sumNetWeight",StringUtils.toString(sumNetWeight, eiMetadata.getMeta("sumNetWeight")));
        map.put("transFeeSettleType",StringUtils.toString(transFeeSettleType, eiMetadata.getMeta("transFeeSettleType")));
        map.put("transBillId",StringUtils.toString(transBillId, eiMetadata.getMeta("transBillId")));
        map.put("destSpotTele",StringUtils.toString(destSpotTele, eiMetadata.getMeta("destSpotTele")));
        map.put("d_userName",StringUtils.toString(d_userName, eiMetadata.getMeta("d_userName")));
        map.put("activeStartDate",StringUtils.toString(activeStartDate, eiMetadata.getMeta("activeStartDate")));
        map.put("destSpotAddrId",StringUtils.toString(destSpotAddrId, eiMetadata.getMeta("destSpotAddrId")));
        map.put("outFeeSettleType",StringUtils.toString(outFeeSettleType, eiMetadata.getMeta("outFeeSettleType")));
        map.put("remark",StringUtils.toString(remark, eiMetadata.getMeta("remark")));
        map.put("transApplyId",StringUtils.toString(transApplyId, eiMetadata.getMeta("transApplyId")));
        map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("billPassword",StringUtils.toString(billPassword, eiMetadata.getMeta("billPassword")));
        map.put("planGrossWeight",StringUtils.toString(planGrossWeight, eiMetadata.getMeta("planGrossWeight")));
        map.put("destSpotId",StringUtils.toString(destSpotId, eiMetadata.getMeta("destSpotId")));
        map.put("ladingSpotAddrId",StringUtils.toString(ladingSpotAddrId, eiMetadata.getMeta("ladingSpotAddrId")));
        map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("freeStoreBeginDate",StringUtils.toString(freeStoreBeginDate, eiMetadata.getMeta("freeStoreBeginDate")));
        map.put("consigneePhone",StringUtils.toString(consigneePhone, eiMetadata.getMeta("consigneePhone")));
        map.put("areaLgsCenterCode",StringUtils.toString(areaLgsCenterCode, eiMetadata.getMeta("areaLgsCenterCode")));
        map.put("ebillMark",StringUtils.toString(ebillMark, eiMetadata.getMeta("ebillMark")));
        map.put("deliveryDate",StringUtils.toString(deliveryDate, eiMetadata.getMeta("deliveryDate")));
        map.put("consigneeContactor",StringUtils.toString(consigneeContactor, eiMetadata.getMeta("consigneeContactor")));
        map.put("directUserNum",StringUtils.toString(directUserNum, eiMetadata.getMeta("directUserNum")));
        map.put("recordSource",StringUtils.toString(recordSource, eiMetadata.getMeta("recordSource")));
        map.put("lgsSegNo",StringUtils.toString(lgsSegNo, eiMetadata.getMeta("lgsSegNo")));
        map.put("consigneeType",StringUtils.toString(consigneeType, eiMetadata.getMeta("consigneeType")));
        map.put("d_userNum",StringUtils.toString(d_userNum, eiMetadata.getMeta("d_userNum")));
        map.put("transBillType",StringUtils.toString(transBillType, eiMetadata.getMeta("transBillType")));
        map.put("billingTime",StringUtils.toString(billingTime, eiMetadata.getMeta("billingTime")));
        map.put("deliveryType",StringUtils.toString(deliveryType, eiMetadata.getMeta("deliveryType")));
        map.put("directUserName",StringUtils.toString(directUserName, eiMetadata.getMeta("directUserName")));
        map.put("ladingSpotName",StringUtils.toString(ladingSpotName, eiMetadata.getMeta("ladingSpotName")));
        map.put("companyBusinessType",StringUtils.toString(companyBusinessType, eiMetadata.getMeta("companyBusinessType")));
        map.put("ladingSpotTele",StringUtils.toString(ladingSpotTele, eiMetadata.getMeta("ladingSpotTele")));
        map.put("tproviderName",StringUtils.toString(tproviderName, eiMetadata.getMeta("tproviderName")));
        map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("haierOrderNumber",StringUtils.toString(haierOrderNumber, eiMetadata.getMeta("haierOrderNumber")));
        map.put("settleUserNum",StringUtils.toString(settleUserNum, eiMetadata.getMeta("settleUserNum")));
        map.put("planNetWeight",StringUtils.toString(planNetWeight, eiMetadata.getMeta("planNetWeight")));
        map.put("driverPhone",StringUtils.toString(driverPhone, eiMetadata.getMeta("driverPhone")));
        map.put("sumNtotalQty",StringUtils.toString(sumNtotalQty, eiMetadata.getMeta("sumNtotalQty")));
        return map;
    }
}