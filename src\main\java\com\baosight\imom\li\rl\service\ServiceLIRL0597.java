/**
* Generate time : 2025-07-04 21:44:04
* Version : 1.0
*/
package com.baosight.imom.li.rl.service;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.imom.common.li.domain.Tlirl0597;

public class ServiceLIRL0597 extends ServiceBase {

    public EiInfo initLoad(EiInfo inInfo) {
        return super.initLoad(inInfo,new Tlirl0597());
    }

    @Override
    public EiInfo query(EiInfo inInfo) {
        return super.query(inInfo,"tlirl0597.query",new Tlirl0597());
    }

    @Override
    public EiInfo insert(EiInfo inInfo) {
        return super.insert(inInfo,"tlirl0597.insert");
    }

    @Override
    public EiInfo update(EiInfo inInfo) {
        return super.update(inInfo,"tlirl0597.update");
    }

    @Override
    public EiInfo delete(EiInfo inInfo) {
        return super.delete(inInfo,"tlirl0597.delete");
    }

}