/**
* Generate time : 2025-07-04 09:28:12
* Version : 1.0
*/
package com.baosight.imom.common.li.domain;
import com.baosight.iplat4j.core.util.NumberUtils;
import java.math.BigDecimal;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* Tlirl0599
* 
*/
public class Tlirl0599 extends DaoEPBase {

    private String actConsigneeName = " ";		/* 实际收货方名称*/
    private String ladingSpotAddr = " ";		/* 装货地点地址*/
    private BigDecimal totalPackQty = new BigDecimal("0");		/* 总包装数量*/
    private String transferNotiSystem = " ";		
    private String recReviseTime = " ";		/* 记录修改时间*/
    private String processFactoryName = " ";		
    private String privateRouteName = " ";		
    private String ladingBillId = " ";		/* 提单号（业务主键）*/
    private String tenantUser = " ";		
    private String destSpotContactor = " ";		/* 目的地联系人*/
    private String deliveryDateTime = " ";		/* 发货日期*/
    private String recCreatorName = " ";		/* 记录创建人姓名*/
    private String printMan = " ";		
    private String unitCode = " ";		/* 单位编码*/
    private String printBatchId = " ";		
    private String verifiSource = " ";		
    private String noPaperFlag = " ";		/* 无纸化标志*/
    private String recRevisorName = " ";		/* 记录修改人*/
    private String destSpotName = " ";		/* 目的地名称*/
    private String billingMethod = " ";		/* 计费方式代码*/
    private String printDate = " ";		
    private String logisticSchemeNo = " ";		
    private String finUserId = " ";		/* 财务用户ID*/
    private String billExpiProcMark = " ";		/* 票据处理标志*/
    private String ladingSpotId = " ";		/* 装货地ID*/
    private String recCreator = " ";		/* 记录创建人ID*/
    private String verifiDate = " ";		
    private String driver = " ";		
    private String driverId = " ";		
    private String d_userName = " ";		
    private String synFlag = " ";		
    private String vehicleName = " ";		
    private String rainCoatFlag = " ";		
    private String actConsigneeId = " ";		/* 实际收货方ID*/
    private String delFlag = "0";		/* 删除标志（0未删除）*/
    private String shipVerifiCode = " ";		
    private String verifiPerson = " ";		
    private String entrustFlag = " ";		
    private String ladingSpotAddrId = " ";		/* 装货地址ID*/
    private String manualNo = " ";		/* 手工单号*/
    private String billBankBatchId = " ";		
    private String storeType = " ";		
    private BigDecimal blTotAmt = new BigDecimal("0");		/* 总金额*/
    private String consigneeType = " ";		/* 收货方类型*/
    private String d_userNum = " ";		/* 用户编号*/
    private String billingTime = " ";		/* 开单时间*/
    private String applyNum = " ";		/* 申请数量*/
    private String arrearType = " ";		
    private String segNo = " ";		/* 段号*/
    private String printDateFirst = " ";		
    private BigDecimal outWeight = new BigDecimal(0.000);		/* 出库重量*/
    private String driverPhone2 = " ";		
    private String contractNum = " ";		/* 合同号*/
    private Integer expiryDays = 0;		/* 有效期天数*/
    private String transferObject = " ";		
    private String consigneeNum = " ";		/* 收货方编号*/
    private String resourceType = " ";		
    private String transferNotiDesc = " ";		
    private String verifiStatus = " ";		/* 核验状态*/
    private String ladingSpotContactor = " ";		/* 装货地联系人*/
    private String userNum = " ";		/* 用户编号*/
    private String activeEndTime = " ";		/* 有效截止时间*/
    private BigDecimal planLoadQty = new BigDecimal(0.000);		/* 计划装载量*/
    private String storeSettleType = " ";		
    private String archiveFlag = " ";		
    private String consignId = " ";		
    private String uuid = " ";		/* 唯一标识（主键）*/
    private String consignMark = " ";		
    private String recRevisor = " ";		/* 记录修改人ID*/
    private String destSpotAddr = " ";		/* 目的地地址*/
    private String ladingBillType = " ";		/* 提单类型*/
    private String ladingBillStatus = " ";		/* 提单状态*/
    private String processFactoryId = " ";		
    private String planInWarhouseTime = " ";		
    private String userNameAbbr = " ";		
    private String verifyDate = " ";		
    private String storeFeeSettleType = " ";		/* 仓储费结算类型*/
    private String privateRouteCode = " ";		
    private String tradeCode = " ";		/* 贸易代码*/
    private String vehicleCode = " ";		
    private String billBankStatus = " ";		
    private String vehicleNo = " ";		/* 车牌号*/
    private String transType = " ";		/* 运输类型*/
    private String tproviderContactId = " ";		
    private String financeContract = " ";		
    private String finUserName = " ";		/* 财务用户名称*/
    private String arrearsShipNo = " ";		
    private String tproviderId = " ";		/* 运输商ID*/
    private String transFeeSettleType = " ";		/* 运输费结算类型*/
    private String planOutWarhouseTime = " ";		
    private String destSpotTele = " ";		/* 目的地电话*/
    private String destSpotAddrId = " ";		
    private BigDecimal arrearsShipAmount = new BigDecimal("0");		/* 欠款金额*/
    private String tproviderContact = " ";		
    private String orderCustCname = " ";		
    private String outFeeSettleType = " ";		/* 出库费结算类型*/
    private String remark = " ";		/* 备注*/
    private String verifyMan = " ";		
    private String requireFinishDate = " ";		
    private String consignBillStatus = " ";		
    private String consigneeName = " ";		/* 收货方名称*/
    private String businessNo = " ";		
    private String emergencyDeliveryTime = " ";		
    private String destSpotId = " ";		/* 目的地ID*/
    private BigDecimal planLoadWt = new BigDecimal(0.000);		/* 计划装载重量*/
    private String recCreateTime;	/* 记录创建时间*/
    private String tproviderContactPlate = " ";		
    private String vehicleId = " ";		
    private String activeStartTime = " ";		/* 有效开始时间*/
    private String deliveryType = " ";		/* 交付类型*/
    private BigDecimal outPackQty = new BigDecimal(0.000);		/* 出库包装数量*/
    private String ladingSpotName = " ";		/* 装货地名称*/
    private String tproviderContactMobile = " ";		
    private String transNo = " ";		
    private String userName = " ";		/* 用户名称*/
    private String ladingSpotTele = " ";		/* 装货地电话*/
    private BigDecimal printNumber = new BigDecimal(0.000);		/* 打印次数*/
    private String destCountry = " ";		
    private String tproviderContactMobile2 = " ";		
    private String tproviderName = " ";		/* 运输商名称*/
    private BigDecimal totalWeight = new BigDecimal("0");		/* 总重量*/
    private String driverPhone = " ";		

    /**
    * initialize the metadata
    */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("actConsigneeName");
        eiColumn.setDescName("实际收货方名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ladingSpotAddr");
        eiColumn.setDescName("装货地点地址");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("totalPackQty");
        eiColumn.setType("N");
        eiColumn.setScaleLength(2);
        eiColumn.setFieldLength(12);
        eiColumn.setDescName("总包装数量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("transferNotiSystem");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processFactoryName");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("privateRouteName");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ladingBillId");
        eiColumn.setDescName("提单号（业务主键）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantUser");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("destSpotContactor");
        eiColumn.setDescName("目的地联系人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deliveryDateTime");
        eiColumn.setDescName("发货日期");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("printMan");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("单位编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("printBatchId");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("verifiSource");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("noPaperFlag");
        eiColumn.setDescName("无纸化标志");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("destSpotName");
        eiColumn.setDescName("目的地名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("billingMethod");
        eiColumn.setDescName("计费方式代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("printDate");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("logisticSchemeNo");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("finUserId");
        eiColumn.setDescName("财务用户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("billExpiProcMark");
        eiColumn.setDescName("票据处理标志");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ladingSpotId");
        eiColumn.setDescName("装货地ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("verifiDate");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driver");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverId");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("d_userName");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("synFlag");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("vehicleName");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("rainCoatFlag");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("actConsigneeId");
        eiColumn.setDescName("实际收货方ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标志（0未删除）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("shipVerifiCode");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("verifiPerson");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("entrustFlag");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ladingSpotAddrId");
        eiColumn.setDescName("装货地址ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("manualNo");
        eiColumn.setDescName("手工单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("billBankBatchId");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("storeType");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("blTotAmt");
        eiColumn.setType("N");
        eiColumn.setScaleLength(2);
        eiColumn.setFieldLength(12);
        eiColumn.setDescName("总金额");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("consigneeType");
        eiColumn.setDescName("收货方类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("d_userNum");
        eiColumn.setDescName("用户编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("billingTime");
        eiColumn.setDescName("开单时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("applyNum");
        eiColumn.setDescName("申请数量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("arrearType");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("段号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("printDateFirst");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("outWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(3);
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("出库重量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverPhone2");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("contractNum");
        eiColumn.setDescName("合同号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("expiryDays");
        eiColumn.setDescName("有效期天数");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("transferObject");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("consigneeNum");
        eiColumn.setDescName("收货方编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("resourceType");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("transferNotiDesc");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("verifiStatus");
        eiColumn.setDescName("核验状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ladingSpotContactor");
        eiColumn.setDescName("装货地联系人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("userNum");
        eiColumn.setDescName("用户编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("activeEndTime");
        eiColumn.setDescName("有效截止时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("planLoadQty");
        eiColumn.setType("N");
        eiColumn.setScaleLength(3);
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("计划装载量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("storeSettleType");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("consignId");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("唯一标识（主键）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("consignMark");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("destSpotAddr");
        eiColumn.setDescName("目的地地址");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ladingBillType");
        eiColumn.setDescName("提单类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ladingBillStatus");
        eiColumn.setDescName("提单状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processFactoryId");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("planInWarhouseTime");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("userNameAbbr");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("verifyDate");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("storeFeeSettleType");
        eiColumn.setDescName("仓储费结算类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("privateRouteCode");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tradeCode");
        eiColumn.setDescName("贸易代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("vehicleCode");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("billBankStatus");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("vehicleNo");
        eiColumn.setDescName("车牌号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("transType");
        eiColumn.setDescName("运输类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tproviderContactId");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("financeContract");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("finUserName");
        eiColumn.setDescName("财务用户名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("arrearsShipNo");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tproviderId");
        eiColumn.setDescName("运输商ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("transFeeSettleType");
        eiColumn.setDescName("运输费结算类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("planOutWarhouseTime");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("destSpotTele");
        eiColumn.setDescName("目的地电话");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("destSpotAddrId");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("arrearsShipAmount");
        eiColumn.setType("N");
        eiColumn.setScaleLength(2);
        eiColumn.setFieldLength(12);
        eiColumn.setDescName("欠款金额");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tproviderContact");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("orderCustCname");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("outFeeSettleType");
        eiColumn.setDescName("出库费结算类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("verifyMan");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("requireFinishDate");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("consignBillStatus");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("consigneeName");
        eiColumn.setDescName("收货方名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("businessNo");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("emergencyDeliveryTime");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("destSpotId");
        eiColumn.setDescName("目的地ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("planLoadWt");
        eiColumn.setType("N");
        eiColumn.setScaleLength(3);
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("计划装载重量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tproviderContactPlate");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("vehicleId");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("activeStartTime");
        eiColumn.setDescName("有效开始时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deliveryType");
        eiColumn.setDescName("交付类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("outPackQty");
        eiColumn.setType("N");
        eiColumn.setScaleLength(3);
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("出库包装数量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ladingSpotName");
        eiColumn.setDescName("装货地名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tproviderContactMobile");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("transNo");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("userName");
        eiColumn.setDescName("用户名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ladingSpotTele");
        eiColumn.setDescName("装货地电话");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("printNumber");
        eiColumn.setType("N");
        eiColumn.setScaleLength(3);
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("打印次数");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("destCountry");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tproviderContactMobile2");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tproviderName");
        eiColumn.setDescName("运输商名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("totalWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(3);
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("总重量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverPhone");
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);


    }

    /**
    * the constructor
    */
    public Tlirl0599() {
        initMetaData();
    }

    /**
    * get the actConsigneeName - 实际收货方名称
    * @return the actConsigneeName
    */
    public String getActConsigneeName() {
        return this.actConsigneeName;
    }

    /**
    * set the actConsigneeName - 实际收货方名称
    */
    public void setActConsigneeName(String actConsigneeName) {
        this.actConsigneeName = actConsigneeName;
    }
    /**
    * get the ladingSpotAddr - 装货地点地址
    * @return the ladingSpotAddr
    */
    public String getLadingSpotAddr() {
        return this.ladingSpotAddr;
    }

    /**
    * set the ladingSpotAddr - 装货地点地址
    */
    public void setLadingSpotAddr(String ladingSpotAddr) {
        this.ladingSpotAddr = ladingSpotAddr;
    }
    /**
    * get the totalPackQty - 总包装数量
    * @return the totalPackQty
    */
    public BigDecimal getTotalPackQty() {
        return this.totalPackQty;
    }

    /**
    * set the totalPackQty - 总包装数量
    */
    public void setTotalPackQty(BigDecimal totalPackQty) {
        this.totalPackQty = totalPackQty;
    }
    /**
    * get the transferNotiSystem 
    * @return the transferNotiSystem
    */
    public String getTransferNotiSystem() {
        return this.transferNotiSystem;
    }

    /**
    * set the transferNotiSystem 
    */
    public void setTransferNotiSystem(String transferNotiSystem) {
        this.transferNotiSystem = transferNotiSystem;
    }
    /**
    * get the recReviseTime - 记录修改时间
    * @return the recReviseTime
    */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
    * set the recReviseTime - 记录修改时间
    */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }
    /**
    * get the processFactoryName 
    * @return the processFactoryName
    */
    public String getProcessFactoryName() {
        return this.processFactoryName;
    }

    /**
    * set the processFactoryName 
    */
    public void setProcessFactoryName(String processFactoryName) {
        this.processFactoryName = processFactoryName;
    }
    /**
    * get the privateRouteName 
    * @return the privateRouteName
    */
    public String getPrivateRouteName() {
        return this.privateRouteName;
    }

    /**
    * set the privateRouteName 
    */
    public void setPrivateRouteName(String privateRouteName) {
        this.privateRouteName = privateRouteName;
    }
    /**
    * get the ladingBillId - 提单号（业务主键）
    * @return the ladingBillId
    */
    public String getLadingBillId() {
        return this.ladingBillId;
    }

    /**
    * set the ladingBillId - 提单号（业务主键）
    */
    public void setLadingBillId(String ladingBillId) {
        this.ladingBillId = ladingBillId;
    }
    /**
    * get the tenantUser 
    * @return the tenantUser
    */
    public String getTenantUser() {
        return this.tenantUser;
    }

    /**
    * set the tenantUser 
    */
    public void setTenantUser(String tenantUser) {
        this.tenantUser = tenantUser;
    }
    /**
    * get the destSpotContactor - 目的地联系人
    * @return the destSpotContactor
    */
    public String getDestSpotContactor() {
        return this.destSpotContactor;
    }

    /**
    * set the destSpotContactor - 目的地联系人
    */
    public void setDestSpotContactor(String destSpotContactor) {
        this.destSpotContactor = destSpotContactor;
    }
    /**
    * get the deliveryDateTime - 发货日期
    * @return the deliveryDateTime
    */
    public String getDeliveryDateTime() {
        return this.deliveryDateTime;
    }

    /**
    * set the deliveryDateTime - 发货日期
    */
    public void setDeliveryDateTime(String deliveryDateTime) {
        this.deliveryDateTime = deliveryDateTime;
    }
    /**
    * get the recCreatorName - 记录创建人姓名
    * @return the recCreatorName
    */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
    * set the recCreatorName - 记录创建人姓名
    */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }
    /**
    * get the printMan 
    * @return the printMan
    */
    public String getPrintMan() {
        return this.printMan;
    }

    /**
    * set the printMan 
    */
    public void setPrintMan(String printMan) {
        this.printMan = printMan;
    }
    /**
    * get the unitCode - 单位编码
    * @return the unitCode
    */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
    * set the unitCode - 单位编码
    */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }
    /**
    * get the printBatchId 
    * @return the printBatchId
    */
    public String getPrintBatchId() {
        return this.printBatchId;
    }

    /**
    * set the printBatchId 
    */
    public void setPrintBatchId(String printBatchId) {
        this.printBatchId = printBatchId;
    }
    /**
    * get the verifiSource 
    * @return the verifiSource
    */
    public String getVerifiSource() {
        return this.verifiSource;
    }

    /**
    * set the verifiSource 
    */
    public void setVerifiSource(String verifiSource) {
        this.verifiSource = verifiSource;
    }
    /**
    * get the noPaperFlag - 无纸化标志
    * @return the noPaperFlag
    */
    public String getNoPaperFlag() {
        return this.noPaperFlag;
    }

    /**
    * set the noPaperFlag - 无纸化标志
    */
    public void setNoPaperFlag(String noPaperFlag) {
        this.noPaperFlag = noPaperFlag;
    }
    /**
    * get the recRevisorName - 记录修改人
    * @return the recRevisorName
    */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
    * set the recRevisorName - 记录修改人
    */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }
    /**
    * get the destSpotName - 目的地名称
    * @return the destSpotName
    */
    public String getDestSpotName() {
        return this.destSpotName;
    }

    /**
    * set the destSpotName - 目的地名称
    */
    public void setDestSpotName(String destSpotName) {
        this.destSpotName = destSpotName;
    }
    /**
    * get the billingMethod - 计费方式代码
    * @return the billingMethod
    */
    public String getBillingMethod() {
        return this.billingMethod;
    }

    /**
    * set the billingMethod - 计费方式代码
    */
    public void setBillingMethod(String billingMethod) {
        this.billingMethod = billingMethod;
    }
    /**
    * get the printDate 
    * @return the printDate
    */
    public String getPrintDate() {
        return this.printDate;
    }

    /**
    * set the printDate 
    */
    public void setPrintDate(String printDate) {
        this.printDate = printDate;
    }
    /**
    * get the logisticSchemeNo 
    * @return the logisticSchemeNo
    */
    public String getLogisticSchemeNo() {
        return this.logisticSchemeNo;
    }

    /**
    * set the logisticSchemeNo 
    */
    public void setLogisticSchemeNo(String logisticSchemeNo) {
        this.logisticSchemeNo = logisticSchemeNo;
    }
    /**
    * get the finUserId - 财务用户ID
    * @return the finUserId
    */
    public String getFinUserId() {
        return this.finUserId;
    }

    /**
    * set the finUserId - 财务用户ID
    */
    public void setFinUserId(String finUserId) {
        this.finUserId = finUserId;
    }
    /**
    * get the billExpiProcMark - 票据处理标志
    * @return the billExpiProcMark
    */
    public String getBillExpiProcMark() {
        return this.billExpiProcMark;
    }

    /**
    * set the billExpiProcMark - 票据处理标志
    */
    public void setBillExpiProcMark(String billExpiProcMark) {
        this.billExpiProcMark = billExpiProcMark;
    }
    /**
    * get the ladingSpotId - 装货地ID
    * @return the ladingSpotId
    */
    public String getLadingSpotId() {
        return this.ladingSpotId;
    }

    /**
    * set the ladingSpotId - 装货地ID
    */
    public void setLadingSpotId(String ladingSpotId) {
        this.ladingSpotId = ladingSpotId;
    }
    /**
    * get the recCreator - 记录创建人ID
    * @return the recCreator
    */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
    * set the recCreator - 记录创建人ID
    */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }
    /**
    * get the verifiDate 
    * @return the verifiDate
    */
    public String getVerifiDate() {
        return this.verifiDate;
    }

    /**
    * set the verifiDate 
    */
    public void setVerifiDate(String verifiDate) {
        this.verifiDate = verifiDate;
    }
    /**
    * get the driver 
    * @return the driver
    */
    public String getDriver() {
        return this.driver;
    }

    /**
    * set the driver 
    */
    public void setDriver(String driver) {
        this.driver = driver;
    }
    /**
    * get the driverId 
    * @return the driverId
    */
    public String getDriverId() {
        return this.driverId;
    }

    /**
    * set the driverId 
    */
    public void setDriverId(String driverId) {
        this.driverId = driverId;
    }
    /**
    * get the d_userName 
    * @return the d_userName
    */
    public String getD_userName() {
        return this.d_userName;
    }

    /**
    * set the d_userName 
    */
    public void setD_userName(String d_userName) {
        this.d_userName = d_userName;
    }
    /**
    * get the synFlag 
    * @return the synFlag
    */
    public String getSynFlag() {
        return this.synFlag;
    }

    /**
    * set the synFlag 
    */
    public void setSynFlag(String synFlag) {
        this.synFlag = synFlag;
    }
    /**
    * get the vehicleName 
    * @return the vehicleName
    */
    public String getVehicleName() {
        return this.vehicleName;
    }

    /**
    * set the vehicleName 
    */
    public void setVehicleName(String vehicleName) {
        this.vehicleName = vehicleName;
    }
    /**
    * get the rainCoatFlag 
    * @return the rainCoatFlag
    */
    public String getRainCoatFlag() {
        return this.rainCoatFlag;
    }

    /**
    * set the rainCoatFlag 
    */
    public void setRainCoatFlag(String rainCoatFlag) {
        this.rainCoatFlag = rainCoatFlag;
    }
    /**
    * get the actConsigneeId - 实际收货方ID
    * @return the actConsigneeId
    */
    public String getActConsigneeId() {
        return this.actConsigneeId;
    }

    /**
    * set the actConsigneeId - 实际收货方ID
    */
    public void setActConsigneeId(String actConsigneeId) {
        this.actConsigneeId = actConsigneeId;
    }
    /**
    * get the delFlag - 删除标志（0未删除）
    * @return the delFlag
    */
    public String getDelFlag() {
        return this.delFlag;
    }

    /**
    * set the delFlag - 删除标志（0未删除）
    */
    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }
    /**
    * get the shipVerifiCode 
    * @return the shipVerifiCode
    */
    public String getShipVerifiCode() {
        return this.shipVerifiCode;
    }

    /**
    * set the shipVerifiCode 
    */
    public void setShipVerifiCode(String shipVerifiCode) {
        this.shipVerifiCode = shipVerifiCode;
    }
    /**
    * get the verifiPerson 
    * @return the verifiPerson
    */
    public String getVerifiPerson() {
        return this.verifiPerson;
    }

    /**
    * set the verifiPerson 
    */
    public void setVerifiPerson(String verifiPerson) {
        this.verifiPerson = verifiPerson;
    }
    /**
    * get the entrustFlag 
    * @return the entrustFlag
    */
    public String getEntrustFlag() {
        return this.entrustFlag;
    }

    /**
    * set the entrustFlag 
    */
    public void setEntrustFlag(String entrustFlag) {
        this.entrustFlag = entrustFlag;
    }
    /**
    * get the ladingSpotAddrId - 装货地址ID
    * @return the ladingSpotAddrId
    */
    public String getLadingSpotAddrId() {
        return this.ladingSpotAddrId;
    }

    /**
    * set the ladingSpotAddrId - 装货地址ID
    */
    public void setLadingSpotAddrId(String ladingSpotAddrId) {
        this.ladingSpotAddrId = ladingSpotAddrId;
    }
    /**
    * get the manualNo - 手工单号
    * @return the manualNo
    */
    public String getManualNo() {
        return this.manualNo;
    }

    /**
    * set the manualNo - 手工单号
    */
    public void setManualNo(String manualNo) {
        this.manualNo = manualNo;
    }
    /**
    * get the billBankBatchId 
    * @return the billBankBatchId
    */
    public String getBillBankBatchId() {
        return this.billBankBatchId;
    }

    /**
    * set the billBankBatchId 
    */
    public void setBillBankBatchId(String billBankBatchId) {
        this.billBankBatchId = billBankBatchId;
    }
    /**
    * get the storeType 
    * @return the storeType
    */
    public String getStoreType() {
        return this.storeType;
    }

    /**
    * set the storeType 
    */
    public void setStoreType(String storeType) {
        this.storeType = storeType;
    }
    /**
    * get the blTotAmt - 总金额
    * @return the blTotAmt
    */
    public BigDecimal getBlTotAmt() {
        return this.blTotAmt;
    }

    /**
    * set the blTotAmt - 总金额
    */
    public void setBlTotAmt(BigDecimal blTotAmt) {
        this.blTotAmt = blTotAmt;
    }
    /**
    * get the consigneeType - 收货方类型
    * @return the consigneeType
    */
    public String getConsigneeType() {
        return this.consigneeType;
    }

    /**
    * set the consigneeType - 收货方类型
    */
    public void setConsigneeType(String consigneeType) {
        this.consigneeType = consigneeType;
    }
    /**
    * get the d_userNum - 用户编号
    * @return the d_userNum
    */
    public String getD_userNum() {
        return this.d_userNum;
    }

    /**
    * set the d_userNum - 用户编号
    */
    public void setD_userNum(String d_userNum) {
        this.d_userNum = d_userNum;
    }
    /**
    * get the billingTime - 开单时间
    * @return the billingTime
    */
    public String getBillingTime() {
        return this.billingTime;
    }

    /**
    * set the billingTime - 开单时间
    */
    public void setBillingTime(String billingTime) {
        this.billingTime = billingTime;
    }
    /**
    * get the applyNum - 申请数量
    * @return the applyNum
    */
    public String getApplyNum() {
        return this.applyNum;
    }

    /**
    * set the applyNum - 申请数量
    */
    public void setApplyNum(String applyNum) {
        this.applyNum = applyNum;
    }
    /**
    * get the arrearType 
    * @return the arrearType
    */
    public String getArrearType() {
        return this.arrearType;
    }

    /**
    * set the arrearType 
    */
    public void setArrearType(String arrearType) {
        this.arrearType = arrearType;
    }
    /**
    * get the segNo - 段号
    * @return the segNo
    */
    public String getSegNo() {
        return this.segNo;
    }

    /**
    * set the segNo - 段号
    */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }
    /**
    * get the printDateFirst 
    * @return the printDateFirst
    */
    public String getPrintDateFirst() {
        return this.printDateFirst;
    }

    /**
    * set the printDateFirst 
    */
    public void setPrintDateFirst(String printDateFirst) {
        this.printDateFirst = printDateFirst;
    }
    /**
    * get the outWeight - 出库重量
    * @return the outWeight
    */
    public BigDecimal getOutWeight() {
        return this.outWeight;
    }

    /**
    * set the outWeight - 出库重量
    */
    public void setOutWeight(BigDecimal outWeight) {
        this.outWeight = outWeight;
    }
    /**
    * get the driverPhone2 
    * @return the driverPhone2
    */
    public String getDriverPhone2() {
        return this.driverPhone2;
    }

    /**
    * set the driverPhone2 
    */
    public void setDriverPhone2(String driverPhone2) {
        this.driverPhone2 = driverPhone2;
    }
    /**
    * get the contractNum - 合同号
    * @return the contractNum
    */
    public String getContractNum() {
        return this.contractNum;
    }

    /**
    * set the contractNum - 合同号
    */
    public void setContractNum(String contractNum) {
        this.contractNum = contractNum;
    }
    /**
    * get the expiryDays - 有效期天数
    * @return the expiryDays
    */
    public Integer getExpiryDays() {
        return this.expiryDays;
    }

    /**
    * set the expiryDays - 有效期天数
    */
    public void setExpiryDays(Integer expiryDays) {
        this.expiryDays = expiryDays;
    }
    /**
    * get the transferObject 
    * @return the transferObject
    */
    public String getTransferObject() {
        return this.transferObject;
    }

    /**
    * set the transferObject 
    */
    public void setTransferObject(String transferObject) {
        this.transferObject = transferObject;
    }
    /**
    * get the consigneeNum - 收货方编号
    * @return the consigneeNum
    */
    public String getConsigneeNum() {
        return this.consigneeNum;
    }

    /**
    * set the consigneeNum - 收货方编号
    */
    public void setConsigneeNum(String consigneeNum) {
        this.consigneeNum = consigneeNum;
    }
    /**
    * get the resourceType 
    * @return the resourceType
    */
    public String getResourceType() {
        return this.resourceType;
    }

    /**
    * set the resourceType 
    */
    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }
    /**
    * get the transferNotiDesc 
    * @return the transferNotiDesc
    */
    public String getTransferNotiDesc() {
        return this.transferNotiDesc;
    }

    /**
    * set the transferNotiDesc 
    */
    public void setTransferNotiDesc(String transferNotiDesc) {
        this.transferNotiDesc = transferNotiDesc;
    }
    /**
    * get the verifiStatus - 核验状态
    * @return the verifiStatus
    */
    public String getVerifiStatus() {
        return this.verifiStatus;
    }

    /**
    * set the verifiStatus - 核验状态
    */
    public void setVerifiStatus(String verifiStatus) {
        this.verifiStatus = verifiStatus;
    }
    /**
    * get the ladingSpotContactor - 装货地联系人
    * @return the ladingSpotContactor
    */
    public String getLadingSpotContactor() {
        return this.ladingSpotContactor;
    }

    /**
    * set the ladingSpotContactor - 装货地联系人
    */
    public void setLadingSpotContactor(String ladingSpotContactor) {
        this.ladingSpotContactor = ladingSpotContactor;
    }
    /**
    * get the userNum - 用户编号
    * @return the userNum
    */
    public String getUserNum() {
        return this.userNum;
    }

    /**
    * set the userNum - 用户编号
    */
    public void setUserNum(String userNum) {
        this.userNum = userNum;
    }
    /**
    * get the activeEndTime - 有效截止时间
    * @return the activeEndTime
    */
    public String getActiveEndTime() {
        return this.activeEndTime;
    }

    /**
    * set the activeEndTime - 有效截止时间
    */
    public void setActiveEndTime(String activeEndTime) {
        this.activeEndTime = activeEndTime;
    }
    /**
    * get the planLoadQty - 计划装载量
    * @return the planLoadQty
    */
    public BigDecimal getPlanLoadQty() {
        return this.planLoadQty;
    }

    /**
    * set the planLoadQty - 计划装载量
    */
    public void setPlanLoadQty(BigDecimal planLoadQty) {
        this.planLoadQty = planLoadQty;
    }
    /**
    * get the storeSettleType 
    * @return the storeSettleType
    */
    public String getStoreSettleType() {
        return this.storeSettleType;
    }

    /**
    * set the storeSettleType 
    */
    public void setStoreSettleType(String storeSettleType) {
        this.storeSettleType = storeSettleType;
    }
    /**
    * get the archiveFlag 
    * @return the archiveFlag
    */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
    * set the archiveFlag 
    */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }
    /**
    * get the consignId 
    * @return the consignId
    */
    public String getConsignId() {
        return this.consignId;
    }

    /**
    * set the consignId 
    */
    public void setConsignId(String consignId) {
        this.consignId = consignId;
    }
    /**
    * get the uuid - 唯一标识（主键）
    * @return the uuid
    */
    public String getUuid() {
        return this.uuid;
    }

    /**
    * set the uuid - 唯一标识（主键）
    */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }
    /**
    * get the consignMark 
    * @return the consignMark
    */
    public String getConsignMark() {
        return this.consignMark;
    }

    /**
    * set the consignMark 
    */
    public void setConsignMark(String consignMark) {
        this.consignMark = consignMark;
    }
    /**
    * get the recRevisor - 记录修改人ID
    * @return the recRevisor
    */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
    * set the recRevisor - 记录修改人ID
    */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }
    /**
    * get the destSpotAddr - 目的地地址
    * @return the destSpotAddr
    */
    public String getDestSpotAddr() {
        return this.destSpotAddr;
    }

    /**
    * set the destSpotAddr - 目的地地址
    */
    public void setDestSpotAddr(String destSpotAddr) {
        this.destSpotAddr = destSpotAddr;
    }
    /**
    * get the ladingBillType - 提单类型
    * @return the ladingBillType
    */
    public String getLadingBillType() {
        return this.ladingBillType;
    }

    /**
    * set the ladingBillType - 提单类型
    */
    public void setLadingBillType(String ladingBillType) {
        this.ladingBillType = ladingBillType;
    }
    /**
    * get the ladingBillStatus - 提单状态
    * @return the ladingBillStatus
    */
    public String getLadingBillStatus() {
        return this.ladingBillStatus;
    }

    /**
    * set the ladingBillStatus - 提单状态
    */
    public void setLadingBillStatus(String ladingBillStatus) {
        this.ladingBillStatus = ladingBillStatus;
    }
    /**
    * get the processFactoryId 
    * @return the processFactoryId
    */
    public String getProcessFactoryId() {
        return this.processFactoryId;
    }

    /**
    * set the processFactoryId 
    */
    public void setProcessFactoryId(String processFactoryId) {
        this.processFactoryId = processFactoryId;
    }
    /**
    * get the planInWarhouseTime 
    * @return the planInWarhouseTime
    */
    public String getPlanInWarhouseTime() {
        return this.planInWarhouseTime;
    }

    /**
    * set the planInWarhouseTime 
    */
    public void setPlanInWarhouseTime(String planInWarhouseTime) {
        this.planInWarhouseTime = planInWarhouseTime;
    }
    /**
    * get the userNameAbbr 
    * @return the userNameAbbr
    */
    public String getUserNameAbbr() {
        return this.userNameAbbr;
    }

    /**
    * set the userNameAbbr 
    */
    public void setUserNameAbbr(String userNameAbbr) {
        this.userNameAbbr = userNameAbbr;
    }
    /**
    * get the verifyDate 
    * @return the verifyDate
    */
    public String getVerifyDate() {
        return this.verifyDate;
    }

    /**
    * set the verifyDate 
    */
    public void setVerifyDate(String verifyDate) {
        this.verifyDate = verifyDate;
    }
    /**
    * get the storeFeeSettleType - 仓储费结算类型
    * @return the storeFeeSettleType
    */
    public String getStoreFeeSettleType() {
        return this.storeFeeSettleType;
    }

    /**
    * set the storeFeeSettleType - 仓储费结算类型
    */
    public void setStoreFeeSettleType(String storeFeeSettleType) {
        this.storeFeeSettleType = storeFeeSettleType;
    }
    /**
    * get the privateRouteCode 
    * @return the privateRouteCode
    */
    public String getPrivateRouteCode() {
        return this.privateRouteCode;
    }

    /**
    * set the privateRouteCode 
    */
    public void setPrivateRouteCode(String privateRouteCode) {
        this.privateRouteCode = privateRouteCode;
    }
    /**
    * get the tradeCode - 贸易代码
    * @return the tradeCode
    */
    public String getTradeCode() {
        return this.tradeCode;
    }

    /**
    * set the tradeCode - 贸易代码
    */
    public void setTradeCode(String tradeCode) {
        this.tradeCode = tradeCode;
    }
    /**
    * get the vehicleCode 
    * @return the vehicleCode
    */
    public String getVehicleCode() {
        return this.vehicleCode;
    }

    /**
    * set the vehicleCode 
    */
    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }
    /**
    * get the billBankStatus 
    * @return the billBankStatus
    */
    public String getBillBankStatus() {
        return this.billBankStatus;
    }

    /**
    * set the billBankStatus 
    */
    public void setBillBankStatus(String billBankStatus) {
        this.billBankStatus = billBankStatus;
    }
    /**
    * get the vehicleNo - 车牌号
    * @return the vehicleNo
    */
    public String getVehicleNo() {
        return this.vehicleNo;
    }

    /**
    * set the vehicleNo - 车牌号
    */
    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }
    /**
    * get the transType - 运输类型
    * @return the transType
    */
    public String getTransType() {
        return this.transType;
    }

    /**
    * set the transType - 运输类型
    */
    public void setTransType(String transType) {
        this.transType = transType;
    }
    /**
    * get the tproviderContactId 
    * @return the tproviderContactId
    */
    public String getTproviderContactId() {
        return this.tproviderContactId;
    }

    /**
    * set the tproviderContactId 
    */
    public void setTproviderContactId(String tproviderContactId) {
        this.tproviderContactId = tproviderContactId;
    }
    /**
    * get the financeContract 
    * @return the financeContract
    */
    public String getFinanceContract() {
        return this.financeContract;
    }

    /**
    * set the financeContract 
    */
    public void setFinanceContract(String financeContract) {
        this.financeContract = financeContract;
    }
    /**
    * get the finUserName - 财务用户名称
    * @return the finUserName
    */
    public String getFinUserName() {
        return this.finUserName;
    }

    /**
    * set the finUserName - 财务用户名称
    */
    public void setFinUserName(String finUserName) {
        this.finUserName = finUserName;
    }
    /**
    * get the arrearsShipNo 
    * @return the arrearsShipNo
    */
    public String getArrearsShipNo() {
        return this.arrearsShipNo;
    }

    /**
    * set the arrearsShipNo 
    */
    public void setArrearsShipNo(String arrearsShipNo) {
        this.arrearsShipNo = arrearsShipNo;
    }
    /**
    * get the tproviderId - 运输商ID
    * @return the tproviderId
    */
    public String getTproviderId() {
        return this.tproviderId;
    }

    /**
    * set the tproviderId - 运输商ID
    */
    public void setTproviderId(String tproviderId) {
        this.tproviderId = tproviderId;
    }
    /**
    * get the transFeeSettleType - 运输费结算类型
    * @return the transFeeSettleType
    */
    public String getTransFeeSettleType() {
        return this.transFeeSettleType;
    }

    /**
    * set the transFeeSettleType - 运输费结算类型
    */
    public void setTransFeeSettleType(String transFeeSettleType) {
        this.transFeeSettleType = transFeeSettleType;
    }
    /**
    * get the planOutWarhouseTime 
    * @return the planOutWarhouseTime
    */
    public String getPlanOutWarhouseTime() {
        return this.planOutWarhouseTime;
    }

    /**
    * set the planOutWarhouseTime 
    */
    public void setPlanOutWarhouseTime(String planOutWarhouseTime) {
        this.planOutWarhouseTime = planOutWarhouseTime;
    }
    /**
    * get the destSpotTele - 目的地电话
    * @return the destSpotTele
    */
    public String getDestSpotTele() {
        return this.destSpotTele;
    }

    /**
    * set the destSpotTele - 目的地电话
    */
    public void setDestSpotTele(String destSpotTele) {
        this.destSpotTele = destSpotTele;
    }
    /**
    * get the destSpotAddrId 
    * @return the destSpotAddrId
    */
    public String getDestSpotAddrId() {
        return this.destSpotAddrId;
    }

    /**
    * set the destSpotAddrId 
    */
    public void setDestSpotAddrId(String destSpotAddrId) {
        this.destSpotAddrId = destSpotAddrId;
    }
    /**
    * get the arrearsShipAmount - 欠款金额
    * @return the arrearsShipAmount
    */
    public BigDecimal getArrearsShipAmount() {
        return this.arrearsShipAmount;
    }

    /**
    * set the arrearsShipAmount - 欠款金额
    */
    public void setArrearsShipAmount(BigDecimal arrearsShipAmount) {
        this.arrearsShipAmount = arrearsShipAmount;
    }
    /**
    * get the tproviderContact 
    * @return the tproviderContact
    */
    public String getTproviderContact() {
        return this.tproviderContact;
    }

    /**
    * set the tproviderContact 
    */
    public void setTproviderContact(String tproviderContact) {
        this.tproviderContact = tproviderContact;
    }
    /**
    * get the orderCustCname 
    * @return the orderCustCname
    */
    public String getOrderCustCname() {
        return this.orderCustCname;
    }

    /**
    * set the orderCustCname 
    */
    public void setOrderCustCname(String orderCustCname) {
        this.orderCustCname = orderCustCname;
    }
    /**
    * get the outFeeSettleType - 出库费结算类型
    * @return the outFeeSettleType
    */
    public String getOutFeeSettleType() {
        return this.outFeeSettleType;
    }

    /**
    * set the outFeeSettleType - 出库费结算类型
    */
    public void setOutFeeSettleType(String outFeeSettleType) {
        this.outFeeSettleType = outFeeSettleType;
    }
    /**
    * get the remark - 备注
    * @return the remark
    */
    public String getRemark() {
        return this.remark;
    }

    /**
    * set the remark - 备注
    */
    public void setRemark(String remark) {
        this.remark = remark;
    }
    /**
    * get the verifyMan 
    * @return the verifyMan
    */
    public String getVerifyMan() {
        return this.verifyMan;
    }

    /**
    * set the verifyMan 
    */
    public void setVerifyMan(String verifyMan) {
        this.verifyMan = verifyMan;
    }
    /**
    * get the requireFinishDate 
    * @return the requireFinishDate
    */
    public String getRequireFinishDate() {
        return this.requireFinishDate;
    }

    /**
    * set the requireFinishDate 
    */
    public void setRequireFinishDate(String requireFinishDate) {
        this.requireFinishDate = requireFinishDate;
    }
    /**
    * get the consignBillStatus 
    * @return the consignBillStatus
    */
    public String getConsignBillStatus() {
        return this.consignBillStatus;
    }

    /**
    * set the consignBillStatus 
    */
    public void setConsignBillStatus(String consignBillStatus) {
        this.consignBillStatus = consignBillStatus;
    }
    /**
    * get the consigneeName - 收货方名称
    * @return the consigneeName
    */
    public String getConsigneeName() {
        return this.consigneeName;
    }

    /**
    * set the consigneeName - 收货方名称
    */
    public void setConsigneeName(String consigneeName) {
        this.consigneeName = consigneeName;
    }
    /**
    * get the businessNo 
    * @return the businessNo
    */
    public String getBusinessNo() {
        return this.businessNo;
    }

    /**
    * set the businessNo 
    */
    public void setBusinessNo(String businessNo) {
        this.businessNo = businessNo;
    }
    /**
    * get the emergencyDeliveryTime 
    * @return the emergencyDeliveryTime
    */
    public String getEmergencyDeliveryTime() {
        return this.emergencyDeliveryTime;
    }

    /**
    * set the emergencyDeliveryTime 
    */
    public void setEmergencyDeliveryTime(String emergencyDeliveryTime) {
        this.emergencyDeliveryTime = emergencyDeliveryTime;
    }
    /**
    * get the destSpotId - 目的地ID
    * @return the destSpotId
    */
    public String getDestSpotId() {
        return this.destSpotId;
    }

    /**
    * set the destSpotId - 目的地ID
    */
    public void setDestSpotId(String destSpotId) {
        this.destSpotId = destSpotId;
    }
    /**
    * get the planLoadWt - 计划装载重量
    * @return the planLoadWt
    */
    public BigDecimal getPlanLoadWt() {
        return this.planLoadWt;
    }

    /**
    * set the planLoadWt - 计划装载重量
    */
    public void setPlanLoadWt(BigDecimal planLoadWt) {
        this.planLoadWt = planLoadWt;
    }
    /**
    * get the recCreateTime - 记录创建时间
    * @return the recCreateTime
    */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
    * set the recCreateTime - 记录创建时间
    */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }
    /**
    * get the tproviderContactPlate 
    * @return the tproviderContactPlate
    */
    public String getTproviderContactPlate() {
        return this.tproviderContactPlate;
    }

    /**
    * set the tproviderContactPlate 
    */
    public void setTproviderContactPlate(String tproviderContactPlate) {
        this.tproviderContactPlate = tproviderContactPlate;
    }
    /**
    * get the vehicleId 
    * @return the vehicleId
    */
    public String getVehicleId() {
        return this.vehicleId;
    }

    /**
    * set the vehicleId 
    */
    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }
    /**
    * get the activeStartTime - 有效开始时间
    * @return the activeStartTime
    */
    public String getActiveStartTime() {
        return this.activeStartTime;
    }

    /**
    * set the activeStartTime - 有效开始时间
    */
    public void setActiveStartTime(String activeStartTime) {
        this.activeStartTime = activeStartTime;
    }
    /**
    * get the deliveryType - 交付类型
    * @return the deliveryType
    */
    public String getDeliveryType() {
        return this.deliveryType;
    }

    /**
    * set the deliveryType - 交付类型
    */
    public void setDeliveryType(String deliveryType) {
        this.deliveryType = deliveryType;
    }
    /**
    * get the outPackQty - 出库包装数量
    * @return the outPackQty
    */
    public BigDecimal getOutPackQty() {
        return this.outPackQty;
    }

    /**
    * set the outPackQty - 出库包装数量
    */
    public void setOutPackQty(BigDecimal outPackQty) {
        this.outPackQty = outPackQty;
    }
    /**
    * get the ladingSpotName - 装货地名称
    * @return the ladingSpotName
    */
    public String getLadingSpotName() {
        return this.ladingSpotName;
    }

    /**
    * set the ladingSpotName - 装货地名称
    */
    public void setLadingSpotName(String ladingSpotName) {
        this.ladingSpotName = ladingSpotName;
    }
    /**
    * get the tproviderContactMobile 
    * @return the tproviderContactMobile
    */
    public String getTproviderContactMobile() {
        return this.tproviderContactMobile;
    }

    /**
    * set the tproviderContactMobile 
    */
    public void setTproviderContactMobile(String tproviderContactMobile) {
        this.tproviderContactMobile = tproviderContactMobile;
    }
    /**
    * get the transNo 
    * @return the transNo
    */
    public String getTransNo() {
        return this.transNo;
    }

    /**
    * set the transNo 
    */
    public void setTransNo(String transNo) {
        this.transNo = transNo;
    }
    /**
    * get the userName - 用户名称
    * @return the userName
    */
    public String getUserName() {
        return this.userName;
    }

    /**
    * set the userName - 用户名称
    */
    public void setUserName(String userName) {
        this.userName = userName;
    }
    /**
    * get the ladingSpotTele - 装货地电话
    * @return the ladingSpotTele
    */
    public String getLadingSpotTele() {
        return this.ladingSpotTele;
    }

    /**
    * set the ladingSpotTele - 装货地电话
    */
    public void setLadingSpotTele(String ladingSpotTele) {
        this.ladingSpotTele = ladingSpotTele;
    }
    /**
    * get the printNumber - 打印次数
    * @return the printNumber
    */
    public BigDecimal getPrintNumber() {
        return this.printNumber;
    }

    /**
    * set the printNumber - 打印次数
    */
    public void setPrintNumber(BigDecimal printNumber) {
        this.printNumber = printNumber;
    }
    /**
    * get the destCountry 
    * @return the destCountry
    */
    public String getDestCountry() {
        return this.destCountry;
    }

    /**
    * set the destCountry 
    */
    public void setDestCountry(String destCountry) {
        this.destCountry = destCountry;
    }
    /**
    * get the tproviderContactMobile2 
    * @return the tproviderContactMobile2
    */
    public String getTproviderContactMobile2() {
        return this.tproviderContactMobile2;
    }

    /**
    * set the tproviderContactMobile2 
    */
    public void setTproviderContactMobile2(String tproviderContactMobile2) {
        this.tproviderContactMobile2 = tproviderContactMobile2;
    }
    /**
    * get the tproviderName - 运输商名称
    * @return the tproviderName
    */
    public String getTproviderName() {
        return this.tproviderName;
    }

    /**
    * set the tproviderName - 运输商名称
    */
    public void setTproviderName(String tproviderName) {
        this.tproviderName = tproviderName;
    }
    /**
    * get the totalWeight - 总重量
    * @return the totalWeight
    */
    public BigDecimal getTotalWeight() {
        return this.totalWeight;
    }

    /**
    * set the totalWeight - 总重量
    */
    public void setTotalWeight(BigDecimal totalWeight) {
        this.totalWeight = totalWeight;
    }
    /**
    * get the driverPhone 
    * @return the driverPhone
    */
    public String getDriverPhone() {
        return this.driverPhone;
    }

    /**
    * set the driverPhone 
    */
    public void setDriverPhone(String driverPhone) {
        this.driverPhone = driverPhone;
    }

    /**
    * get the value from Map
    */
    public void fromMap(Map map) {
        setActConsigneeName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("actConsigneeName")), actConsigneeName));
        setLadingSpotAddr(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ladingSpotAddr")), ladingSpotAddr));
        setTotalPackQty(NumberUtils.toBigDecimal(StringUtils.toString(map.get("totalPackQty")), totalPackQty));
        setTransferNotiSystem(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("transferNotiSystem")), transferNotiSystem));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setProcessFactoryName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processFactoryName")), processFactoryName));
        setPrivateRouteName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("privateRouteName")), privateRouteName));
        setLadingBillId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ladingBillId")), ladingBillId));
        setTenantUser(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantUser")), tenantUser));
        setDestSpotContactor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("destSpotContactor")), destSpotContactor));
        setDeliveryDateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deliveryDateTime")), deliveryDateTime));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setPrintMan(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("printMan")), printMan));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setPrintBatchId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("printBatchId")), printBatchId));
        setVerifiSource(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("verifiSource")), verifiSource));
        setNoPaperFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("noPaperFlag")), noPaperFlag));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setDestSpotName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("destSpotName")), destSpotName));
        setBillingMethod(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("billingMethod")), billingMethod));
        setPrintDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("printDate")), printDate));
        setLogisticSchemeNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("logisticSchemeNo")), logisticSchemeNo));
        setFinUserId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("finUserId")), finUserId));
        setBillExpiProcMark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("billExpiProcMark")), billExpiProcMark));
        setLadingSpotId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ladingSpotId")), ladingSpotId));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setVerifiDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("verifiDate")), verifiDate));
        setDriver(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("driver")), driver));
        setDriverId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("driverId")), driverId));
        setD_userName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("d_userName")), d_userName));
        setSynFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("synFlag")), synFlag));
        setVehicleName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("vehicleName")), vehicleName));
        setRainCoatFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("rainCoatFlag")), rainCoatFlag));
        setActConsigneeId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("actConsigneeId")), actConsigneeId));
        setDelFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("delFlag")), delFlag));
        setShipVerifiCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("shipVerifiCode")), shipVerifiCode));
        setVerifiPerson(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("verifiPerson")), verifiPerson));
        setEntrustFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("entrustFlag")), entrustFlag));
        setLadingSpotAddrId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ladingSpotAddrId")), ladingSpotAddrId));
        setManualNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("manualNo")), manualNo));
        setBillBankBatchId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("billBankBatchId")), billBankBatchId));
        setStoreType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("storeType")), storeType));
        setBlTotAmt(NumberUtils.toBigDecimal(StringUtils.toString(map.get("blTotAmt")), blTotAmt));
        setConsigneeType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("consigneeType")), consigneeType));
        setD_userNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("d_userNum")), d_userNum));
        setBillingTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("billingTime")), billingTime));
        setApplyNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("applyNum")), applyNum));
        setArrearType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("arrearType")), arrearType));
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setPrintDateFirst(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("printDateFirst")), printDateFirst));
        setOutWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("outWeight")), outWeight));
        setDriverPhone2(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("driverPhone2")), driverPhone2));
        setContractNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("contractNum")), contractNum));
        setExpiryDays(NumberUtils.toInteger(StringUtils.toString(map.get("expiryDays")), expiryDays));
        setTransferObject(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("transferObject")), transferObject));
        setConsigneeNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("consigneeNum")), consigneeNum));
        setResourceType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("resourceType")), resourceType));
        setTransferNotiDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("transferNotiDesc")), transferNotiDesc));
        setVerifiStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("verifiStatus")), verifiStatus));
        setLadingSpotContactor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ladingSpotContactor")), ladingSpotContactor));
        setUserNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("userNum")), userNum));
        setActiveEndTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("activeEndTime")), activeEndTime));
        setPlanLoadQty(NumberUtils.toBigDecimal(StringUtils.toString(map.get("planLoadQty")), planLoadQty));
        setStoreSettleType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("storeSettleType")), storeSettleType));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setConsignId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("consignId")), consignId));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setConsignMark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("consignMark")), consignMark));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setDestSpotAddr(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("destSpotAddr")), destSpotAddr));
        setLadingBillType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ladingBillType")), ladingBillType));
        setLadingBillStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ladingBillStatus")), ladingBillStatus));
        setProcessFactoryId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processFactoryId")), processFactoryId));
        setPlanInWarhouseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("planInWarhouseTime")), planInWarhouseTime));
        setUserNameAbbr(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("userNameAbbr")), userNameAbbr));
        setVerifyDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("verifyDate")), verifyDate));
        setStoreFeeSettleType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("storeFeeSettleType")), storeFeeSettleType));
        setPrivateRouteCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("privateRouteCode")), privateRouteCode));
        setTradeCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tradeCode")), tradeCode));
        setVehicleCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("vehicleCode")), vehicleCode));
        setBillBankStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("billBankStatus")), billBankStatus));
        setVehicleNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("vehicleNo")), vehicleNo));
        setTransType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("transType")), transType));
        setTproviderContactId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tproviderContactId")), tproviderContactId));
        setFinanceContract(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("financeContract")), financeContract));
        setFinUserName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("finUserName")), finUserName));
        setArrearsShipNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("arrearsShipNo")), arrearsShipNo));
        setTproviderId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tproviderId")), tproviderId));
        setTransFeeSettleType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("transFeeSettleType")), transFeeSettleType));
        setPlanOutWarhouseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("planOutWarhouseTime")), planOutWarhouseTime));
        setDestSpotTele(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("destSpotTele")), destSpotTele));
        setDestSpotAddrId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("destSpotAddrId")), destSpotAddrId));
        setArrearsShipAmount(NumberUtils.toBigDecimal(StringUtils.toString(map.get("arrearsShipAmount")), arrearsShipAmount));
        setTproviderContact(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tproviderContact")), tproviderContact));
        setOrderCustCname(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("orderCustCname")), orderCustCname));
        setOutFeeSettleType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("outFeeSettleType")), outFeeSettleType));
        setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
        setVerifyMan(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("verifyMan")), verifyMan));
        setRequireFinishDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("requireFinishDate")), requireFinishDate));
        setConsignBillStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("consignBillStatus")), consignBillStatus));
        setConsigneeName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("consigneeName")), consigneeName));
        setBusinessNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("businessNo")), businessNo));
        setEmergencyDeliveryTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("emergencyDeliveryTime")), emergencyDeliveryTime));
        setDestSpotId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("destSpotId")), destSpotId));
        setPlanLoadWt(NumberUtils.toBigDecimal(StringUtils.toString(map.get("planLoadWt")), planLoadWt));
        //TODO recCreateTime cannot generate automatically ,LocalDateTime dont support
        setTproviderContactPlate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tproviderContactPlate")), tproviderContactPlate));
        setVehicleId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("vehicleId")), vehicleId));
        setActiveStartTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("activeStartTime")), activeStartTime));
        setDeliveryType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deliveryType")), deliveryType));
        setOutPackQty(NumberUtils.toBigDecimal(StringUtils.toString(map.get("outPackQty")), outPackQty));
        setLadingSpotName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ladingSpotName")), ladingSpotName));
        setTproviderContactMobile(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tproviderContactMobile")), tproviderContactMobile));
        setTransNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("transNo")), transNo));
        setUserName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("userName")), userName));
        setLadingSpotTele(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ladingSpotTele")), ladingSpotTele));
        setPrintNumber(NumberUtils.toBigDecimal(StringUtils.toString(map.get("printNumber")), printNumber));
        setDestCountry(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("destCountry")), destCountry));
        setTproviderContactMobile2(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tproviderContactMobile2")), tproviderContactMobile2));
        setTproviderName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tproviderName")), tproviderName));
        setTotalWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("totalWeight")), totalWeight));
        setDriverPhone(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("driverPhone")), driverPhone));
    }

    /**
    * set the value to Map
    */
    public Map toMap() {
        Map map = new HashMap();
        map.put("actConsigneeName",StringUtils.toString(actConsigneeName, eiMetadata.getMeta("actConsigneeName")));
        map.put("ladingSpotAddr",StringUtils.toString(ladingSpotAddr, eiMetadata.getMeta("ladingSpotAddr")));
        map.put("totalPackQty",StringUtils.toString(totalPackQty, eiMetadata.getMeta("totalPackQty")));
        map.put("transferNotiSystem",StringUtils.toString(transferNotiSystem, eiMetadata.getMeta("transferNotiSystem")));
        map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("processFactoryName",StringUtils.toString(processFactoryName, eiMetadata.getMeta("processFactoryName")));
        map.put("privateRouteName",StringUtils.toString(privateRouteName, eiMetadata.getMeta("privateRouteName")));
        map.put("ladingBillId",StringUtils.toString(ladingBillId, eiMetadata.getMeta("ladingBillId")));
        map.put("tenantUser",StringUtils.toString(tenantUser, eiMetadata.getMeta("tenantUser")));
        map.put("destSpotContactor",StringUtils.toString(destSpotContactor, eiMetadata.getMeta("destSpotContactor")));
        map.put("deliveryDateTime",StringUtils.toString(deliveryDateTime, eiMetadata.getMeta("deliveryDateTime")));
        map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("printMan",StringUtils.toString(printMan, eiMetadata.getMeta("printMan")));
        map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("printBatchId",StringUtils.toString(printBatchId, eiMetadata.getMeta("printBatchId")));
        map.put("verifiSource",StringUtils.toString(verifiSource, eiMetadata.getMeta("verifiSource")));
        map.put("noPaperFlag",StringUtils.toString(noPaperFlag, eiMetadata.getMeta("noPaperFlag")));
        map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("destSpotName",StringUtils.toString(destSpotName, eiMetadata.getMeta("destSpotName")));
        map.put("billingMethod",StringUtils.toString(billingMethod, eiMetadata.getMeta("billingMethod")));
        map.put("printDate",StringUtils.toString(printDate, eiMetadata.getMeta("printDate")));
        map.put("logisticSchemeNo",StringUtils.toString(logisticSchemeNo, eiMetadata.getMeta("logisticSchemeNo")));
        map.put("finUserId",StringUtils.toString(finUserId, eiMetadata.getMeta("finUserId")));
        map.put("billExpiProcMark",StringUtils.toString(billExpiProcMark, eiMetadata.getMeta("billExpiProcMark")));
        map.put("ladingSpotId",StringUtils.toString(ladingSpotId, eiMetadata.getMeta("ladingSpotId")));
        map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("verifiDate",StringUtils.toString(verifiDate, eiMetadata.getMeta("verifiDate")));
        map.put("driver",StringUtils.toString(driver, eiMetadata.getMeta("driver")));
        map.put("driverId",StringUtils.toString(driverId, eiMetadata.getMeta("driverId")));
        map.put("d_userName",StringUtils.toString(d_userName, eiMetadata.getMeta("d_userName")));
        map.put("synFlag",StringUtils.toString(synFlag, eiMetadata.getMeta("synFlag")));
        map.put("vehicleName",StringUtils.toString(vehicleName, eiMetadata.getMeta("vehicleName")));
        map.put("rainCoatFlag",StringUtils.toString(rainCoatFlag, eiMetadata.getMeta("rainCoatFlag")));
        map.put("actConsigneeId",StringUtils.toString(actConsigneeId, eiMetadata.getMeta("actConsigneeId")));
        map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("shipVerifiCode",StringUtils.toString(shipVerifiCode, eiMetadata.getMeta("shipVerifiCode")));
        map.put("verifiPerson",StringUtils.toString(verifiPerson, eiMetadata.getMeta("verifiPerson")));
        map.put("entrustFlag",StringUtils.toString(entrustFlag, eiMetadata.getMeta("entrustFlag")));
        map.put("ladingSpotAddrId",StringUtils.toString(ladingSpotAddrId, eiMetadata.getMeta("ladingSpotAddrId")));
        map.put("manualNo",StringUtils.toString(manualNo, eiMetadata.getMeta("manualNo")));
        map.put("billBankBatchId",StringUtils.toString(billBankBatchId, eiMetadata.getMeta("billBankBatchId")));
        map.put("storeType",StringUtils.toString(storeType, eiMetadata.getMeta("storeType")));
        map.put("blTotAmt",StringUtils.toString(blTotAmt, eiMetadata.getMeta("blTotAmt")));
        map.put("consigneeType",StringUtils.toString(consigneeType, eiMetadata.getMeta("consigneeType")));
        map.put("d_userNum",StringUtils.toString(d_userNum, eiMetadata.getMeta("d_userNum")));
        map.put("billingTime",StringUtils.toString(billingTime, eiMetadata.getMeta("billingTime")));
        map.put("applyNum",StringUtils.toString(applyNum, eiMetadata.getMeta("applyNum")));
        map.put("arrearType",StringUtils.toString(arrearType, eiMetadata.getMeta("arrearType")));
        map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("printDateFirst",StringUtils.toString(printDateFirst, eiMetadata.getMeta("printDateFirst")));
        map.put("outWeight",StringUtils.toString(outWeight, eiMetadata.getMeta("outWeight")));
        map.put("driverPhone2",StringUtils.toString(driverPhone2, eiMetadata.getMeta("driverPhone2")));
        map.put("contractNum",StringUtils.toString(contractNum, eiMetadata.getMeta("contractNum")));
        map.put("expiryDays",StringUtils.toString(expiryDays, eiMetadata.getMeta("expiryDays")));
        map.put("transferObject",StringUtils.toString(transferObject, eiMetadata.getMeta("transferObject")));
        map.put("consigneeNum",StringUtils.toString(consigneeNum, eiMetadata.getMeta("consigneeNum")));
        map.put("resourceType",StringUtils.toString(resourceType, eiMetadata.getMeta("resourceType")));
        map.put("transferNotiDesc",StringUtils.toString(transferNotiDesc, eiMetadata.getMeta("transferNotiDesc")));
        map.put("verifiStatus",StringUtils.toString(verifiStatus, eiMetadata.getMeta("verifiStatus")));
        map.put("ladingSpotContactor",StringUtils.toString(ladingSpotContactor, eiMetadata.getMeta("ladingSpotContactor")));
        map.put("userNum",StringUtils.toString(userNum, eiMetadata.getMeta("userNum")));
        map.put("activeEndTime",StringUtils.toString(activeEndTime, eiMetadata.getMeta("activeEndTime")));
        map.put("planLoadQty",StringUtils.toString(planLoadQty, eiMetadata.getMeta("planLoadQty")));
        map.put("storeSettleType",StringUtils.toString(storeSettleType, eiMetadata.getMeta("storeSettleType")));
        map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("consignId",StringUtils.toString(consignId, eiMetadata.getMeta("consignId")));
        map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("consignMark",StringUtils.toString(consignMark, eiMetadata.getMeta("consignMark")));
        map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("destSpotAddr",StringUtils.toString(destSpotAddr, eiMetadata.getMeta("destSpotAddr")));
        map.put("ladingBillType",StringUtils.toString(ladingBillType, eiMetadata.getMeta("ladingBillType")));
        map.put("ladingBillStatus",StringUtils.toString(ladingBillStatus, eiMetadata.getMeta("ladingBillStatus")));
        map.put("processFactoryId",StringUtils.toString(processFactoryId, eiMetadata.getMeta("processFactoryId")));
        map.put("planInWarhouseTime",StringUtils.toString(planInWarhouseTime, eiMetadata.getMeta("planInWarhouseTime")));
        map.put("userNameAbbr",StringUtils.toString(userNameAbbr, eiMetadata.getMeta("userNameAbbr")));
        map.put("verifyDate",StringUtils.toString(verifyDate, eiMetadata.getMeta("verifyDate")));
        map.put("storeFeeSettleType",StringUtils.toString(storeFeeSettleType, eiMetadata.getMeta("storeFeeSettleType")));
        map.put("privateRouteCode",StringUtils.toString(privateRouteCode, eiMetadata.getMeta("privateRouteCode")));
        map.put("tradeCode",StringUtils.toString(tradeCode, eiMetadata.getMeta("tradeCode")));
        map.put("vehicleCode",StringUtils.toString(vehicleCode, eiMetadata.getMeta("vehicleCode")));
        map.put("billBankStatus",StringUtils.toString(billBankStatus, eiMetadata.getMeta("billBankStatus")));
        map.put("vehicleNo",StringUtils.toString(vehicleNo, eiMetadata.getMeta("vehicleNo")));
        map.put("transType",StringUtils.toString(transType, eiMetadata.getMeta("transType")));
        map.put("tproviderContactId",StringUtils.toString(tproviderContactId, eiMetadata.getMeta("tproviderContactId")));
        map.put("financeContract",StringUtils.toString(financeContract, eiMetadata.getMeta("financeContract")));
        map.put("finUserName",StringUtils.toString(finUserName, eiMetadata.getMeta("finUserName")));
        map.put("arrearsShipNo",StringUtils.toString(arrearsShipNo, eiMetadata.getMeta("arrearsShipNo")));
        map.put("tproviderId",StringUtils.toString(tproviderId, eiMetadata.getMeta("tproviderId")));
        map.put("transFeeSettleType",StringUtils.toString(transFeeSettleType, eiMetadata.getMeta("transFeeSettleType")));
        map.put("planOutWarhouseTime",StringUtils.toString(planOutWarhouseTime, eiMetadata.getMeta("planOutWarhouseTime")));
        map.put("destSpotTele",StringUtils.toString(destSpotTele, eiMetadata.getMeta("destSpotTele")));
        map.put("destSpotAddrId",StringUtils.toString(destSpotAddrId, eiMetadata.getMeta("destSpotAddrId")));
        map.put("arrearsShipAmount",StringUtils.toString(arrearsShipAmount, eiMetadata.getMeta("arrearsShipAmount")));
        map.put("tproviderContact",StringUtils.toString(tproviderContact, eiMetadata.getMeta("tproviderContact")));
        map.put("orderCustCname",StringUtils.toString(orderCustCname, eiMetadata.getMeta("orderCustCname")));
        map.put("outFeeSettleType",StringUtils.toString(outFeeSettleType, eiMetadata.getMeta("outFeeSettleType")));
        map.put("remark",StringUtils.toString(remark, eiMetadata.getMeta("remark")));
        map.put("verifyMan",StringUtils.toString(verifyMan, eiMetadata.getMeta("verifyMan")));
        map.put("requireFinishDate",StringUtils.toString(requireFinishDate, eiMetadata.getMeta("requireFinishDate")));
        map.put("consignBillStatus",StringUtils.toString(consignBillStatus, eiMetadata.getMeta("consignBillStatus")));
        map.put("consigneeName",StringUtils.toString(consigneeName, eiMetadata.getMeta("consigneeName")));
        map.put("businessNo",StringUtils.toString(businessNo, eiMetadata.getMeta("businessNo")));
        map.put("emergencyDeliveryTime",StringUtils.toString(emergencyDeliveryTime, eiMetadata.getMeta("emergencyDeliveryTime")));
        map.put("destSpotId",StringUtils.toString(destSpotId, eiMetadata.getMeta("destSpotId")));
        map.put("planLoadWt",StringUtils.toString(planLoadWt, eiMetadata.getMeta("planLoadWt")));
        map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("tproviderContactPlate",StringUtils.toString(tproviderContactPlate, eiMetadata.getMeta("tproviderContactPlate")));
        map.put("vehicleId",StringUtils.toString(vehicleId, eiMetadata.getMeta("vehicleId")));
        map.put("activeStartTime",StringUtils.toString(activeStartTime, eiMetadata.getMeta("activeStartTime")));
        map.put("deliveryType",StringUtils.toString(deliveryType, eiMetadata.getMeta("deliveryType")));
        map.put("outPackQty",StringUtils.toString(outPackQty, eiMetadata.getMeta("outPackQty")));
        map.put("ladingSpotName",StringUtils.toString(ladingSpotName, eiMetadata.getMeta("ladingSpotName")));
        map.put("tproviderContactMobile",StringUtils.toString(tproviderContactMobile, eiMetadata.getMeta("tproviderContactMobile")));
        map.put("transNo",StringUtils.toString(transNo, eiMetadata.getMeta("transNo")));
        map.put("userName",StringUtils.toString(userName, eiMetadata.getMeta("userName")));
        map.put("ladingSpotTele",StringUtils.toString(ladingSpotTele, eiMetadata.getMeta("ladingSpotTele")));
        map.put("printNumber",StringUtils.toString(printNumber, eiMetadata.getMeta("printNumber")));
        map.put("destCountry",StringUtils.toString(destCountry, eiMetadata.getMeta("destCountry")));
        map.put("tproviderContactMobile2",StringUtils.toString(tproviderContactMobile2, eiMetadata.getMeta("tproviderContactMobile2")));
        map.put("tproviderName",StringUtils.toString(tproviderName, eiMetadata.getMeta("tproviderName")));
        map.put("totalWeight",StringUtils.toString(totalWeight, eiMetadata.getMeta("totalWeight")));
        map.put("driverPhone",StringUtils.toString(driverPhone, eiMetadata.getMeta("driverPhone")));
        return map;
    }
}