package com.baosight.imom.common.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.*;

/**
 * 主子表Excel导出工具类
 * 支持主表和子表数据的合并导出，子表数据作为主表数据的扩展列
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public class MasterDetailExcelExporter {
    
    private static final Logger log = LoggerFactory.getLogger(MasterDetailExcelExporter.class);
    
    /**
     * 主子表数据导出到文件存储
     * 
     * @param inInfo 包含导出配置的EiInfo
     * @param masterDataList 主表数据列表
     * @param detailDataList 子表数据列表
     * @param loginMap 登录信息
     * @return 包含下载链接的Map
     */
    public static Map<String, String> exportMasterDetail2FileStorage(
            EiInfo inInfo, 
            List<Map<String, Object>> masterDataList,
            List<Map<String, Object>> detailDataList,
            Map<String, Object> loginMap) {
        
        long startTime = System.currentTimeMillis();
        log.info("主子表Excel导出开始: 主表{}条, 子表{}条", 
                masterDataList.size(), detailDataList.size());
        
        try {
            // 解析导出配置
            ExportCol exportCol = parseMasterDetailExportCol(inInfo);
            
            // 合并主子表数据
            List<Map<String, Object>> mergedDataList = mergeMasterDetailData(
                    masterDataList, detailDataList, exportCol.getMasterKey(), exportCol.getDetailKey());
            
            // 转换为Excel数据格式
            List<List<Object>> dataList = listMap2ListObj(mergedDataList, exportCol.getTitleColList());
            
            // 生成Excel文件流
            ByteArrayOutputStream bos = createMasterDetailWorkbookStream(
                    exportCol.getTitleList(), dataList, exportCol.getSheetName());
            
            // 上传到文件存储
            FileItem fileItem = EasyExcelExportUtil.createFileItem(
                    new ByteArrayInputStream(bos.toByteArray()), exportCol.getFileName());
            MultipartFile multipartFile = new CommonsMultipartFile(fileItem);
            
            Map<String, String> docMap = new HashMap<>();
            String downloadUrl = FileUtils.uploadFileByOriginalName((CommonsMultipartFile) multipartFile, "/pageDownload");
            docMap.put("docUrl", downloadUrl);
            
            log.info("主子表Excel导出完成: {}, 耗时: {}s", 
                    docMap, (System.currentTimeMillis() - startTime) / 1000d);
            
            return docMap;
            
        } catch (Exception e) {
            log.error("主子表Excel导出失败", e);
            throw new RuntimeException("主子表Excel导出失败: " + e.getMessage());
        }
    }
    
    /**
     * 解析主子表导出配置
     */
    private static ExportCol parseMasterDetailExportCol(EiInfo inInfo) {
        EiBlock exportColumnBlock = inInfo.getBlock("exportColumnBlock");
        if (exportColumnBlock == null) {
            throw new RuntimeException("导出列未配置");
        }
        
        String fileName = exportColumnBlock.getString("fileName");
        String sheetName = exportColumnBlock.getString("sheetName");
        String masterKey = exportColumnBlock.getString("masterKey"); // 主表关联键
        String detailKey = exportColumnBlock.getString("detailKey"); // 子表关联键
        
        if (StringUtils.isBlank(masterKey) || StringUtils.isBlank(detailKey)) {
            throw new RuntimeException("主子表关联键未配置");
        }
        
        List<Map<String, Object>> columns = (List<Map<String, Object>>) exportColumnBlock.get("columns");
        if (CollectionUtils.isEmpty(columns)) {
            throw new RuntimeException("导出列配置为空");
        }
        
        // 按索引排序
        columns.sort((o1, o2) -> MapUtils.getInteger(o1, "idx").compareTo(MapUtils.getInteger(o2, "idx")));
        
        List<String> titleList = new ArrayList<>();
        List<String> titleColList = new ArrayList<>();
        
        for (Map<String, Object> column : columns) {
            String colName = MapUtils.getString(column, "column");
            String title = MapUtils.getString(column, "title");
            titleList.add(title);
            titleColList.add(colName);
        }
        
        return new ExportCol(titleList, titleColList, fileName, sheetName, masterKey, detailKey);
    }
    
    /**
     * 合并主子表数据
     * 
     * @param masterDataList 主表数据
     * @param detailDataList 子表数据
     * @param masterKey 主表关联键
     * @param detailKey 子表关联键
     * @return 合并后的数据列表
     */
    private static List<Map<String, Object>> mergeMasterDetailData(
            List<Map<String, Object>> masterDataList,
            List<Map<String, Object>> detailDataList,
            String masterKey,
            String detailKey) {
        
        List<Map<String, Object>> mergedDataList = new ArrayList<>();
        
        if (CollectionUtils.isEmpty(masterDataList)) {
            return mergedDataList;
        }
        
        // 将子表数据按关联键分组
        Map<String, List<Map<String, Object>>> detailGroupMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(detailDataList)) {
            for (Map<String, Object> detail : detailDataList) {
                String key = MapUtils.getString(detail, detailKey);
                if (StringUtils.isNotBlank(key)) {
                    detailGroupMap.computeIfAbsent(key, k -> new ArrayList<>()).add(detail);
                }
            }
        }
        
        // 遍历主表数据，合并对应的子表数据
        for (Map<String, Object> master : masterDataList) {
            String masterKeyValue = MapUtils.getString(master, masterKey);
            List<Map<String, Object>> relatedDetails = detailGroupMap.get(masterKeyValue);
            
            if (CollectionUtils.isEmpty(relatedDetails)) {
                // 没有子表数据，直接添加主表数据
                mergedDataList.add(new HashMap<>(master));
            } else {
                // 有子表数据，为每个子表记录创建一行
                for (Map<String, Object> detail : relatedDetails) {
                    Map<String, Object> mergedRow = new HashMap<>(master);
                    // 添加子表数据，使用前缀避免字段名冲突
                    for (Map.Entry<String, Object> entry : detail.entrySet()) {
                        String key = entry.getKey();
                        if (!masterKey.equals(key)) { // 避免重复的关联键
                            mergedRow.put("detail_" + key, entry.getValue());
                        }
                    }
                    mergedDataList.add(mergedRow);
                }
            }
        }
        
        return mergedDataList;
    }
    
    /**
     * 将Map列表转换为Excel数据格式
     */
    private static List<List<Object>> listMap2ListObj(List<Map<String, Object>> listMap, List<String> titleColList) {
        List<List<Object>> dataList = new ArrayList<>();
        if (CollectionUtils.isEmpty(listMap) || CollectionUtils.isEmpty(titleColList)) {
            return dataList;
        }
        
        for (Map<String, Object> map : listMap) {
            List<Object> dataRow = new ArrayList<>();
            for (String col : titleColList) {
                dataRow.add(map.get(col));
            }
            dataList.add(dataRow);
        }
        return dataList;
    }
    
    /**
     * 创建主子表Excel工作簿流
     */
    private static ByteArrayOutputStream createMasterDetailWorkbookStream(
            List<String> headList, List<List<Object>> dataList, String sheetName) {
        
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try {
            // 构造表头
            List<List<String>> head = new ArrayList<>();
            for (String s : headList) {
                head.add(Collections.singletonList(s));
            }
            
            // 创建Excel写入器
            ExcelWriter excelWriter = EasyExcel
                    .write(bos)
                    .head(head)
                    .build();
            
            WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).build();
            excelWriter.write(dataList, writeSheet);
            excelWriter.finish();
            
        } catch (Exception e) {
            try {
                bos.close();
            } catch (Exception ex) {
                log.error("关闭输出流失败", ex);
            }
            throw e;
        }
        return bos;
    }
    
    /**
     * 导出列配置类
     */
    public static class ExportCol {
        private List<String> titleList;
        private List<String> titleColList;
        private String fileName;
        private String sheetName;
        private String masterKey;
        private String detailKey;
        
        public ExportCol(List<String> titleList, List<String> titleColList, String fileName, 
                        String sheetName, String masterKey, String detailKey) {
            this.titleList = titleList;
            this.titleColList = titleColList;
            this.fileName = fileName;
            this.sheetName = sheetName;
            this.masterKey = masterKey;
            this.detailKey = detailKey;
        }
        
        // Getters
        public List<String> getTitleList() { return titleList; }
        public List<String> getTitleColList() { return titleColList; }
        public String getFileName() { return fileName; }
        public String getSheetName() { return sheetName; }
        public String getMasterKey() { return masterKey; }
        public String getDetailKey() { return detailKey; }
    }
} 