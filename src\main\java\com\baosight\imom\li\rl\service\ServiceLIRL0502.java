package com.baosight.imom.li.rl.service;


import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.li.rl.dao.LIRL0501;
import com.baosight.imom.li.rl.dao.LIRL0502;
import com.baosight.iplat4j.core.data.id.UUIDHexIdGenerator;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import org.apache.commons.collections.MapUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @Author: 谢徐皓
 * @Description: ${车辆类型管理}
 * @Date: 2024/11/26 16:00
 * @Version: 1.0
 */
public class ServiceLIRL0502 extends ServiceBase {

    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIRL0502().eiMetadata);
        return inInfo;
    }

    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        Map queryMap = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
        String segNo = MapUtils.getString(queryMap, "segNo", "");
        String segName = MapUtils.getString(queryMap, "segName", "");
        if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
            String massage = "缺少业务单元代码不能查询！";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        outInfo = super.query(inInfo, LIRL0501.QUERYPAGE);
        return outInfo;
    }

    public EiInfo queryLitter(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        Map queryMap = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
        String segNo = MapUtils.getString(queryMap, "segNo", "");
        String segName = MapUtils.getString(queryMap, "segName", "");
        if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
            String massage = "缺少业务单元代码不能查询！";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        outInfo = super.query(inInfo, LIRL0501.QUERYPAGE);
        return outInfo;
    }
}
