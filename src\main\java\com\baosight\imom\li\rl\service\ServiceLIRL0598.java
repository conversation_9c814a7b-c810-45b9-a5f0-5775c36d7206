/**
* Generate time : 2025-07-04 15:31:29
* Version : 1.0
*/
package com.baosight.imom.li.rl.service;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.imom.common.li.domain.Tlirl0598;

public class ServiceLIRL0598 extends ServiceBase {

    public EiInfo initLoad(EiInfo inInfo) {
        return super.initLoad(inInfo,new Tlirl0598());
    }

    @Override
    public EiInfo query(EiInfo inInfo) {
        return super.query(inInfo,"tlirl0598.query",new Tlirl0598());
    }

    @Override
    public EiInfo insert(EiInfo inInfo) {
        return super.insert(inInfo,"tlirl0598.insert");
    }

    @Override
    public EiInfo update(EiInfo inInfo) {
        return super.update(inInfo,"tlirl0598.update");
    }

    @Override
    public EiInfo delete(EiInfo inInfo) {
        return super.delete(inInfo,"tlirl0598.delete");
    }

}