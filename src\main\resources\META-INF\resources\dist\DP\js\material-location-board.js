// 原料库位看板管理类 - 区域独立分页版本
class MaterialLocationBoard {
    constructor() {
        this.locations = []; // 库位基础数据
        this.materials = []; // 物料数据
        this.areas = []; // 动态生成的区域列表
        this.occupancyData = {}; // 各区域占用量数据
        this.totalOccupancy = 0; // 总占用量数据（后台返回的listSum）
        this.refreshInterval = null;
        this.areaPageIntervals = {}; // 各区域的翻页定时器
        this.areaDelayTimeouts = {}; // 各区域的延迟启动定时器
        this.finalRefreshTimeout = null; // 最终刷新定时器

        // 分页配置
        this.locationsPerArea = this.getLocationsPerArea(); // 根据屏幕宽度动态计算
        this.areaCurrentPages = {}; // 各区域当前页码
        this.areaTotalPages = {}; // 各区域总页数
        this.pageInterval = 20000; // 20秒翻页间隔
        this.refreshDataInterval = 20000; // 20秒数据刷新间隔
        this.finalWaitTime = 10000; // 所有区域翻页完成后等待10秒

        // 监听窗口大小变化
        this.setupResizeListener();

        this.init();
    }

    // 根据屏幕宽度获取每区域显示的库位数量
    getLocationsPerArea() {
        const screenWidth = window.innerWidth;
        const locationsPerArea = screenWidth <= 1200 ? 8 : 16;

        console.log(`屏幕宽度: ${screenWidth}px, 每区域库位数: ${locationsPerArea}`);

        return locationsPerArea;
    }

    // 设置窗口大小变化监听器
    setupResizeListener() {
        let resizeTimeout;
        this.resizeHandler = () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                const newLocationsPerArea = this.getLocationsPerArea();
                if (newLocationsPerArea !== this.locationsPerArea) {
                    console.log(`屏幕宽度变化，重新计算分页: ${this.locationsPerArea} -> ${newLocationsPerArea}`);
                    this.locationsPerArea = newLocationsPerArea;
                    this.recalculatePagination();
                }
            }, 300); // 防抖，300ms后执行
        };

        window.addEventListener('resize', this.resizeHandler);
    }

    // 重新计算分页
    recalculatePagination() {
        // 停止当前所有翻页
        this.stopAreaPagination();

        // 重新计算各区域分页信息
        this.areas.forEach(area => {
            this.areaCurrentPages[area.name] = 1; // 重置到第一页
            this.areaTotalPages[area.name] = Math.ceil(area.locations.length / this.locationsPerArea);
        });

        console.log('重新计算分页信息:', {
            locationsPerArea: this.locationsPerArea,
            currentPages: this.areaCurrentPages,
            totalPages: this.areaTotalPages
        });

        // 重新渲染看板
        this.renderBoard();

        // 重新启动分页
        this.startAreaPagination();
    }

    // 初始化
    init() {
        this.loadDataFromServer();
        this.updateDateTime();
        setInterval(() => this.updateDateTime(), 1000);

        // 绑定控制按钮事件
        this.bindControlEvents();

        // 不再启动定时数据刷新，改为基于翻页状态的刷新
        // this.startDataRefresh();
    }

    // 绑定控制按钮事件
    bindControlEvents() {
        // 刷新按钮
        $('#refreshBtn').on('click', () => {
            this.showRefreshToast();
            this.refresh();
        });

        // 全屏按钮
        $('#fullscreenBtn').on('click', () => {
            this.toggleFullscreen();
        });
    }

    // 显示刷新提示
    showRefreshToast() {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 2000,
                timerProgressBar: true,
                icon: 'info',
                title: '正在刷新数据...'
            });
        }
    }

    // 切换全屏
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.log('全屏失败:', err);
                if (typeof Swal !== 'undefined') {
                    Swal.fire({
                        toast: true,
                        position: 'top-end',
                        showConfirmButton: false,
                        timer: 2000,
                        icon: 'warning',
                        title: '浏览器不支持全屏功能'
                    });
                }
            });
        } else {
            document.exitFullscreen();
        }
    }

    // 从服务器加载数据
    async loadDataFromServer() {
        try {
            const queryData = {
                segNo: 'JC000000',
                serviceId: 'S_LI_RL_0182',
            };

            const response = await $.ajax({
                type: "post",
                contentType: "application/json",
                url: ytjServerUrl,
                data: JSON.stringify(queryData)
            });

            console.log('服务器响应:', response);

            this.locations = response.listL;
            this.materials = response.listK;
            // 保存占用量数据
            this.occupancyData = {
                A: response.listA || 0,
                B: response.listB || 0,
                C: response.listC || 0,
                D: response.listD || 0
            };
            // 保存汇总占用量数据
            this.totalOccupancy = parseFloat(response.listSum) || 0;

            this.processAreasFromLocations();
            this.renderBoard();
            this.startAreaPagination();

        } catch (error) {
            console.error('加载数据失败:', error);
            // 使用模拟数据作为备用
            // this.locations = this.loadMockLocations();
            // this.materials = this.loadMockMaterials();
            // this.processAreasFromLocations();
            // this.renderBoard();
            // this.startAreaPagination();
        }
    }

    // 从locations中处理并生成areas
    processAreasFromLocations() {
        // 从locations中提取所有唯一的crossArea
        const areaNames = [...new Set(this.locations.map(loc => loc.crossArea))];

        // 按指定顺序排序：A, B, C, D
        const sortOrder = ['A', 'B', 'C', 'D'];
        areaNames.sort((a, b) => {
            const indexA = sortOrder.indexOf(a);
            const indexB = sortOrder.indexOf(b);
            return indexA - indexB;
        });

        // 生成areas数组，包含每个区域的库位
        this.areas = areaNames.map(areaName => {
            const areaLocations = this.locations.filter(loc => loc.crossArea === areaName);
            return {
                name: areaName,
                label: `${areaName}区`,
                locations: areaLocations
            };
        });

        // 初始化各区域的分页信息
        this.areas.forEach(area => {
            this.areaCurrentPages[area.name] = 1;
            this.areaTotalPages[area.name] = Math.ceil(area.locations.length / this.locationsPerArea);
        });

        console.log('处理完成的区域:', this.areas);
        console.log('各区域分页信息:', {
            currentPages: this.areaCurrentPages,
            totalPages: this.areaTotalPages
        });
    }

    // 渲染看板
    renderBoard() {
        const container = $('#areas-container');
        container.empty();

        // 按顺序渲染所有区域
        const sortOrder = ['A', 'B', 'C', 'D'];
        sortOrder.forEach(areaName => {
            const area = this.areas.find(a => a.name === areaName);
            if (area) {
                const areaElement = this.createAreaElement(area);
                container.append(areaElement);
            }
        });

        // 更新汇总信息
        this.updateSummaryInfo();
    }

    // 更新汇总信息
    updateSummaryInfo() {
        // 使用后台返回的listSum作为总占用量
        const totalOccupancy = this.totalOccupancy || 0;

        // 计算剩余量，确保精确度
        const remainingOccupancy = parseFloat((100 - totalOccupancy).toFixed(2));

        // 格式化显示，如果是整数则不显示小数点
        const totalDisplay = totalOccupancy % 1 === 0 ? totalOccupancy.toString() : totalOccupancy.toFixed(2);
        const remainingDisplay = remainingOccupancy % 1 === 0 ? remainingOccupancy.toString() : remainingOccupancy.toFixed(2);

        // 创建或更新汇总信息元素
        let summaryDiv = $('.summary-info');
        if (summaryDiv.length === 0) {
            summaryDiv = $('<div>').addClass('summary-info');
            $('.top').append(summaryDiv);
        }

        summaryDiv.empty();
        const currentLine = $('<div>').addClass('summary-line').text(`当前占用：${totalDisplay}%`);
        const remainingLine = $('<div>').addClass('summary-line').text(`剩余占用：${remainingDisplay}%`);
        summaryDiv.append(currentLine).append(remainingLine);
    }

    // 创建区域元素
    createAreaElement(area) {
        const areaDiv = $('<div>').addClass('area-section').addClass(`area-${area.name.toLowerCase()}`);

        // 创建标题容器
        const headerDiv = $('<div>').addClass('area-header');

        // 区域标签（居中显示）
        const labelDiv = $('<div>').addClass('area-label').text(area.label);

        // 显示占用量信息（右上角，上下两行）
        const currentOccupancy = this.occupancyData ? parseFloat(this.occupancyData[area.name]) || 0 : 0;
        const remainingOccupancy = parseFloat((100 - currentOccupancy).toFixed(2));
        const occupancyDiv = $('<div>').addClass('area-occupancy-info');
        // 保留小数位显示，如果是整数则不显示小数点
        const currentDisplay = currentOccupancy % 1 === 0 ? currentOccupancy.toString() : currentOccupancy.toFixed(2);
        const remainingDisplay = remainingOccupancy % 1 === 0 ? remainingOccupancy.toString() : remainingOccupancy.toFixed(2);
        const currentLine = $('<div>').addClass('occupancy-line').text(`占用 ${currentDisplay}%`);
        const remainingLine = $('<div>').addClass('occupancy-line').text(`剩余 ${remainingDisplay}%`);
        occupancyDiv.append(currentLine).append(remainingLine);

        headerDiv.append(labelDiv).append(occupancyDiv);
        areaDiv.append(headerDiv);

        // 库位网格
        const gridDiv = $('<div>').addClass('location-grid').attr('id', `area-${area.name.toLowerCase()}-grid`);

        // 获取当前页应该显示的库位
        const currentPage = this.areaCurrentPages[area.name];
        const startIndex = (currentPage - 1) * this.locationsPerArea;
        const endIndex = startIndex + this.locationsPerArea;
        const currentPageLocations = area.locations.slice(startIndex, endIndex);

        currentPageLocations.forEach(location => {
            const locationElement = this.createLocationElement(location);
            gridDiv.append(locationElement);
        });

        areaDiv.append(gridDiv);
        return areaDiv;
    }

    // 创建库位元素
    createLocationElement(location) {
        const locationDiv = $('<div>').addClass('location-item');

        // 库位名称
        const nameDiv = $('<div>').addClass('location-name').text(location.locationName);
        locationDiv.append(nameDiv);

        // 创建两层
        const layersDiv = $('<div>').addClass('location-layers');

        for (let layer = 1; layer <= 2; layer++) {
            const layerDiv = $('<div>')
                .addClass(`layer layer-${layer}`)
                .attr('data-location', location.locationName)
                .attr('data-layer', layer);

            // 检查该库位该层是否有物料
            const material = this.findMaterialByLocation(location.locationName, layer);

            if (material) {
                // layerDiv.addClass('occupied').text(this.formatPackId(material.packId));
                layerDiv.addClass('occupied').text(`第${layer}层`);
                // 添加状态指示器
                const indicator = $('<div>').addClass('status-indicator');
                layerDiv.append(indicator);
            } else {
                layerDiv.addClass('empty').text(`第${layer}层`);
            }

            layersDiv.append(layerDiv);
        }

        locationDiv.append(layersDiv);

        // 添加点击事件
        locationDiv.on('click', () => this.showLocationDetails(location));

        return locationDiv;
    }

    // 格式化包号显示
    formatPackId(packId) {
        if (!packId) return '';
        return packId.length > 8 ? packId.substring(0, 8) + '...' : packId;
    }

    // 根据库位和层数查找物料
    findMaterialByLocation(locationName, layer) {
        return this.materials.find(material =>
            material.areaCode === locationName &&
            parseInt(material.posDirCode) === layer
        );
    }

    // 显示库位详情 - 使用SweetAlert
    showLocationDetails(location) {
        const materials = this.materials.filter(m => m.areaCode === location.locationName);

        let contentHtml = `
            <div style="text-align: left;">
                <h4 style="margin-bottom: 15px; color: #0070C0;">📍 库位信息</h4>
                <p><strong>库位编号:</strong> ${location.locationName}</p>
                <p><strong>所属区域:</strong> ${location.crossArea}区</p>
                <hr style="margin: 15px 0;">
        `;

        if (materials.length > 0) {
            contentHtml += '<h4 style="margin-bottom: 10px; color: #0070C0;">📦 存储捆包</h4>';

            // 分别获取第1层和第2层的捆包
            const layerOneMaterials = materials.filter(m => m.posDirCode === '1');
            const layerTwoMaterials = materials.filter(m => m.posDirCode === '2');

            // 处理第1层
            if (layerOneMaterials.length > 0) {
                contentHtml += this.generateLayerPackageDisplay(layerOneMaterials, '第1层');
            }

            // 处理第2层
            if (layerTwoMaterials.length > 0) {
                contentHtml += this.generateLayerPackageDisplay(layerTwoMaterials, '第2层');
            }

            // 如果没有任何捆包
            if (layerOneMaterials.length === 0 && layerTwoMaterials.length === 0) {
                contentHtml += '<p style="color: #999; font-style: italic;">💭 当前无存储捆包</p>';
            }
        } else {
            contentHtml += '<p style="color: #999; font-style: italic;">💭 当前无存储捆包</p>';
        }

        contentHtml += '</div>';

        // 使用SweetAlert显示详情
        Swal.fire({
            title: `库位 ${location.locationName}`,
            html: contentHtml,
            icon: 'info',
            confirmButtonText: '关闭',
            confirmButtonColor: '#0070C0',
            width: '900px', // 固定宽度
            background: '#fff',
            customClass: {
                confirmButton: 'swal-custom-btn',
                content: 'swal-custom-content'
            },
            didOpen: () => {
                // 添加自定义样式
                const style = document.createElement('style');
                style.textContent = `
                    .swal-custom-content {
                        max-height: 80vh !important;
                        overflow-y: auto !important;
                    }
                    .swal2-html-container {
                        max-height: none !important;
                        overflow: visible !important;
                    }
                `;
                document.head.appendChild(style);
            }
        });
    }

    // 生成层级捆包显示
    generateLayerPackageDisplay(materials, layerName) {
        let html = `
            <div style="background: #f0f8ff; padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid #0070C0;">
                <h5 style="margin: 0 0 10px 0; color: #0070C0; font-weight: bold;">${layerName}</h5>
                <div style="
                    display: grid; 
                    grid-template-columns: repeat(5, 1fr); 
                    gap: 8px; 
                    margin-top: 10px;
                    padding-right: 10px;
                    border: 1px solid #e0e0e0;
                    border-radius: 6px;
                    background: white;
                    padding: 10px;
                ">
        `;

        materials.forEach(material => {
            html += `
                <div style="
                    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
                    color: white;
                    padding: 8px 6px;
                    border-radius: 6px;
                    text-align: center;
                    font-size: 11px;
                    font-weight: bold;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    border: 1px solid rgba(255,255,255,0.2);
                    transition: transform 0.2s ease;
                    cursor: pointer;
                    word-break: break-all;
                    line-height: 1.1;
                    min-height: 45px;
                    height: auto;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    overflow-wrap: break-word;
                    word-wrap: break-word;
                    hyphens: auto;
                " 
                onmouseover="this.style.transform='scale(1.05)'" 
                onmouseout="this.style.transform='scale(1)'"
                title="${material.packId}">
                    ${this.formatPackIdForDisplay(material.packId)}
                </div>
            `;
        });

        html += `
                </div>
                <p style="margin: 10px 0 0 0; color: #666; font-size: 13px;">
                    <strong>共 ${materials.length} 个捆包</strong>
                </p>
            </div>
        `;

        return html;
    }

    // 格式化捆包号显示（用于弹窗详情）
    formatPackIdForDisplay(packId) {
        if (!packId) return '';
        // 完整显示捆包ID，不再截断
        return packId;
    }

    // 启动各区域分页
    startAreaPagination() {
        // 清除之前的定时器
        this.stopAreaPagination();

        // 为每个有多页的区域启动翻页定时器，设置不同的延迟
        let delayOffset = 0;
        this.areas.forEach((area, index) => {
            if (this.areaTotalPages[area.name] > 1) {
                // 每个区域间隔3秒启动，让翻页时间错开
                const delay = delayOffset * 3000;

                this.areaDelayTimeouts[area.name] = setTimeout(() => {
                    this.areaPageIntervals[area.name] = setInterval(() => {
                        this.nextAreaPage(area.name);
                    }, this.pageInterval);

                    console.log(`${area.name}区启动自动翻页，延迟${delay / 1000}秒，总页数: ${this.areaTotalPages[area.name]}`);
                }, delay);

                delayOffset++;
            }
        });
    }

    // 停止各区域分页
    stopAreaPagination() {
        // 清理翻页定时器
        Object.values(this.areaPageIntervals).forEach(interval => {
            if (interval) {
                clearInterval(interval);
            }
        });
        this.areaPageIntervals = {};

        // 清理延迟启动定时器
        Object.values(this.areaDelayTimeouts).forEach(timeout => {
            if (timeout) {
                clearTimeout(timeout);
            }
        });
        this.areaDelayTimeouts = {};

        // 清理最终刷新定时器
        if (this.finalRefreshTimeout) {
            clearTimeout(this.finalRefreshTimeout);
            this.finalRefreshTimeout = null;
        }
    }

    // 区域翻页
    nextAreaPage(areaName) {
        const currentPage = this.areaCurrentPages[areaName];
        const totalPages = this.areaTotalPages[areaName];

        // 计算下一页
        this.areaCurrentPages[areaName] = currentPage >= totalPages ? 1 : currentPage + 1;

        console.log(`${areaName}区翻页: ${currentPage} -> ${this.areaCurrentPages[areaName]}`);

        // 重新渲染该区域
        this.renderArea(areaName);

        // 检查是否所有区域都已翻到最后一页
        this.checkAllAreasAtLastPage();
    }

    // 渲染指定区域
    renderArea(areaName) {
        const area = this.areas.find(a => a.name === areaName);
        if (!area) return;

        const gridContainer = $(`#area-${areaName.toLowerCase()}-grid`);
        if (!gridContainer.length) return;

        gridContainer.empty();

        // 获取当前页应该显示的库位
        const currentPage = this.areaCurrentPages[areaName];
        const startIndex = (currentPage - 1) * this.locationsPerArea;
        const endIndex = startIndex + this.locationsPerArea;
        const currentPageLocations = area.locations.slice(startIndex, endIndex);

        currentPageLocations.forEach(location => {
            const locationElement = this.createLocationElement(location);
            gridContainer.append(locationElement);
        });

        // 更新区域标签中的页面信息 - 已隐藏分页显示
        // 清理可能存在的旧分页信息
        const labelDiv = $(`.area-${areaName.toLowerCase()} .area-label`);
        labelDiv.find('.area-page-info').remove();

        // if (this.areaTotalPages[areaName] > 1) {
        //     const pageInfoText = ` (${this.areaCurrentPages[areaName]}/${this.areaTotalPages[areaName]})`;
        //     // 移除旧的页面信息，添加新的
        //     labelDiv.find('.area-page-info').remove();
        //     const pageInfo = $('<span>').addClass('area-page-info').text(pageInfoText);
        //     labelDiv.append(pageInfo);
        // }
    }

    // 更新日期时间
    updateDateTime() {
        const now = new Date();
        const dateTimeStr = now.getFullYear() + '年' +
            (now.getMonth() + 1).toString().padStart(2, '0') + '月' +
            now.getDate().toString().padStart(2, '0') + '日    ' +
            now.getHours().toString().padStart(2, '0') + ':' +
            now.getMinutes().toString().padStart(2, '0');

        $('#datetime').text(dateTimeStr);
    }

    // 手动刷新
    refresh() {
        this.loadDataFromServer();
    }

    // 销毁实例
    destroy() {
        this.stopAreaPagination();
        $('.location-item').off('click');

        // 清理最终刷新定时器
        if (this.finalRefreshTimeout) {
            clearTimeout(this.finalRefreshTimeout);
            this.finalRefreshTimeout = null;
        }

        // 清理窗口大小变化监听器
        window.removeEventListener('resize', this.resizeHandler);
    }

    // 检查是否所有区域都已翻到最后一页
    checkAllAreasAtLastPage() {
        const multiPageAreas = this.areas.filter(area => this.areaTotalPages[area.name] > 1);

        if (multiPageAreas.length === 0) {
            // 如果没有多页区域，直接返回false，不需要特殊处理
            return false;
        }

        const allAtLastPage = multiPageAreas.every(area =>
            this.areaCurrentPages[area.name] === this.areaTotalPages[area.name]
        );

        if (allAtLastPage) {
            console.log('所有区域都已翻到最后一页，准备等待10秒后刷新数据');
            this.scheduleDataRefresh();
            return true;
        }

        return false;
    }

    // 安排数据刷新
    scheduleDataRefresh() {
        // 清除之前的刷新定时器
        if (this.finalRefreshTimeout) {
            clearTimeout(this.finalRefreshTimeout);
        }

        // 停止所有翻页定时器
        this.stopAreaPagination();

        // 10秒后刷新数据
        this.finalRefreshTimeout = setTimeout(() => {
            console.log('执行数据刷新...');
            this.loadDataFromServer();
        }, this.finalWaitTime);

        console.log(`将在${this.finalWaitTime / 1000}秒后刷新数据`);
    }
}

// 全局实例
let materialBoard = null;

// 页面加载完成后初始化
$(document).ready(function () {
    try {
        materialBoard = new MaterialLocationBoard();
        console.log('原料库位看板初始化成功');
    } catch (error) {
        console.error('原料库位看板初始化失败:', error);
    }
});

// 页面卸载时清理资源
$(window).on('beforeunload', function () {
    if (materialBoard) {
        materialBoard.destroy();
    }
});

// 暴露全局方法供外部调用
window.MaterialLocationBoard = {
    refresh: () => materialBoard && materialBoard.refresh(),
    getInstance: () => materialBoard
};

