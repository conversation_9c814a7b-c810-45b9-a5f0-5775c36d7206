package com.baosight.imom.li.rl.service;

import com.baosight.elim.common.utils.GlobalUtils;
import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.imc.common.utils.ImcGlobalUtils;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.li.ds.domain.LIDS0102;
import com.baosight.imom.li.ds.domain.LIDS0601;
import com.baosight.imom.li.ds.domain.LIDS1101;
import com.baosight.imom.li.rl.dao.*;
import com.baosight.imom.xt.ss.domain.XTSS11;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.ed.util.SequenceGenerator;
import com.baosight.xservices.em.util.SmsSendManager;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Image;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.pdf.PdfImportedPage;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfWriter;
import com.spire.doc.interfaces.IField;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.xmlbeans.impl.piccolo.util.DuplicateKeyException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.management.QueryEval;
import java.awt.print.PageFormat;
import java.io.*;
import java.math.BigDecimal;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.baosight.imom.li.rl.service.ServiceLIRLInterface.removeLastHyphen;


/***
 * PDA 专用接口服务
 */
public class ServiceLIRLInterfacePda extends ServiceBase {
    private static final Logger logger = LoggerFactory.getLogger(ServiceLIRLInterfacePda.class);

    public static final String USER_ID_TOKEN = "imom:bc:user_id_token_";

    public static final int STATUS_FAILURE = -2;


    public static final int A4_WIDTH = 595;
    public static final int A4_HEIGHT = 842;
    public static final int LANDSCAPE = PageFormat.LANDSCAPE;
    public static final int PORTRAIT = PageFormat.PORTRAIT;
    public static final int PRINT_MARGIN = 36;
    /**
     * restTemplate
     */
    @Autowired
    private RestTemplate restTemplate;

    // vehicleNo+carTraceNo
    private static final ConcurrentHashMap<String, Object> lockMap = new ConcurrentHashMap<>();


    /**
     * 状态返回
     */
    public static final Integer STATUS_UPDATE_TOKEN = 2;

    /**
     * redis服务
     */
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    /**
     * PDA登录
     * serviceId：S_LI_RL_0006
     * @param inInfo
     * @return
     */
    public EiInfo pdaLogin(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        if (null == inInfo.get("userName") || null == inInfo.get("password")
                || StringUtils.isBlank(inInfo.get("userName").toString()) ||
                StringUtils.isBlank(inInfo.get("password").toString())) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            logger.error("LIRLInterfacePda.pdaLogin用户名或密码为空！");
            outInfo.setMsg("用户名或密码为空！");
            return outInfo;
        }

        String userName = inInfo.get("userName").toString();
        String password = inInfo.get("password").toString();
        Map userMap = ImcGlobalUtils.getUserToken(userName, password);
        if (MapUtils.isEmpty(userMap)) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            logger.error("LIRLInterfacePda.pdaLogin用户名或密码错误，请重试！");
            outInfo.setMsg("用户名或密码错误，请重试！");
            return outInfo;
        }

        //获取用户姓名
        String userNamer = MapUtils.getString(userMap, "userName");
        List<Map> returnSegList = Lists.newArrayList();
        //调用code-value服务类的查询codeValue服务
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("userId",userName);
        eiInfo.set("flag","PDA");
        eiInfo.set(EiConstant.serviceName, "XTSS01");
        eiInfo.set(EiConstant.methodName, "queryUnit");
        EiInfo outEi = XLocalManager.callNoTx(eiInfo);
        if (outEi.getStatus()==-1){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(outEi.getMsg());
            return outInfo;
        }

        //获取token
        String imomToken = TokenUtils.getImomToken();
        returnSegList = outEi.getBlock("result2").getRows();
        //判断密码是否正确
        setUserAccessToken(userName,password,imomToken);
        outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        // outInfo.set("accessToken", accessTokenReturn);
        outInfo.set("userId", userName);
        outInfo.set("userName", userNamer);
        outInfo.set("segInfoList", returnSegList);
        outInfo.set("accessToken", TokenUtils.getImomToken());
        // if (returnSegList.size() == 1) {
        //     try {
        //         XS_OPERATION_USER xsOperationUser = new XS_OPERATION_USER();
        //         xsOperationUser.setUuid(UUIDUtils.getUUID());
        //         xsOperationUser.setSegNo(returnSegList.get(0).get("segNo").toString());
        //         xsOperationUser.setSegCname(returnSegList.get(0).get("segName").toString());
        //         xsOperationUser.setOperationType(LIRLConstant.OPERATE_STATUS_LOGIN);
        //         xsOperationUser.setSourceSystem(LIRLConstant.SOURCE_PDA);
        //         xsOperationUser.setEmployeeJobNumber(userName);
        //         xsOperationUser.setEmployeeName(userNamer);
        //         RecordUtils.setCreator(xsOperationUser.toMap());
        //         dao.insert(XS_OPERATION_USER.INSERT, xsOperationUser);
        //     } catch (Exception e) {
        //         logger.error("LIRLInterfacePda.pdaLogin写入XS_OPERATION_USER登录信息失败，错误信息:"+e.getMessage());
        //     }
        // }
        return outInfo;
    }

    /**
     * PDA登录
     * serviceId：S_LI_RL_0666
     * @param inInfo
     * @return
     */
    public EiInfo queryMenu(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        String segNo = (String) inInfo.get("segNo"); //账套
        String userId = (String) inInfo.get("userId");
        if (StringUtils.isBlank(segNo)){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("账套为空！");
            return outInfo;
        }

        if (StringUtils.isBlank(userId)){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("用户为空！");
            return outInfo;
        }

        // 根据userId查询用户组
        HashMap queryGroupMap = new HashMap<>();
        queryGroupMap.put("segNo", segNo);
        queryGroupMap.put("userId", userId);
        List<HashMap> query = this.dao.query("XTSS20.queryUserGroup", queryGroupMap);
        if (CollectionUtils.isEmpty(query)){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("登录账号无用户组角色！");
            return outInfo;
        }

        List<String> menuList = new ArrayList<>();
        // 根据用户组角色查询权限表
        for (HashMap data : query) {
            HashMap queryMenuMap = new HashMap();
            queryMenuMap.put("segNo", data.get("segNo"));
            queryMenuMap.put("userGroupId", data.get("parentId"));
            List<HashMap> userMenuList = this.dao.query("XTSS20.queryPDA", queryMenuMap);
            if (!CollectionUtils.isEmpty(userMenuList)){
                for (HashMap userMenu : userMenuList) {
                    menuList.add(userMenu.get("userPermission").toString());
                }
            }
        }
        
        // 使用Stream方式拆分逗号分隔的字符串并去重
        menuList = menuList.stream()
            .flatMap(item -> Arrays.stream(item.split("、")))
            .map(String::trim)
            .distinct()
            .collect(Collectors.toList());
        outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        outInfo.set("menuList", menuList);
        return outInfo;
    }


    /**
     * PDA切换账套
     * serviceId：S_LI_RL_0052
     * @param inInfo
     * @return
     */
    public EiInfo pdaSelectAllSegNo(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        if (null == inInfo.get("userName")
                || StringUtils.isBlank(inInfo.get("userName").toString())
                ) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            logger.error("LIRLInterfacePda.pdaLogin用户名或密码为空！");
            outInfo.setMsg("用户名不能空！");
            return outInfo;
        }
        String userName = (String) inInfo.get("userName");

        List<Map> returnSegList = Lists.newArrayList();
        //调用code-value服务类的查询codeValue服务
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("userId",userName);
        eiInfo.set("flag","PDA");
        eiInfo.set(EiConstant.serviceName, "XTSS01");
        eiInfo.set(EiConstant.methodName, "queryUnit");
        EiInfo outEi = XLocalManager.callNoTx(eiInfo);
        if (outEi.getStatus()==-1){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(outEi.getMsg());
            return outInfo;
        }

        //获取token
        String imomToken = TokenUtils.getImomToken();
        returnSegList = outEi.getBlock("result2").getRows();

        outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        // outInfo.set("accessToken", accessTokenReturn);
        outInfo.set("userId", userName);
        outInfo.set("userName", userName);
        outInfo.set("segInfoList", returnSegList);
        outInfo.set("accessToken", imomToken);
        return outInfo;
    }

    /***
     * PDA 强制叫号查询车牌信息
     *	S_LI_RL_0050
     */
    public EiInfo forcedCallByQueryVehicleInfo(EiInfo eiInfo){
        EiInfo outInfo = new EiInfo();
        String segNo = (String) eiInfo.get("segNo"); //账套
        if (StringUtils.isBlank(segNo)){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("账套为空！");
            return outInfo;
        }
        // //效验token
        // outInfo = validToken(eiInfo);
        // if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
        //     outInfo.setStatus(EiConstant.STATUS_DEFAULT);
        //     outInfo.setMsg("token无效！");
        //     return outInfo;
        // }

        HashMap<Object, Object> hashMap = new HashMap<>();
        hashMap.put("segNo",segNo);
        List<HashMap> query = this.dao.query(LIRL0401.QUERY_QUEUE_DATA, hashMap);
        outInfo.set("list",query);
        outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        return outInfo;
    }


    /***
     * 查询开始装卸货状态的车辆以及装卸点
     *
     */
    public EiInfo queryLadingHandPointInfo(EiInfo eiInfo){
        EiInfo outInfo = new EiInfo();
        String segNo = (String) eiInfo.get("segNo"); //账套
        if (StringUtils.isBlank(segNo)){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("账套为空！");
            return outInfo;
        }
        //效验token
        outInfo = validToken(eiInfo);
        if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("token无效！");
            return outInfo;
        }
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("segNo",segNo);
        hashMap.put("status","30");//正在作业
        List<HashMap> queryLIRL0301 = this.dao.query(LIRL0301.QUERY_LADING_INFO);
        eiInfo.set("list",queryLIRL0301);
        eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
        return eiInfo;
    }


    /***
     * 根据装卸点查询开始装卸货状态的车辆
     * @ServiceId :S_LI_RL_0055
     */
    public EiInfo queryLadingVehicle(EiInfo eiInfo){
        EiInfo outInfo = new EiInfo();
        String segNo = (String) eiInfo.get("segNo"); //账套
        String handPointId = (String)eiInfo.get("handPointId"); //仓库代码
        String factoryArea = (String)eiInfo.get("factoryArea"); //仓库代码
        if (StringUtils.isEmpty(segNo)){
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg("账套为空！");
            return eiInfo;
        }
        //效验token
        outInfo = validToken(eiInfo);
        if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("token无效！");
            return outInfo;
        }
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("segNo",segNo);
        hashMap.put("handPointId",handPointId);
        hashMap.put("factoryArea",factoryArea);
        hashMap.put("status","30");//正在作业
        List<HashMap> queryLIRL0301 = this.dao.query(LIRL0301.QUERY_LADING_VEHICLE,hashMap);
        eiInfo.set("list",queryLIRL0301);
        eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
        return eiInfo;
    }

    /***
     * PDA 强制叫号
     *	S_LI_RL_0007
     */
    public EiInfo forcedCall(EiInfo eiInfo){

        EiInfo outInfo = new EiInfo();
        try {
            //获取账套、车牌号、车辆跟踪号
            String segNo = (String) eiInfo.get("segNo"); //账套
            String vehicleNo = (String) eiInfo.get("vehicleNo"); //车牌号
            String carTraceNo = (String) eiInfo.get("carTraceNo"); //车辆跟踪号
            String handPointId = (String) eiInfo.get("handPointId"); //目标装卸点

            //检查是否已经叫号了
            HashMap<Object, Object> hashMap = new HashMap<>();
            hashMap.put("segNo",segNo);
            hashMap.put("vehicleNo",vehicleNo);
            hashMap.put("carTraceNo",carTraceNo);
            List<LIRL0401> LIRL0401List = this.dao.query(LIRL0401.QUERY_HAND_POINT_ID, hashMap);
            if (LIRL0401List.size()<0&& CollectionUtils.isEmpty(LIRL0401List)){
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("此车辆排队序列中不存在,请重新查询数据!");
                return eiInfo;
            }
            for (LIRL0401 lirl0401 : LIRL0401List) {
                String targetHandPointId = lirl0401.getTargetHandPointId();

                HashMap<Object, Object> hashMaplirl0402 = new HashMap<>();
                Map map = lirl0401.toMap();
                hashMaplirl0402.putAll(map);
                RecordUtils.setCreator(hashMap);
                hashMaplirl0402.put("uuid", UUIDUtils.getUUID());
                hashMaplirl0402.put("remark", DateUtil.curDateTimeStr14().concat(":强制叫号"));
                hashMaplirl0402.put("sysRemark", DateUtil.curDateTimeStr14().concat(":强制叫号"));
                hashMaplirl0402.put("queueDate", DateUtil.curDateTimeStr14());
                if (StringUtils.isNotEmpty(handPointId)){
                    if (!targetHandPointId.equals(handPointId)){
                        hashMaplirl0402.put("targetHandPointId", handPointId);
                        //不为空直接插入叫号表
                        this.dao.insert(LIRL0402.INSERT,hashMaplirl0402);
                        //更新装卸点进车跟踪数据
                        updateHandPointJobNumber(segNo, handPointId, 0, 1, 10);
                        //更新车辆跟踪装卸点
                        hashMap.put("targetHandPointId",handPointId);
                        hashMap.put("oldTargetHandPointId",targetHandPointId);
                        //更新0305表
                        this.dao.update(LIRL0305.UPDATE_TARGET_HAND_POINT_ID, hashMap);
                        this.dao.update(LIRL0301.UPDATE_TARGET_HAND_POINT, hashMap);
                        this.dao.delete(LIRL0401.DELETE,map);
                        this.dao.delete(LIRL0303.DELETE,map);

                    }else {
                        hashMaplirl0402.put("targetHandPointId",targetHandPointId);
                        this.dao.insert(LIRL0402.INSERT,hashMaplirl0402);
                        //更新装卸点进车跟踪数据
                        updateHandPointJobNumber(segNo, targetHandPointId, 0, 1, 10);
                        //更新车辆跟踪装卸点
                        hashMap.put("targetHandPointId",lirl0401.getTargetHandPointId());
                        this.dao.update(LIRL0301.UPDATE_TARGET_HAND_POINT, hashMap);
                        this.dao.delete(LIRL0401.DELETE,map);
                        this.dao.delete(LIRL0303.DELETE,map);
                    }
                } else {
                    hashMaplirl0402.put("targetHandPointId",targetHandPointId);
                    this.dao.insert(LIRL0402.INSERT,hashMaplirl0402);
                    //更新装卸点进车跟踪数据
                    updateHandPointJobNumber(segNo, targetHandPointId, 0, 1, 10);
                    //更新车辆跟踪装卸点
                    hashMap.put("targetHandPointId",lirl0401.getTargetHandPointId());
                    this.dao.update(LIRL0301.UPDATE_TARGET_HAND_POINT, hashMap);
                    this.dao.delete(LIRL0401.DELETE,map);
                    this.dao.delete(LIRL0303.DELETE,map);
                }
                //发送短信
                senMessage(hashMaplirl0402);
            }
            outInfo.setMsg("强制叫号成功!");
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("强制叫号成功!");
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }
        return outInfo;
    }

    /***
     * PDA 查询所有装卸点信息
     *
     * @Service S_LI_RL_0051
     */

    public EiInfo findHandPointIdByPda(EiInfo eiInfo){

        String segNo = (String)eiInfo.get("segNo"); //目标装卸点
        String factoryArea = (String)eiInfo.get("factoryArea"); //仓库代码
        String factoryBuilding = (String)eiInfo.get("factoryBuilding"); //仓库代码
        String warehouseCode = (String)eiInfo.get("warehouseCode"); //仓库代码
        String vehicleNo = (String)eiInfo.get("vehicleNo"); //车牌号
        String carTraceNo = (String)eiInfo.get("carTraceNo"); //车辆跟踪号
        if (StringUtils.isEmpty(segNo)){
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg("账套为空！");
            return eiInfo;
        }

        //根据目标装卸点查询车辆跟踪表
        HashMap<Object, Object> hashMap = new HashMap<>();
        hashMap.put("segNo",segNo);
        hashMap.put("factoryArea",factoryArea);
        if ("JC000000".equals(segNo)){
            hashMap.put("vehicleNo",vehicleNo);
            hashMap.put("carTraceNo",carTraceNo);
        }
        hashMap.put("factoryBuilding",factoryBuilding);
        hashMap.put("warehouseCode",warehouseCode);
        hashMap.put("status","30");
        List<LIRL0301> listLIRL0301 = this.dao.query(LIRL0304.QUERY_HAND_POINT_INFO, hashMap);

        //更新车辆跟踪表厂区代码
        this.dao.update(LIRL0301.BACK_ALLOCATE_VEHICLE_NO,hashMap);
        eiInfo.set("list",listLIRL0301);
        eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
        return eiInfo;

    }

    /***
     * PDA 查询车牌信息
     *
     * findVehicleByPda
     */

    public EiInfo findVehicleByPda(EiInfo eiInfo){

        String segNo = (String)eiInfo.get("segNo"); //目标装卸点
        String handPointId = (String)eiInfo.get("handPointId"); //仓库代码
        if (StringUtils.isEmpty(segNo)||StringUtils.isEmpty(handPointId)){
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg("账套、装卸点为空！");
            return eiInfo;
        }

        //效验token
        EiInfo outInfo = validToken(eiInfo);
        if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("token无效！");
            return outInfo;
        }
        //根据目标装卸点查询车辆跟踪表
        HashMap<Object, Object> hashMap = new HashMap<>();
        hashMap.put("segNo",segNo);
        hashMap.put("handPointId",handPointId);
        // hashMap.put("handTypeList",handTypeList);
        hashMap.put("targetHandPointIdNotNull","1");
        // hashMap.put("statusList",statusList);
        List<String> listLIRL0301 = this.dao.query(LIRL0301.QUERY_VEHICLE, hashMap);
        eiInfo.set("list",listLIRL0301);
        eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
        return eiInfo;

    }


    /***
     * PDA 查询车牌信息
     *
     * 	S_LI_RL_0093
     */

    public EiInfo findVehicleByPdaCQ(EiInfo eiInfo){

        String segNo = (String)eiInfo.get("segNo"); //目标装卸点
        String handPointId = (String)eiInfo.get("handPointId"); //仓库代码
        if (StringUtils.isEmpty(segNo)||StringUtils.isEmpty(handPointId)){
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg("账套、装卸点为空！");
            return eiInfo;
        }

        //效验token
        EiInfo outInfo = validToken(eiInfo);
        if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("token无效！");
            return outInfo;
        }
        //根据目标装卸点查询车辆跟踪表
        HashMap<Object, Object> hashMap = new HashMap<>();
        hashMap.put("segNo",segNo);
//        hashMap.put("handPointId503",handPointId);
        hashMap.put("handPointId401",handPointId);
        // hashMap.put("handTypeList",handTypeList);
//         hashMap.put("targetHandPointIdNotNull","1");
         hashMap.put("flag","1");
        // hashMap.put("statusList",statusList);
        List<String> listLIRL0301 = this.dao.query(LIRL0301.QUERY_VEHICLE_C_Q, hashMap);
        eiInfo.set("list",listLIRL0301);
        eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
        return eiInfo;

    }

    /***
     * PDA 出入库回写出入库明细
     * 	S_LI_RL_0001
     */
    public EiInfo outInStockPlan(EiInfo eiInfo){

        try {

            List<HashMap> result = (List<HashMap>)eiInfo.get("result");
            if (CollectionUtils.isEmpty(result)){
                eiInfo.setStatus(-1);
                eiInfo.setMsg("出入库明细数据不存在!");
                return eiInfo;
            }
            for (HashMap hashMap : result) {
                //获取车辆跟踪号,车牌号,账套
                String carTraceNo = MapUtils.getString(hashMap, "carTraceNo");
                String vehicleNo = MapUtils.getString(hashMap, "vehicleNo");
                String segNo = MapUtils.getString(hashMap, "segNo");
                String putinId = MapUtils.getString(hashMap, "putinId");
                String matInnerId = MapUtils.getString(hashMap, "matInnerId");
                String putoutId = MapUtils.getString(hashMap, "putoutId");//出库单号
                String voucherNum = MapUtils.getString(hashMap, "voucherNum");//提单号
                String driverName = MapUtils.getString(hashMap, "driverName");//司机姓名
                String driverTel = MapUtils.getString(hashMap, "driverTel");//司机手机号
                String driverIdentity = MapUtils.getString(hashMap, "driverIdentity");//司机身份证号
                String deliverType = MapUtils.getString(hashMap, "deliverType");//交货方式
                String deliverName = MapUtils.getString(hashMap, "deliverName");//交货方式名称
                String packId = MapUtils.getString(hashMap, "packId");//捆包号
                String factoryOrderNum = MapUtils.getString(hashMap, "factoryOrderNum");//钢厂资源号
                String prodTypeId = MapUtils.getString(hashMap, "prodTypeId");//品种附属码
                String prodTypeName = MapUtils.getString(hashMap, "prodTypeName");//品种附属码名称
                String shopsign = MapUtils.getString(hashMap, "shopsign");//牌号
                String specDesc = MapUtils.getString(hashMap, "specDesc");//规格
                String weight = MapUtils.getString(hashMap, "weight");//重量
                String quantity = MapUtils.getString(hashMap, "quantity");//数量
                String warehouseCode = MapUtils.getString(hashMap, "warehouseCode");//仓库代码
                String warehouseName = MapUtils.getString(hashMap, "warehouseName");//仓库名称
                String customerId = MapUtils.getString(hashMap, "customerId");//客户代码
                String customerName = MapUtils.getString(hashMap, "customerName");//客户名称
                String locationId = MapUtils.getString(hashMap, "locationId");//库位代码
                String locationName = MapUtils.getString(hashMap, "locationName");//库位名称
                String recCreator = MapUtils.getString(hashMap, "recCreator");//创建人
                String recCreatorName = MapUtils.getString(hashMap, "recCreatorName");//创建人姓名
                String putoutDate = MapUtils.getString(hashMap, "putoutDate");//出库日期
                String finalDestination = MapUtils.getString(hashMap, "finalDestination");//终到站名称
                String deliveryType = MapUtils.getString(hashMap, "deliveryType");//交货方式
                String deliveryTypeName = MapUtils.getString(hashMap, "deliveryTypeName");//交货方式
                String lon = MapUtils.getString(hashMap, "lon");//经度
                String lat = MapUtils.getString(hashMap, "lat");//纬度
                String perNo = MapUtils.getString(hashMap, "perNo");//装货行车工
                String perName = MapUtils.getString(hashMap, "perName");//装货行车工姓名
                String destSpotAddr = MapUtils.getString(hashMap, "destSpotAddr");//终到站地址
                String handPointId = MapUtils.getString(hashMap, "currentHandPointId");//装卸点
                String loadId = MapUtils.getString(hashMap, "loadId");//开始装卸货流水号
                boolean isXcar=false;
//                //查询开关，判断如果开关为开,生成IMOM库存表数据
//                String ifInboundReconciliation = new SwitchUtils().getProcessSwitchValue(segNo, "IF_INBOUND_RECONCILIATION", dao);
//                if ("1".equals(ifInboundReconciliation)){
//                    return eiInfo;
//                }

                //判断是否是无计划入库的材料
                HashMap<String, String> lirl0308HashMap = new HashMap<>();
                lirl0308HashMap.put("carTraceNo",carTraceNo);
                lirl0308HashMap.put("vehicleNo",vehicleNo);
                lirl0308HashMap.put("segNo",segNo);
                lirl0308HashMap.put("packId",packId);
                if ("JC000000".equals(segNo)){
                    lirl0308HashMap.put("notFlag","10");
                }
                List<LIRL0308> LIRL0308list = this.dao.query(LIRL0308.QUERY, lirl0308HashMap);
                if (StringUtils.isBlank(carTraceNo)){
                    lirl0308HashMap.put("carTraceNo","xxx");
                }
                List<LIRL0301> listLIRL0301 = this.dao.query(LIRL0301.QUERY, lirl0308HashMap);
                if (CollectionUtils.isEmpty(listLIRL0301))
                {
                    eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
                    logger.info("车辆进厂信息不存在，请检查!"+result);
                    //叉车
                    if ("JC000000".equals(segNo)){
                        isXcar=true;
                    }else {
                        eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                        eiInfo.setMsg("车辆进厂信息不存在，请检查!");
                        return eiInfo;
                    }
                }

                if (CollectionUtils.isEmpty(LIRL0308list)&& !isXcar){
                    //新增出入库计划明细
                    LIRL0308 lirl0308 = new LIRL0308();
                    lirl0308.setSegNo(segNo);
                    lirl0308.setUnitCode(segNo);
                    lirl0308.setCarTraceNo(carTraceNo);
                    lirl0308.setPutinId(putinId);
                    lirl0308.setPutoutId(putoutId);
                    lirl0308.setVoucherNum(voucherNum);
                    lirl0308.setDriverName(listLIRL0301.get(0).getDriverName());
                    lirl0308.setDriverTel(listLIRL0301.get(0).getTelNum());
                    lirl0308.setDriverIdentity(listLIRL0301.get(0).getIdCard());
                    lirl0308.setVehicleNo(listLIRL0301.get(0).getVehicleNo());
                    lirl0308.setMatInnerId(matInnerId);
                    lirl0308.setDeliverType(deliveryType);
                    lirl0308.setDeliverName(deliveryTypeName);
                    lirl0308.setPackId(packId);
                    lirl0308.setFactoryOrderNum(factoryOrderNum);
                    lirl0308.setProdTypeId(prodTypeId);
                    lirl0308.setProdTypeName(prodTypeName);
                    lirl0308.setShopsign(shopsign);
                    lirl0308.setSpecDesc(specDesc);
                    lirl0308.setWeight(new BigDecimal(weight));
                    lirl0308.setQuantity(new BigDecimal(quantity));
                    lirl0308.setWarehouseCode(warehouseCode);
                    lirl0308.setWarehouseName(warehouseName);
                    lirl0308.setCustomerId(customerId);
                    lirl0308.setCustomerName(customerName);
                    lirl0308.setLocationId(locationId);
                    lirl0308.setLocationName(locationName);
                    lirl0308.setRecCreator(recCreator);
                    lirl0308.setRecCreatorName(recCreatorName);
                    lirl0308.setRecCreateTime(DateUtil.curDateTimeStr14());
                    lirl0308.setRecRevisor(recCreator);
                    lirl0308.setRecRevisorName(recCreatorName);
                    lirl0308.setRecReviseTime(DateUtil.curDateTimeStr14());
                    lirl0308.setPutoutDate(putoutDate);
                    lirl0308.setFinalDestination(finalDestination);
                    lirl0308.setFinalStationLongitude(lon);
                    lirl0308.setFinalStationLatitude(lat);
                    lirl0308.setPerNo(perNo);
                    lirl0308.setPerName(perName);
                    lirl0308.setDestSpotAddr(destSpotAddr);
                    lirl0308.setLoadId(loadId);
                    lirl0308.setHandPointId(handPointId);
                    if (StringUtils.isNotBlank(putinId)){
                        lirl0308.setPutInOutFlag("10");
                    }else if (StringUtils.isBlank(putinId)&&StringUtils.isBlank(putoutId)){
                        lirl0308.setPutInOutFlag("10");
                    }else if (StringUtils.isNotBlank(putoutId)){
                        lirl0308.setPutInOutFlag("20");
                    }

                    Map map = lirl0308.toMap();
                    map.put("uuid",UUIDUtils.getUUID());
                    this.dao.insert(LIRL0308.INSERT,map);

                    eiInfo.setMsg("出入库明细新增成功！");
                }else if (!isXcar){

                    LIRL0308 lirl0308 = new LIRL0308();
                    //更新无入库计划的putinId、matInnerId
                    lirl0308.setFactoryOrderNum(factoryOrderNum);
                    lirl0308.setPackId(packId);
                    lirl0308.setCarTraceNo(carTraceNo);
                    lirl0308.setUuid(LIRL0308list.get(0).getUuid());
                    lirl0308.setProdTypeId(prodTypeId);
                    lirl0308.setProdTypeName(prodTypeName);
                    lirl0308.setShopsign(shopsign);
                    lirl0308.setSpecDesc(specDesc);
                    lirl0308.setWeight(new BigDecimal(weight));
                    lirl0308.setQuantity(new BigDecimal(quantity));
                    lirl0308.setPutinId(putinId);
                    lirl0308.setWarehouseCode(warehouseCode);
                    lirl0308.setWarehouseName(warehouseName);
                    lirl0308.setCustomerId(customerId);
                    lirl0308.setCustomerName(customerName);
                    lirl0308.setMatInnerId(matInnerId);
                    lirl0308.setRecCreator(recCreator);
                    lirl0308.setRecCreatorName(recCreatorName);
                    lirl0308.setRecCreateTime(DateUtil.curDateTimeStr14());
                    lirl0308.setRecRevisor(recCreator);
                    lirl0308.setRecRevisorName(recCreatorName);
                    lirl0308.setRecReviseTime(DateUtil.curDateTimeStr14());
                    Map map = lirl0308.toMap();
                    this.dao.update(LIRL0308.UPDATE_INFO, map);
                    eiInfo.setMsg("无计划入库明细更新成功！");
                }
                //叉车
                if (isXcar){
                    //新增出入库计划明细
                    LIRL0308 lirl0308 = new LIRL0308();
                    lirl0308.setSegNo(segNo);
                    lirl0308.setUnitCode(segNo);
                    lirl0308.setCarTraceNo(carTraceNo);
                    lirl0308.setPutinId(putinId);
                    lirl0308.setPutoutId(putoutId);
                    lirl0308.setVoucherNum(voucherNum);
                    lirl0308.setDriverName(" ");
                    lirl0308.setDriverTel(" ");
                    lirl0308.setDriverIdentity(" ");
                    lirl0308.setVehicleNo(vehicleNo);
                    lirl0308.setMatInnerId(matInnerId);
                    lirl0308.setDeliverType(deliveryType);
                    lirl0308.setDeliverName(deliveryTypeName);
                    lirl0308.setPackId(packId);
                    lirl0308.setFactoryOrderNum(factoryOrderNum);
                    lirl0308.setProdTypeId(prodTypeId);
                    lirl0308.setProdTypeName(prodTypeName);
                    lirl0308.setShopsign(shopsign);
                    lirl0308.setSpecDesc(specDesc);
                    lirl0308.setWeight(new BigDecimal(weight));
                    lirl0308.setQuantity(new BigDecimal(quantity));
                    lirl0308.setWarehouseCode(warehouseCode);
                    lirl0308.setWarehouseName(warehouseName);
                    lirl0308.setCustomerId(customerId);
                    lirl0308.setCustomerName(customerName);
                    lirl0308.setLocationId(locationId);
                    lirl0308.setLocationName(locationName);
                    lirl0308.setRecCreator(recCreator);
                    lirl0308.setRecCreatorName(recCreatorName);
                    lirl0308.setRecCreateTime(DateUtil.curDateTimeStr14());
                    lirl0308.setRecRevisor(recCreator);
                    lirl0308.setRecRevisorName(recCreatorName);
                    lirl0308.setRecReviseTime(DateUtil.curDateTimeStr14());
                    lirl0308.setPutoutDate(putoutDate);
                    lirl0308.setFinalDestination(finalDestination);
                    lirl0308.setFinalStationLongitude(lon);
                    lirl0308.setFinalStationLatitude(lat);
                    lirl0308.setPerNo(perNo);
                    lirl0308.setPerName(perName);
                    lirl0308.setDestSpotAddr(destSpotAddr);
                    lirl0308.setLoadId(loadId);
                    lirl0308.setHandPointId(handPointId);
                    if (StringUtils.isNotBlank(putinId)){
                        lirl0308.setPutInOutFlag("10");
                    }else if (StringUtils.isBlank(putinId)&&StringUtils.isBlank(putoutId)){
                        lirl0308.setPutInOutFlag("10");
                    }else if (StringUtils.isNotBlank(putoutId)){
                        lirl0308.setPutInOutFlag("20");
                    }

                    Map map = lirl0308.toMap();
                    map.put("uuid",UUIDUtils.getUUID());
                    this.dao.insert(LIRL0308.INSERT,map);

                    eiInfo.setMsg("叉车出入库明细新增成功！");
                }
            }


            eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg(e.getMessage());
            return eiInfo;
        }
        return eiInfo;
    }

    /**
     * 获取排队表中最大的序号
     * @param segNo
     * @return
     */
    public  Integer getQueueNumber (String segNo) {
        Integer queueNumber;
        HashMap<Object, Object> hashMapLirl04011 = new HashMap<>();
        hashMapLirl04011.put("segNo", segNo);
        List<String> query = this.dao.query(LIRL0401.QUERY_QUEUE_NUMBER, hashMapLirl04011);
        if (CollectionUtils.isEmpty(query)){
            queueNumber=0;
        }else {
            queueNumber = Integer.parseInt(query.get(0));
        }
        return queueNumber;
    }


    /***
     * PDA 车辆开始装卸货
     * prog.wl_vehicle_plan_mgr.vehicle_load_good
     * S_LI_RL_0010
     *
     */
    public EiInfo vehicleLoadGood(EiInfo eiInfo) {

        try {
            String stringDate = DateUtil.curDateTimeStr14();//修改时间

            //获取车辆跟踪号,车牌号,账套
            String segNo = (String) eiInfo.get("segNo");//账套
            if (null == eiInfo.get("segNo") || StringUtils.isBlank(eiInfo.get("segNo").toString())) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("业务单元代码为空，请重试！");
                return eiInfo;
            }
            String carTraceNo = (String) eiInfo.get("carTraceNo"); //车辆跟踪单号
            if (null == eiInfo.get("carTraceNo") || StringUtils.isBlank(eiInfo.get("carTraceNo").toString())) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("车辆跟踪单号为空，请重试！");
                return eiInfo;
            }
            String vehicleNo = eiInfo.getString("vehicleNo");//车牌号
            if (null == eiInfo.get("vehicleNo") || StringUtils.isBlank(eiInfo.get("vehicleNo").toString())) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("车牌号为空，请重试！");
                return eiInfo;
            }
            String recCreator = eiInfo.getString("recCreator");//当前登录人
            if (null == eiInfo.get("recCreator") || StringUtils.isBlank(eiInfo.get("recCreator").toString())) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("当前登录人为空，请重试！");
                return eiInfo;
            }
            String recCreatorName = eiInfo.getString("recCreatorName");//当前登录人姓名
            if (null == eiInfo.get("recCreatorName") || StringUtils.isBlank(eiInfo.get("recCreatorName").toString())) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("当前登录人姓名为空，请重试！");
                return eiInfo;
            }
            String currentHandPointId = eiInfo.getString("currentHandPointId");//页面传入当前装卸点
            if (null == eiInfo.get("currentHandPointId") || StringUtils.isBlank(eiInfo.get("currentHandPointId").toString())) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("当前装卸点为空，请重试！");
                return eiInfo;
            }
            //重庆增加传参
            String factoryArea = eiInfo.getString("factoryArea");//页面传入当前厂区
            if ("JC000000".equals(segNo)){
                if (null == eiInfo.get("factoryArea") || StringUtils.isBlank(eiInfo.get("factoryArea").toString())) {
                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                    eiInfo.setMsg("当前厂区为空，请重试！");
                    return eiInfo;
                }
            }


            String perName = eiInfo.getString("perName");//行车工姓名

            String status  = (String) eiInfo.get("status");//判断是否正在作业
            //如果status为30就代表为正在装卸货的车辆只需要跟新时间

            String loadId="";

            //效验token
            EiInfo outInfo = validToken(eiInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                outInfo.setMsg("token无效！");
                return outInfo;
            }


            //查询出勾选的数据是否当前装卸点事否有数据
            Map queryLIRL0301 = new HashMap();
            queryLIRL0301.put("segNo", segNo);
            queryLIRL0301.put("carTraceNo", carTraceNo);
            List<LIRL0301> lirl0301s = dao.query(LIRL0301.QUERY, queryLIRL0301);
            if (lirl0301s.size() < 1){
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("车辆跟踪数据不存在!");
                return eiInfo;
            }
            LIRL0301 lirl0301 = lirl0301s.get(0);
            if (StringUtils.isBlank(lirl0301.getCurrentHandPointId())) {
                //如果没0则是第一次进行开始装卸货
                //修改跟踪表的数据，修改当前装卸点
                lirl0301.setCurrentHandPointId(currentHandPointId);
                //重庆宝钢账套，校验厂区是否为空，为空更新一波
                if ("JC000000".equals(segNo)){
                    if (StringUtils.isBlank(lirl0301.getFactoryArea())) {
                        lirl0301.setFactoryArea(factoryArea);
                    }
                }
                dao.update(LIRL0301.UPDATE, lirl0301);
                //插入开始装卸货表
                String strSeqTypeId = "TLIRL_SEQ0406";//开始装卸货流水号
                Date date = new Date(System.currentTimeMillis());
                String[] args = {segNo.substring(0, 2), date.toString(), ""};
                loadId = SequenceGenerator.getNextSequence(strSeqTypeId, "", args);
                Map insertLIRL0406 = new HashMap();
                RecordUtils.setCreator(insertLIRL0406);
                insertLIRL0406.put("segNo", lirl0301.getSegNo());
                insertLIRL0406.put("unitCode", lirl0301.getSegNo());
                insertLIRL0406.put("loadId", loadId);
                insertLIRL0406.put("status", "10");
                insertLIRL0406.put("loadDate", stringDate);
                insertLIRL0406.put("carTraceNo", lirl0301.getCarTraceNo());
                insertLIRL0406.put("dateSource", "20");
                insertLIRL0406.put("vehicleNo", vehicleNo);
                insertLIRL0406.put("targetHandPointId", lirl0301.getTargetHandPointId());
                insertLIRL0406.put("currentHandPointId", lirl0301.getCurrentHandPointId());
                insertLIRL0406.put("factoryArea", lirl0301.getFactoryArea());
                insertLIRL0406.put("documentType", 10);
                insertLIRL0406.put("recCreator", recCreator);
                insertLIRL0406.put("recCreatorName", recCreatorName);
                insertLIRL0406.put("recCreateTime", stringDate);
                insertLIRL0406.put("recRevisor", recCreator);
                insertLIRL0406.put("recRevisorName", recCreatorName);
                insertLIRL0406.put("recReviseTime", stringDate);
                insertLIRL0406.put("uuid", StrUtil.getUUID());
                insertLIRL0406.put("cancelLoadDate", " ");
                insertLIRL0406.put("voucherNum", " ");
                insertLIRL0406.put("sysRemark", " ");
                insertLIRL0406.put("remark", " ");
                insertLIRL0406.put("perName", perName);
                LIRL0406 lirl0406 = new LIRL0406();
                lirl0406.fromMap(insertLIRL0406);
                dao.insert(LIRL0406.INSERT, lirl0406);
                //修改跟踪表的状态和清空目标装卸点和开始装卸货的时间
                lirl0301.setStatus("30");
                lirl0301.setTargetHandPointId("");
                //判断开始时间是否有值
                String beginEntruckingTime = lirl0301.getBeginEntruckingTime();
                if (StringUtils.isBlank(beginEntruckingTime)){
                    lirl0301.setBeginEntruckingTime(stringDate);
                }
                lirl0301.setRecRevisor(recCreator);
                lirl0301.setRecRevisorName(recCreatorName);
                lirl0301.setRecReviseTime(stringDate);
                dao.update(LIRL0301.UPDATE, lirl0301);

            } else {
                //查询出勾选的数据是否当前装卸点事否有数据
                queryLIRL0301 = new HashMap();
                queryLIRL0301.put("segNo", segNo);
                queryLIRL0301.put("carTraceNo", carTraceNo);
                if (!"30".equals(status)){
                    status="20";
                }
                queryLIRL0301.put("status", status);
                lirl0301s = dao.query(LIRL0301.QUERY, queryLIRL0301);
                if (lirl0301s.size() == 0) {
                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                    eiInfo.setMsg("车辆跟踪数据不存在!");
                    return eiInfo;
                }
                lirl0301 = lirl0301s.get(0);
                String currentHandPointId2 = lirl0301.getCurrentHandPointId();//当前装卸点代码
                if (currentHandPointId.equals(currentHandPointId2)) {
                    //当前的装卸点可以进行第二次的开始装卸货,修改开始装卸货的时间，开始装卸货的状态不发生改变。
                    lirl0301.setStatus("30");

                    //判断开始时间是否有值 不修改作业开始时间
                    String beginEntruckingTime = lirl0301.getBeginEntruckingTime();
                    if (StringUtils.isBlank(beginEntruckingTime)){
                        lirl0301.setBeginEntruckingTime(stringDate);
                    }
                    lirl0301.setBeginEntruckingTime(stringDate);
                    lirl0301.setRecRevisor(recCreator);
                    lirl0301.setRecRevisorName(recCreatorName);
                    lirl0301.setRecReviseTime(stringDate);
                    //重庆宝钢账套，校验厂区是否为空，为空更新一波
                    if ("JC000000".equals(segNo)){
                        if (StringUtils.isBlank(lirl0301.getFactoryArea())) {
                            lirl0301.setFactoryArea(factoryArea);
                        }
                    }
                    dao.update(LIRL0301.UPDATE, lirl0301);

                    // if ("30".equals(status)){
                    //     Map updateLIRL0406 = new HashMap();
                    //     updateLIRL0406.put("segNo",segNo);
                    //     updateLIRL0406.put("targetHandPointId", lirl0301.getTargetHandPointId());
                    //     updateLIRL0406.put("currentHandPointId", lirl0301.getCurrentHandPointId());
                    //     updateLIRL0406.put("documentType", 20);//重复装卸货
                    //     updateLIRL0406.put("recCreator", recCreator);
                    //     updateLIRL0406.put("recCreatorName", recCreatorName);
                    //     updateLIRL0406.put("recCreateTime", stringDate);
                    //     updateLIRL0406.put("recRevisor", recCreator);
                    //     updateLIRL0406.put("recRevisorName", recCreatorName);
                    //     updateLIRL0406.put("recReviseTime", stringDate);
                    //     updateLIRL0406.put("sysRemark", " ");
                    //     updateLIRL0406.put("remark", " ");
                    //     updateLIRL0406.put("cancelLoadDate", " ");
                    //     updateLIRL0406.put("voucherNum", " ");
                    //     updateLIRL0406.put("carTraceNo", carTraceNo);
                    //     updateLIRL0406.put("vehicleNo", vehicleNo);
                    //     updateLIRL0406.put("stringDate", DateUtil.curDateTimeStr14());
                    //     dao.update(LIRL0406.UPDATE_BY_HAND_POINT_ID, updateLIRL0406);
                    // }else {
                        //然后重新插入一条数据到车辆开始装卸货表，标记20为重复开始装卸货。
                        Map insertLIRL0406 = new HashMap();
                        insertLIRL0406.put("segNo", segNo);
                        insertLIRL0406.put("unitCode", segNo);
                        String strSeqTypeId = "TLIRL_SEQ0406";//开始装卸货流水号
                        Date date = new Date(System.currentTimeMillis());
                        String[] args = {segNo.substring(0, 2), date.toString(), ""};
                        loadId = SequenceGenerator.getNextSequence(strSeqTypeId, "", args);
                        RecordUtils.setCreator(insertLIRL0406);

                        insertLIRL0406.put("loadId", loadId);
                        insertLIRL0406.put("status", 10);
                        insertLIRL0406.put("loadDate", stringDate);
                        insertLIRL0406.put("carTraceNo", carTraceNo);
                        insertLIRL0406.put("vehicleNo", lirl0301.getVehicleNo());
                        insertLIRL0406.put("targetHandPointId", lirl0301.getTargetHandPointId());
                        insertLIRL0406.put("currentHandPointId", lirl0301.getCurrentHandPointId());
                        insertLIRL0406.put("factoryArea", lirl0301.getFactoryArea());
                        insertLIRL0406.put("documentType", 20);
                        insertLIRL0406.put("recCreator", recCreator);
                        insertLIRL0406.put("recCreatorName", recCreatorName);
                        insertLIRL0406.put("recCreateTime", stringDate);
                        insertLIRL0406.put("recRevisor", recCreator);
                        insertLIRL0406.put("recRevisorName", recCreatorName);
                        insertLIRL0406.put("recReviseTime", stringDate);
                        insertLIRL0406.put("uuid", StrUtil.getUUID());
                        insertLIRL0406.put("dateSource", "20");
                        insertLIRL0406.put("cancelLoadDate", " ");
                        insertLIRL0406.put("voucherNum", " ");
                        insertLIRL0406.put("sysRemark", " ");
                        insertLIRL0406.put("remark", " ");
                        insertLIRL0406.put("perName", perName);
                        dao.insert(LIRL0406.INSERT, insertLIRL0406);
                    // }
                } else {
                    //需要把车辆跟踪号原本的装卸点的数据给结束装卸货，然后再开始新的装卸货
                    //查询出当前车辆跟踪号生成的开始装卸货的数据
                    Map queryLIRL0406 = new HashMap();
                    queryLIRL0406.put("segNo", segNo);
                    queryLIRL0406.put("carTraceNo", carTraceNo);
                    queryLIRL0406.put("status", 10);
                    queryLIRL0406.put("currentHandPointId", currentHandPointId2);
                    queryLIRL0406.put("delFlag", 0);
                    List<LIRL0406> lirl0406s = dao.query(LIRL0406.QUERY, queryLIRL0406);
                    if (lirl0406s.size() == 0) {
                        eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                        eiInfo.setMsg("车辆开始装卸货表数据不存在!");
                        return eiInfo;
                    }
                    //然后修改这条数据的状态
                    LIRL0406 lirl0406 = lirl0406s.get(0);
                    lirl0406.setStatus("00");
                    dao.update(LIRL0406.UPDATE, lirl0406);
                    //同时修改跟踪表中的数据  修改状态为进厂，目标装卸点有   当前装卸点为空
                    lirl0301.setStatus("10");
                    lirl0301.setCurrentHandPointId("");
                    lirl0301.setTargetHandPointId(lirl0406.getTargetHandPointId());
                    lirl0301.setRecRevisor(recCreator);
                    lirl0301.setRecRevisorName(recCreatorName);
                    lirl0301.setRecReviseTime(stringDate);
                    //然后开始新的装卸货
                    //修改跟踪表的数据，修改当前装卸点
                    lirl0301.setCurrentHandPointId(currentHandPointId);
                    //重庆宝钢账套，校验厂区是否为空，为空更新一波
                    if ("JC000000".equals(segNo)){
                        if (StringUtils.isBlank(lirl0301.getFactoryArea())) {
                            lirl0301.setFactoryArea(factoryArea);
                        }
                    }
                    dao.update(LIRL0301.UPDATE, lirl0301);
                    //把跟踪表中的数据添加到开始装卸货的表中
                    Map insertLIRL0406 = new HashMap();
                    insertLIRL0406.put("segNo", segNo);
                    insertLIRL0406.put("unitCode", segNo);
                    String strSeqTypeId = "TLIRL_SEQ0406";//开始装卸货流水号
                    Date date = new Date(System.currentTimeMillis());
                    String[] args = {segNo.substring(0, 2), date.toString(), ""};
                    loadId = SequenceGenerator.getNextSequence(strSeqTypeId, "", args);
                    RecordUtils.setCreator(insertLIRL0406);

                    insertLIRL0406.put("loadId", loadId);
                    insertLIRL0406.put("status", 10);
                    insertLIRL0406.put("loadDate", stringDate);
                    insertLIRL0406.put("carTraceNo", carTraceNo);
                    insertLIRL0406.put("vehicleNo", lirl0301.getVehicleNo());
                    insertLIRL0406.put("targetHandPointId", lirl0301.getTargetHandPointId());
                    insertLIRL0406.put("currentHandPointId", lirl0301.getCurrentHandPointId());
                    insertLIRL0406.put("factoryArea", lirl0301.getFactoryArea());
                    insertLIRL0406.put("documentType", 10);
                    insertLIRL0406.put("recCreator", recCreator);
                    insertLIRL0406.put("recCreatorName", recCreatorName);
                    insertLIRL0406.put("recCreateTime", stringDate);
                    insertLIRL0406.put("recRevisor", recCreator);
                    insertLIRL0406.put("recRevisorName", recCreatorName);
                    insertLIRL0406.put("recReviseTime", stringDate);
                    insertLIRL0406.put("uuid", StrUtil.getUUID());
                    insertLIRL0406.put("dateSource", "20");
                    insertLIRL0406.put("cancelLoadDate", " ");
                    insertLIRL0406.put("voucherNum", " ");
                    insertLIRL0406.put("sysRemark", " ");
                    insertLIRL0406.put("remark", " ");
                    insertLIRL0406.put("perName",perName);
                    dao.insert(LIRL0406.INSERT, insertLIRL0406);
                    //修改跟踪表的状态和清空目标装卸点
                    lirl0301.setStatus("30");
                    lirl0301.setTargetHandPointId("");
                    //判断开始时间是否有值 不修改作业开始时间
                    String beginEntruckingTime = lirl0301.getBeginEntruckingTime();
                    if (StringUtils.isBlank(beginEntruckingTime)){
                        lirl0301.setBeginEntruckingTime(stringDate);
                    }
                    dao.update(LIRL0301.UPDATE, lirl0301);

                }
                // //判断目标装卸点和当前装卸点是否一致
                // isTheSiteConsistent(stringDate, segNo, recCreator, recCreatorName, lirl0301);

            }

            // //把上一装卸点当前作业-1,预计作业数作业数+1
            //
            // HashMap updateLIRL0306 = new HashMap();
            // updateLIRL0306.put("segNo", segNo);
            // updateLIRL0306.put("handPointId", currentHandPointId);//当前作业数+1
            // updateLIRL0306.put("currentJobNumber",new BigDecimal("1"));
            // updateLIRL0306.put("preJobNumber",new BigDecimal("0"));
            // updateLIRL0306.put("recRevisor", recCreator);
            // updateLIRL0306.put("recRevisorName", recCreatorName);
            // updateLIRL0306.put("recReviseTime", stringDate);
            // dao.update(LIRL0306.UPDATE_JOB_NUMBER_BY_HAND_POINT3,updateLIRL0306);
            //
            // //把上一装卸点当前作业-1,预计作业数作业数+1
            //
            // updateLIRL0306 = new HashMap();
            // updateLIRL0306.put("segNo", segNo);
            // updateLIRL0306.put("handPointId", currentHandPointId);//当前作业数+1
            // updateLIRL0306.put("currentJobNumber",new BigDecimal("0"));
            // updateLIRL0306.put("preJobNumber",new BigDecimal("1"));
            // updateLIRL0306.put("recRevisor", recCreator);
            // updateLIRL0306.put("recRevisorName", recCreatorName);
            // updateLIRL0306.put("recReviseTime", stringDate);
            // dao.update(LIRL0306.UPDATE_JOB_NUMBER_BY_HAND_POINT2,updateLIRL0306);

            if (!"30".equals(status)){
                //更新装点状态
                updateHandPointJobNumber(segNo, currentHandPointId, 1, 0, 40);
                updateHandPointJobNumber(segNo, currentHandPointId, 0, 1, 50);
            }


            //开始装卸货清空下一目标
            HashMap<String, String> stringStringHashMap = new HashMap<>();
            stringStringHashMap.put("segNo", segNo);
            stringStringHashMap.put("carTraceNo", carTraceNo);
            stringStringHashMap.put("vehicleNo", vehicleNo);
            stringStringHashMap.put("nextTatget", " ");
            this.dao.update(LIRL0407.UPDATE_NEXT_TARGET, stringStringHashMap);
            // //查询车辆结束装卸货货中是否存在该条数据
            // Map queryLIRL0407 = new HashMap();
            // queryLIRL0407.put("segNo", segNo);
            // queryLIRL0407.put("carTraceNo", carTraceNo);
            // queryLIRL0407.put("vehicleNo", vehicleNo);
            // queryLIRL0407.put("delFlag", 0);
            // int count = super.count(LIRL0407.COUNT, queryLIRL0407);
            // if (count > 0) {
            //     eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            //     eiInfo.setMsg("此车已被操作要离厂,不能开始装卸货!");
            //     return eiInfo;
            // }
            eiInfo.set("loadId",loadId);
            eiInfo.set("currentHandPointId",currentHandPointId);
            eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg(e.getMessage());
            return eiInfo;
        }
        return eiInfo;
    }


    private void isTheSiteConsistent(String stringDate, String segNo, String recCreator, String recCreatorName, LIRL0301 lirl0301) {
        //判断目标装卸点和当前装卸点是否一致
        //如果一致 当前装卸点预计作业数-1,当前作业数+1；
        if (lirl0301.getTargetHandPointId().equals(lirl0301.getCurrentHandPointId())){
            Map updateLIRL0306 = new HashMap();
            updateLIRL0306.put("segNo", segNo);
            updateLIRL0306.put("handPointId", lirl0301.getTargetHandPointId());
            updateLIRL0306.put("currentJobNumber",new BigDecimal("1"));
            updateLIRL0306.put("preJobNumber",new BigDecimal("-1"));
            updateLIRL0306.put("recRevisor", recCreator);
            updateLIRL0306.put("recRevisorName", recCreatorName);
            updateLIRL0306.put("recReviseTime", stringDate);
            dao.update(LIRL0306.UPDATE_JOB_NUMBER_BY_HAND_POINT2,updateLIRL0306);
        }else {
            //不一致 当前装卸点预计作业数-1,选择装卸点作业数作业数+1；
            Map updateLIRL0306 = new HashMap();
            updateLIRL0306.put("segNo", segNo);
            updateLIRL0306.put("handPointId", lirl0301.getCurrentHandPointId());
            updateLIRL0306.put("currentJobNumber",new BigDecimal("1"));
            updateLIRL0306.put("recRevisor", recCreator);
            updateLIRL0306.put("recRevisorName", recCreatorName);
            updateLIRL0306.put("recReviseTime", stringDate);
            dao.update(LIRL0306.UPDATE_JOB_NUMBER_BY_HAND_POINT2,updateLIRL0306);

            updateLIRL0306 = new HashMap();
            updateLIRL0306.put("segNo", segNo);
            updateLIRL0306.put("handPointId", lirl0301.getTargetHandPointId());
            updateLIRL0306.put("preJobNumber",new BigDecimal("-1"));
            updateLIRL0306.put("recRevisor", recCreator);
            updateLIRL0306.put("recRevisorName", recCreatorName);
            updateLIRL0306.put("recReviseTime", stringDate);
            dao.update(LIRL0306.UPDATE_JOB_NUMBER_BY_HAND_POINT2,updateLIRL0306);
        }
    }

    /***
     * PDA 车辆结束装卸货
     * prog.wl_vehicle_plan_mgr.vehicle_finish_load_good
     * S_LI_RL_0011
     */
    public EiInfo vehicleFinishLoadGood(EiInfo eiInfo) {

        try {
            String stringDate = DateUtil.curDateTimeStr14();//修改时间
            String in_seg_no = (String) eiInfo.get("segNo");//账套
            if (null == eiInfo.get("segNo") || StringUtils.isBlank(eiInfo.get("segNo").toString())) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("业务单元代码为空，请重试！");
                return eiInfo;
            }
            String in_car_trace_no = (String) eiInfo.get("carTraceNo"); //车辆跟踪单号
            if (null == eiInfo.get("carTraceNo") || StringUtils.isBlank(eiInfo.get("carTraceNo").toString())) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("车辆跟踪单号为空，请重试！");
                return eiInfo;
            }
            String in_modi_person = eiInfo.getString("recCreator");//当前登录人
            if (null == eiInfo.get("recCreator") || StringUtils.isBlank(eiInfo.get("recCreator").toString())) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("当前登录人为空，请重试！");
                return eiInfo;
            }
            String in_modi_person_name = eiInfo.getString("recCreatorName");//当前登录人姓名
            if (null == eiInfo.get("recCreatorName") || StringUtils.isBlank(eiInfo.get("recCreatorName").toString())) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("当前登录人姓名为空，请重试！");
                return eiInfo;
            }
            String in_target_hand_point_id = eiInfo.getString("targetHandPointId");//页面传入目标装卸点
            if (null == eiInfo.get("targetHandPointId") || StringUtils.isBlank(eiInfo.get("targetHandPointId").toString())) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("目标装卸点为空，请重试！");
                return eiInfo;
            }
            String in_next_target = eiInfo.getString("nextTarget");//下一装卸点
            if (null == eiInfo.get("nextTarget") || StringUtils.isBlank(eiInfo.get("nextTarget").toString())) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("下一装卸点为空，请重试！");
                return eiInfo;
            }

            String loadIdNew = eiInfo.getString("loadId");//


            //效验token
            /*EiInfo outInfo = validToken(eiInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                outInfo.setMsg("token无效！");
                return outInfo;
            }*/

            //首先查询出勾选数据的当前装卸点
            Map queryLIRL0301 = new HashMap();
            queryLIRL0301.put("segNo",in_seg_no);
            queryLIRL0301.put("carTraceNo",in_car_trace_no);
            List<LIRL0301> lirl0301s = dao.query(LIRL0301.QUERY, queryLIRL0301);
            if (lirl0301s.size() == 0){
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("车辆跟踪数据不存在!");
                return eiInfo;
            }
            LIRL0301 lirl0301 = lirl0301s.get(0);
            String currentHandPointId = lirl0301.getCurrentHandPointId();//当前装卸点代码
            String vehicleNo = lirl0301.getVehicleNo();//车牌号

            //卸货点当前作业数-1
            Map updateLIRL0306 = new HashMap();
            updateLIRL0306.put("segNo", in_seg_no);
            updateLIRL0306.put("handPointId", lirl0301.getCurrentHandPointId());
            updateLIRL0306.put("currentJobNumber",new BigDecimal("1"));
            updateLIRL0306.put("recRevisor", in_modi_person);
            updateLIRL0306.put("recRevisorName", in_modi_person_name);
            updateLIRL0306.put("recReviseTime", stringDate);
            // dao.update(LIRL0306.UPDATE_JOB_NUMBER_BY_HAND_POINT2,updateLIRL0306);

            //控制程序
            //如果前台页面传的下一个目标为10（下一个装卸点）
            //那么目标装卸点为必填，如果为空，那么就提升报错
            if ("10".equals(in_next_target)&&StringUtils.isEmpty(in_target_hand_point_id)){
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("车辆跟踪号"+in_car_trace_no+"的下个目标为下一装卸点，所以下一装卸点字段不能为空!");
                return eiInfo;
            }else if ("20".equals(in_next_target)){
                //离厂 目标装卸点为空
                in_target_hand_point_id = "";
                //下一装卸点当前作业数作业数-1
                // updateLIRL0306 = new HashMap();
                // updateLIRL0306.put("segNo", in_seg_no);
                // updateLIRL0306.put("handPointId", currentHandPointId);//当前作业数-1
                // updateLIRL0306.put("currentJobNumber",new BigDecimal("1"));
                // updateLIRL0306.put("preJobNumber",new BigDecimal("0"));
                // updateLIRL0306.put("recRevisor", in_modi_person);
                // updateLIRL0306.put("recRevisorName", in_modi_person_name);
                // updateLIRL0306.put("recReviseTime", stringDate);
                // dao.update(LIRL0306.UPDATE_JOB_NUMBER_BY_HAND_POINT4,updateLIRL0306);
                updateHandPointJobNumber(in_seg_no, currentHandPointId, 1, 0, 60);
            }else {
                //把上一装卸点当前作业-1
                // updateLIRL0306 = new HashMap();
                // updateLIRL0306.put("segNo", in_seg_no);
                // updateLIRL0306.put("handPointId", currentHandPointId);//当前作业数-1
                // updateLIRL0306.put("currentJobNumber",new BigDecimal("1"));
                // updateLIRL0306.put("preJobNumber",new BigDecimal("0"));
                // updateLIRL0306.put("recRevisor", in_modi_person);
                // updateLIRL0306.put("recRevisorName", in_modi_person_name);
                // updateLIRL0306.put("recReviseTime", stringDate);
                // dao.update(LIRL0306.UPDATE_JOB_NUMBER_BY_HAND_POINT4,updateLIRL0306);
                updateHandPointJobNumber(in_seg_no, currentHandPointId, 1, 0, 60);

                //下一装卸点预计作业数作业数+1
                // updateLIRL0306 = new HashMap();
                // updateLIRL0306.put("segNo", in_seg_no);
                // updateLIRL0306.put("handPointId", in_target_hand_point_id);
                // updateLIRL0306.put("currentJobNumber",new BigDecimal("0"));
                // updateLIRL0306.put("preJobNumber",new BigDecimal("1"));
                // updateLIRL0306.put("recRevisor", in_modi_person);
                // updateLIRL0306.put("recRevisorName", in_modi_person_name);
                // updateLIRL0306.put("recReviseTime", stringDate);
                // dao.update(LIRL0306.UPDATE_JOB_NUMBER_BY_HAND_POINT4,updateLIRL0306);
                updateHandPointJobNumber(in_seg_no, in_target_hand_point_id, 0, 1, 60);

            }
            //然后标记当前装卸点为1 暂时没用到

            //如果是其他家的 根据客户勾选的目标装卸点来
            //先修改跟踪表中的目标装卸点（下一装卸点）
            Map updateLIRL0301 = new HashMap();
            updateLIRL0301.put("segNo",in_seg_no);
            updateLIRL0301.put("targetHandPointId",in_target_hand_point_id);
            updateLIRL0301.put("carTraceNo",in_car_trace_no);
            updateLIRL0301.put("delFlag",0);
            dao.update(LIRL0301.UPDATE_LIRL0301TARGET_HAND_POINT_ID,updateLIRL0301);

            //查询最后一次开始装卸货的车辆
            Map queryLIRL0406 = new HashMap();
            queryLIRL0406.put("segNo",in_seg_no);
            queryLIRL0406.put("vehicleNo",vehicleNo);
            queryLIRL0406.put("carTraceNo",in_car_trace_no);
//            List<LIRL0406> lirl0406s = dao.query(LIRL0406.QUERY_ALL, queryLIRL0406);
            //然后标记当前装卸点为1 暂时没用到
//            String loadIdNew="";
            String targetHandPointId="";
            String currentHandPointIdNew="";
//            if (CollectionUtils.isNotEmpty(lirl0406s)){
//                loadIdNew = lirl0406s.get(0).getLoadId();
//                targetHandPointId = lirl0406s.get(0).getTargetHandPointId();
//                currentHandPointIdNew = lirl0406s.get(0).getCurrentHandPointId();
//            }
            // if ("20".equals(in_next_target)&&StringUtils.isBlank(in_target_hand_point_id)){
            //     in_target_hand_point_id="离厂";
            // }
            //插入车辆结束装卸货表
            Map insertLIRL0407 = new HashMap<>();
            insertLIRL0407.put("segNo",in_seg_no);
            insertLIRL0407.put("unitCode",in_seg_no);
            String strSeqTypeId = "TLIRL_SEQ0407";//开始装卸货流水号
            Date date = new Date(System.currentTimeMillis());
            String[] args = {in_seg_no.substring(0, 2), date.toString(), ""};
            String loadId = SequenceGenerator.getNextSequence(strSeqTypeId, "", args);
            insertLIRL0407.put("finishLoadId",loadId);
            insertLIRL0407.put("status",10);
            insertLIRL0407.put("finishLoadDate",stringDate);
            insertLIRL0407.put("carTraceNo",lirl0301.getCarTraceNo());
            insertLIRL0407.put("dateSource",20);
            insertLIRL0407.put("vehicleNo",lirl0301.getVehicleNo());
            insertLIRL0407.put("currentHandPointId",lirl0301.getCurrentHandPointId());
            insertLIRL0407.put("tatgetHandPointId",in_target_hand_point_id);
            insertLIRL0407.put("factoryArea",lirl0301.getFactoryArea());
            insertLIRL0407.put("nextTatget",in_next_target);
            insertLIRL0407.put("recCreator", in_modi_person);
            insertLIRL0407.put("recCreatorName", in_modi_person_name);
            insertLIRL0407.put("recCreateTime", stringDate);
            insertLIRL0407.put("recRevisor", in_modi_person);
            insertLIRL0407.put("recRevisorName", in_modi_person_name);
            insertLIRL0407.put("recReviseTime", stringDate);
            insertLIRL0407.put("uuid", StrUtil.getUUID());
            insertLIRL0407.put("delFlag", 0);
            LIRL0407 lirl0407 = new LIRL0407();
            lirl0407.fromMap(insertLIRL0407);
            lirl0407.setLoadId(loadIdNew);
            dao.insert(LIRL0407.INSERT,lirl0407);
            //根据结束装卸货中开是否有目标装卸点重是否有值
            //如果有值   跟踪表的状态为30开始装卸货状态
            //否者    为结束装卸货状态
            Map queryLIRL0407 = new HashMap();
            queryLIRL0407.put("segNo",in_seg_no);
            queryLIRL0407.put("carTraceNo",in_car_trace_no);
            queryLIRL0407.put("finishLoadId",loadId);
            queryLIRL0407.put("delFlag",0);
            int count = super.count(LIRL0407.COUNT, queryLIRL0407);
            if (count>0){
                updateLIRL0301 = new HashMap<>();
                updateLIRL0301.put("segNo",in_seg_no);
                updateLIRL0301.put("status",40);
                updateLIRL0301.put("currentHandPointId","");
                updateLIRL0301.put("tatgetHandPointId",in_target_hand_point_id);
                updateLIRL0301.put("completeUninstallTime",stringDate);
                updateLIRL0301.put("recRevisor", in_modi_person);
                updateLIRL0301.put("recRevisorName", in_modi_person_name);
                updateLIRL0301.put("recReviseTime", stringDate);

                updateLIRL0301.put("carTraceNo", in_car_trace_no);
                updateLIRL0301.put("segNO", in_seg_no);
                updateLIRL0301.put("delFlag", 0);
                dao.update(LIRL0301.UPDATE_LIRL0301JSZX,updateLIRL0301);
            }else {
                updateLIRL0301 = new HashMap<>();
                updateLIRL0301.put("segNo",in_seg_no);
                updateLIRL0301.put("status",40);
                updateLIRL0301.put("currentHandPointId","");
                updateLIRL0301.put("tatgetHandPointId","");
                updateLIRL0301.put("completeUninstallTime",stringDate);
                updateLIRL0301.put("recRevisor", in_modi_person);
                updateLIRL0301.put("recRevisorName", in_modi_person_name);
                updateLIRL0301.put("recReviseTime", stringDate);

                updateLIRL0301.put("carTraceNo", in_car_trace_no);
                updateLIRL0301.put("segNO", in_seg_no);
                updateLIRL0301.put("delFlag", 0);
                dao.update(LIRL0301.UPDATE_LIRL0301JSZX2,updateLIRL0301);
            }


            //判断厂内周转开关是否开启
            String inPlantCirculationSwitch = new SwitchUtils().getProcessSwitchValue(in_seg_no, "IN_PLANT_CIRCULATION_SWITCH", dao);
            if ("1".equals(inPlantCirculationSwitch)){
                if ("10".equals(in_next_target)){
                    // 现场装货有这种情况：从东门进入到西门提货，西门提完之后再从东门进入继续装货的，
                    // 实际会涉及进厂有两次，针对这种情况，可设如下方案：例如从5、6、7、8号门转到其他装卸点(非5、6、7、8)的情况，
                    // 再回到东门时，可自动插入到进厂车辆里，根据类型进行区分(例如第一次进厂类型是首次进厂，第二次进厂类似是厂内周转)，
                    // 这样门卫可知道此车是厂内周转过来的，就可以抬杆操作，但是对此种类型只要能点进厂按钮即可，
                    // 与原进厂流程不相关，如果有车牌识别，此种情况也支持自动抬杆（结束装卸货处理这段逻辑）

                    //查询车辆进厂表
                    Map queryLIRL0405 = new HashMap();
                    queryLIRL0405.put("segNo",in_seg_no);
                    queryLIRL0405.put("carTraceNo",in_car_trace_no);
                    queryLIRL0405.put("vehicleNo",lirl0301.getVehicleNo());
                    queryLIRL0405.put("status",10);
                    List<LIRL0405> lirl0405s = dao.query(LIRL0405.QUERY, queryLIRL0405);
                    if (lirl0405s.size() == 0){
                        eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                        eiInfo.setMsg("车辆进厂信息不存在!");
                        return eiInfo;
                    }
                    //查询目标装卸点名称
                    String inTargethandPointName = "";//目标装卸点名称
                    String currentthandPointName = "";//当前装卸点名称

                    Map queryLIRL0304 = new HashMap();
                    queryLIRL0304.put("segNo",in_seg_no);
                    queryLIRL0304.put("status","30");
                    queryLIRL0304.put("handPointId",in_target_hand_point_id);
                    List<HashMap> lirl0304s = dao.query(LIRL0304.QUERY_HAND_POINT_NAME, queryLIRL0304);
                    if (lirl0304s.size()>0){
                        HashMap hashMap = lirl0304s.get(0);
                        inTargethandPointName = MapUtils.getString(hashMap, "handPointName", "");
                    }else {
                        eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                        eiInfo.setMsg("目标装卸点信息不存在!");
                        return eiInfo;
                    }
                    //查询当前装卸点名称
                    queryLIRL0304 = new HashMap();
                    queryLIRL0304.put("segNo",in_seg_no);
                    queryLIRL0304.put("status","30");
                    queryLIRL0304.put("handPointId",currentHandPointId);
                    lirl0304s = dao.query(LIRL0304.QUERY_HAND_POINT_NAME, queryLIRL0304);
                    if (lirl0304s.size()>0){
                        HashMap hashMap = lirl0304s.get(0);
                        currentthandPointName = MapUtils.getString(hashMap, "handPointName", "");
                    }else {
                        eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                        eiInfo.setMsg("当前装卸点信息不存在!");
                        return eiInfo;
                    }
                    //判断当前装卸点
                    // 实际会涉及进厂有两次，针对这种情况，可设如下方案：例如从5、6、7、8号门转到其他装卸点(非5、6、7、8)的情况，
                    if ("5号门".equals(currentthandPointName)||"6号门".equals(currentthandPointName)||"7号门".equals(currentthandPointName)||"8号门".equals(currentthandPointName)){
                        if ("5号门".equals(inTargethandPointName)){
                        }else if ("6号门".equals(inTargethandPointName)){
                        }else if ("7号门".equals(inTargethandPointName)){
                        }else if ("8号门".equals(inTargethandPointName)){
                        }else {
                            LIRL0405 lirl0405 = lirl0405s.get(0);
                            Map insertLIRL0415 = new HashMap();
                            insertLIRL0415.put("segNo",in_seg_no);
                            insertLIRL0415.put("unitCode",in_seg_no);
                            strSeqTypeId = "TLIRL_SEQ0415";//车辆进厂流水号
                            String[] args2 = {in_seg_no.substring(0, 2),"", date.toString(), ""};
                            String intoFactoryId = SequenceGenerator.getNextSequence(strSeqTypeId, args2);
                            insertLIRL0415.put("intoFactoryId",intoFactoryId);
                            insertLIRL0415.put("vehicleNo",lirl0405.getVehicleNo());
                            insertLIRL0415.put("status","10");
                            insertLIRL0415.put("factoryType","20");
                            insertLIRL0415.put("intoFactoryDate","");
                            insertLIRL0415.put("carTraceNo",lirl0405.getCarTraceNo());
                            insertLIRL0415.put("targetHandPointId",in_target_hand_point_id);
                            insertLIRL0415.put("factoryArea",lirl0405.getFactoryArea());
                            insertLIRL0415.put("factoryAreaName",lirl0301.getFactoryAreaName());
                            insertLIRL0415.put("recCreator",in_modi_person);
                            insertLIRL0415.put("recCreatorName",in_modi_person_name);
                            insertLIRL0415.put("recCreateTime",stringDate);
                            insertLIRL0415.put("recRevisor", in_modi_person);
                            insertLIRL0415.put("recRevisorName", in_modi_person_name);
                            insertLIRL0415.put("recReviseTime", stringDate);
                            insertLIRL0415.put("uuid", StrUtil.getUUID());
                            insertLIRL0415.put("archiveFlag", "");
                            insertLIRL0415.put("delFlag", "0");
                            insertLIRL0415.put("remark", "");
                            insertLIRL0415.put("sysRemark", "");
                            insertLIRL0415.put("tenantId", "");
                            dao.insert(LIRL0415.INSERT,insertLIRL0415);
                        }
                    }
                }
            }


            eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg(e.getMessage());
            return eiInfo;
        }
        return eiInfo;
    }

    /***
     * 扫描捆包
     * 物流服务：S_UC_PR_230601
     *
     * serviceId:	S_LI_RL_0018
     */
    public EiInfo queryPackInfoInPda(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        if (null == inInfo.get("segNo") || StringUtils.isBlank(inInfo.get("segNo").toString())
                || null == inInfo.get("packId") || StringUtils.isBlank(inInfo.get("packId").toString())
                || null == inInfo.get("warehouseCode") || StringUtils.isBlank(inInfo.get("warehouseCode").toString())
                || null == inInfo.get("factoryArea") || StringUtils.isBlank(inInfo.get("factoryArea").toString())) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("业务单元号、仓库编码、捆包或厂区为空，请重试！");
            return outInfo;
        }
        try {
            outInfo = validToken(inInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                return outInfo;
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("token无效！");
            return outInfo;
        }
        String userId = null == outInfo.get("userId") ? GlobalUtils.getUserId() : outInfo.get("userId").toString();
        String userName = null == outInfo.get("userName") ? GlobalUtils.getUserCNNameById() : outInfo.get("userName").toString();
        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }
        inInfo.set("userId",userId);
        inInfo.set("userName", userName);
        inInfo.set(EiConstant.serviceId,"S_UC_PR_0407");
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                     outInfo.setStatus(EiConstant.STATUS_FAILURE);
                     return outInfo;

            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }
        return outInfo;
    }
    /***
     * 查询库位
     * 物流服务：S_UC_PR_230623
     * serviceId:	S_LI_RL_0019
     */
    public EiInfo queryLocationPda(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        if (null == inInfo.get("segNo") || StringUtils.isBlank(inInfo.get("segNo").toString())
                || null == inInfo.get("warehouseCode") || StringUtils.isBlank(inInfo.get("warehouseCode").toString())) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("业务单元号、仓库编码或库位编码为空，请重试！");
            return outInfo;
        }
        try {
            outInfo = validToken(inInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                return outInfo;
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("token无效！");
            return outInfo;
        }
        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }

        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }

        inInfo.set(EiConstant.serviceId,"S_UC_PR_0415");
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;

            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

        return outInfo;
    }

    /***
     * PDA有计划入库
     * 物流服务：S_UC_PR_230636 匹配计划入库
     * ServiceId:S_LI_RL_0020
     */

    public EiInfo warehousingPdaNew(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        String factoryBuilding = (String) inInfo.get("factoryBuilding");//厂房代码
        String factoryArea = (String) inInfo.get("factoryArea");//厂房代码
        String allocateVehicleNo = (String) inInfo.get("allocateVehicleNo");//厂房代码
        String carTraceNo = (String) inInfo.get("carTraceNo");//车辆跟踪号
//        String carTraceNo ="JC20250404253";
        String validString = validWarehousingParam(inInfo.getAttr());
        if (!StringUtils.isBlank(validString)) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(validString);
            return outInfo;
        }
        try {
            outInfo = validToken(inInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                return outInfo;
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("token无效！");
            return outInfo;
        }
        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }
        String userId = String.valueOf(inInfo.get("userId"));
        String userName = String.valueOf(inInfo.get("userName"));

        log("行车编号：", inInfo.get("craneId") + "开始执行有计划入库！");
        //查询开关，判断如果开关为开,生成IMOM库存表数据
        String ifInboundReconciliation = new SwitchUtils().getProcessSwitchValue(inInfo.get("segNo").toString(), "IF_INBOUND_RECONCILIATION", dao);
        if ("1".equals(ifInboundReconciliation)) {
            if ("F1".equals(factoryBuilding) && "CQBG".equals(factoryArea)) {
                EiInfo sendInfo = new EiInfo();
                sendInfo.set(EiConstant.serviceName, "LIDSInterfacesPda");
                sendInfo.set(EiConstant.methodName, "generateInventoryData");
                sendInfo.set("segNo", inInfo.getString("segNo"));
                sendInfo.set("userId", userId);
                sendInfo.set("userName", userName);
                sendInfo.set("craneId", inInfo.get("craneId"));
                sendInfo.set("rowList", inInfo.getAttr().get("rowList"));
                outInfo = XLocalManager.callNewTx(sendInfo);
                if (outInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                    outInfo.setStatus(EiConstant.STATUS_SUCCESS);
                    outInfo.set("msg", "此捆包未与行吊实绩关联，请后续使用PDA进行补录。");
                    outInfo.setMsg("此捆包未与行吊实绩关联，请后续使用PDA进行补录。");
                    log("调用LIDSInterfacesPda.generateInventoryData补充IMOM库存失败：" + sendInfo.getMsg());
                    System.out.println("调用LIDSInterfacesPda.generateInventoryData补充IMOM库存失败：" + sendInfo.getMsg());
//                    throw new RuntimeException("生成IMOM库存表数据失败！");
                }
            }
        }

        List<HashMap> rowList = (List<HashMap>) inInfo.getAttr().get("rowList");
        for (HashMap hashMap : rowList) {

            String damageType="";
            //货损类型集合
            List<String> damageTypeList = (List<String>) hashMap.get("cargoDamageType");
            if (CollectionUtils.isNotEmpty(damageTypeList)){
                damageType=  String.join(",", damageTypeList);
            }
            String locationId = MapUtils.getString(hashMap, "locationId");
            String locationName = MapUtils.getString(hashMap, "locationName");
            //更改捆包状态
            HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
            objectObjectHashMap.put("segNo",hashMap.get("segNo"));
//            objectObjectHashMap.put("allocateVehicleNo",allocateVehicleNo);
            objectObjectHashMap.put("packId",hashMap.get("packId"));
            objectObjectHashMap.put("locationId",locationId);
            objectObjectHashMap.put("locationName",locationName);
            objectObjectHashMap.put("damageType",damageType);//货损类型
            objectObjectHashMap.put("allocateVehicleNo",allocateVehicleNo);//货损类型
            this.dao.update(LIRL0503.UPDATE_PACK_STATUS,objectObjectHashMap);
        }

        //增加开关判断
        //如果是卸货+装货业务的车辆，自动推荐装货的装卸点
        String ifAutomaticallyRecommendedPoint = new SwitchUtils().getProcessSwitchValue((String) inInfo.get("segNo"), "IF_AUTOMATICALLY_RECOMMENDED_POINT", dao);
        if ("1".equals(ifAutomaticallyRecommendedPoint)){
            Map<String, String> hashMapLirl0301 = new HashMap<>();
            hashMapLirl0301.put("segNo", (String) inInfo.get("segNo"));
            hashMapLirl0301.put("carTraceNo", carTraceNo);
            hashMapLirl0301.put("status", "30");
            List<LIRL0301> queryLIRL03011 = this.dao.query(LIRL0301.QUERY, hashMapLirl0301);
            if (CollectionUtils.isNotEmpty(queryLIRL03011)){
                String handType = queryLIRL03011.get(0).getHandType();
                if (StringUtils.isBlank(allocateVehicleNo)||"undefined".equals(allocateVehicleNo)){
                    Map<String, Object> hashMapLirl0502= new HashMap<>();
                    hashMapLirl0502.put("segNo", inInfo.get("segNo"));
                    hashMapLirl0502.put("carTraceNo", carTraceNo);
                    List<LIRL0301> query = this.dao.query(LIRL0301.QUERY, hashMapLirl0502);
                    if (CollectionUtils.isNotEmpty(query)){
                        allocateVehicleNo= query.get(0).getAllocateVehicleNo();
                    }
                }
                if (StringUtils.isNotBlank(allocateVehicleNo)) {
                    if ("30".equals(handType)) {
                        //查询配单表，拿到装卸点，装货配单号
                        Map<String, Object> hashMapLirl0502 = new HashMap<>();
                        hashMapLirl0502.put("segNo", inInfo.get("segNo"));
                        hashMapLirl0502.put("carTraceNo", carTraceNo);
                        hashMapLirl0502.put("allocType", "20");
                        hashMapLirl0502.put("delFlag", "0");
                        hashMapLirl0502.put("allocateVehicleNo", allocateVehicleNo);
                        //查询配单表
                        List<LIRL0502> queryLIRL0502 = this.dao.query(LIRL0502.QUERY, hashMapLirl0502);
                        List<LIRL0502> queryLIRL05021 = new ArrayList<>();
                        if (CollectionUtils.isEmpty(queryLIRL0502)) {
                            HashMap<Object, Object> hashMapLirl05021 = new HashMap<>();
                            hashMapLirl05021.put("segNo", inInfo.get("segNo"));
                            hashMapLirl05021.put("carTraceNo", carTraceNo);
                            hashMapLirl05021.put("allocType", "10");
                            hashMapLirl05021.put("delFlag", "0");
                            hashMapLirl05021.put("allocateVehicleNo", allocateVehicleNo);
                            queryLIRL05021 = this.dao.query(LIRL0502.QUERY, hashMapLirl05021);
                            if (CollectionUtils.isEmpty(queryLIRL05021)) {
                                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                                inInfo.setMsg("卸货配单信息不存在！");
                                return inInfo;
                            }

                        }
                        List<String> collect = new ArrayList<>();
                        if (CollectionUtils.isNotEmpty(queryLIRL0502)) {
                            collect = queryLIRL0502.stream().map(lirl0503 -> lirl0503.getAllocateVehicleNo()).collect(Collectors.toList());
                        } else {
                            collect = queryLIRL05021.stream().map(lirl0503 -> lirl0503.getAllocateVehicleNo()).collect(Collectors.toList());
                        }
                        hashMapLirl0502.put("allocateVehicleNoAdd", collect);
                        hashMapLirl0502.put("outPackFlag", "0");//去除自带货
                        hashMapLirl0502.put("voucherNumFlag", "0"); //去除无计划
                        hashMapLirl0502.put("statusEq", "20"); //去除无计划
                        hashMapLirl0502.put("voucherNumEq", "20"); //去除无计划
                        if (CollectionUtils.isEmpty(queryLIRL0502)) {
                            hashMapLirl0502.put("allocateVehicleNoAdd", "");
                            hashMapLirl0502.put("nextAlcVehicleNoIn", collect.get(0));
                            queryLIRL0502 = queryLIRL05021;
                        }
                        //判断卸货配单是否全部卸完
                        List<HashMap> query = this.dao.query(LIRL0503.QUERY_ALL_PACK_NEW, hashMapLirl0502);
                        if (CollectionUtils.isEmpty(query)) {
                            //叫号通知装货继续前往下一目标装卸点
                            //查询配单表，拿到装卸点，装货配单号
                            //查询0401
                            String nextAlcVehicleNo = queryLIRL0502.get(0).getNextAlcVehicleNo();
                            if (StringUtils.isBlank(nextAlcVehicleNo)) {
                                nextAlcVehicleNo = queryLIRL0502.get(0).getAllocateVehicleNo();
                            }
                            Map<String, Object> lirl0401Map = new HashMap<>();
                            lirl0401Map.put("segNo", inInfo.get("segNo"));
                            lirl0401Map.put("voucherNum", nextAlcVehicleNo); //下级配车单号
                            lirl0401Map.put("carTraceNo", carTraceNo);
                            lirl0401Map.put("vehicleNo", queryLIRL0502.get(0).getVehicleNo());
                            List<HashMap> query1 = this.dao.query(LIRL0401.QUERY_BY_VE_NO, lirl0401Map);
                            if (CollectionUtils.isNotEmpty(query1)) {
                                List<String> handPointName = query1.stream().distinct().map(map -> (String) map.get("handPointName")).collect(Collectors.toList());
                                //发送短信
//                            String content= "尊敬的司机："+query1.get(0).get("driverName")+"您好，请继续前往"+handPointName+"装卸点，谢谢。";
//                            outInfo.set("content",content);
//                            outInfo.set("mobileNum","16639826830");
                                EiInfo eiInfo = new EiInfo();
                                HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
                                objectObjectHashMap.put("param1", queryLIRL03011.get(0).getTelNum());
                                objectObjectHashMap.put("param2", queryLIRL03011.get(0).getDriverName());
                                objectObjectHashMap.put("param3", queryLIRL0502.get(0).getVehicleNo());
                                objectObjectHashMap.put("param4", handPointName);
                                MessageUtils.sendMessage(objectObjectHashMap, "MT0000001016");

                                //根据车辆跟踪号回写配单号
                                HashMap<String, Object> stringObjectHashMap = new HashMap<>();
                                stringObjectHashMap.put("segNo", inInfo.get("segNo"));
                                stringObjectHashMap.put("carTraceNo", carTraceNo);
                                stringObjectHashMap.put("allocateVehicleNo", nextAlcVehicleNo);
                                this.dao.update(LIRL0301.BACK_ALLOCATE_VEHICLE_NO, stringObjectHashMap);

//                                //调用入库质量确认单打印
//                                EiInfo info = new EiInfo();
//                                info.set("segNo", inInfo.get("segNo"));
//                                info.set("allocateVehicleNo", (String) collect.get(0));
//                                info.set("carTraceNo", carTraceNo);
//                                info.set("factoryBuilding", factoryBuilding);
//                                //插入车辆排序表
//                                info.set(EiConstant.serviceName, "LIRLInterfacePda");
//                                info.set(EiConstant.methodName, "printPutinQualityConfirm");
//                                EiInfo outInfo1 = XLocalManager.call(info);
//                                if (outInfo1.getStatus() < 0) {
//                                    inInfo.setStatus(EiConstant.STATUS_SUCCESS);
//                                }
                            }
                        }
                        if (StringUtils.isNotBlank(queryLIRL0502.get(0).getNextAlcVehicleNo())) {
                            HashMap<String, Object> stringObjectHashMap = new HashMap<>();
                            stringObjectHashMap.put("segNo", inInfo.get("segNo"));
                            stringObjectHashMap.put("carTraceNo", carTraceNo);
                            stringObjectHashMap.put("allocateVehicleNo", queryLIRL0502.get(0).getNextAlcVehicleNo());
                            this.dao.update(LIRL0301.BACK_ALLOCATE_VEHICLE_NO, stringObjectHashMap);
                        }
                    } else if ("20".equals(handType)) {

                        //查询配单表，拿到装卸点，装货配单号
                        Map<String, Object> hashMapLirl0502 = new HashMap<>();
                        hashMapLirl0502.put("segNo", inInfo.get("segNo"));
                        hashMapLirl0502.put("carTraceNo", carTraceNo);
                        hashMapLirl0502.put("allocType", "20");
                        hashMapLirl0502.put("delFlag", "0");
                        hashMapLirl0502.put("allocateVehicleNo", allocateVehicleNo);
                        //查询配单表
                        List<LIRL0502> queryLIRL0502 = this.dao.query(LIRL0502.QUERY, hashMapLirl0502);
                        if (CollectionUtils.isEmpty(queryLIRL0502)) {
                            if (CollectionUtils.isEmpty(queryLIRL0502)) {
                                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                                inInfo.setMsg("卸货配单信息不存在！");
                                return inInfo;
                            }
                        }
//                        List<String> collect = queryLIRL0502.stream().map(lirl0503 -> lirl0503.getAllocateVehicleNo()).collect(Collectors.toList());
//                        hashMapLirl0502.put("allocateVehicleNoAdd", collect);
//                        hashMapLirl0502.put("outPackFlag", "0");//去除自带货
//                        hashMapLirl0502.put("voucherNumFlag", "0"); //去除无计划
//                        hashMapLirl0502.put("statusEq", "20"); //去除无计划
//                        hashMapLirl0502.put("voucherNumEq", "20"); //去除无计划
//                        //判断卸货配单是否全部卸完
//                        List<HashMap> query = this.dao.query(LIRL0503.QUERY_ALL_PACK, hashMapLirl0502);
//                        if (CollectionUtils.isEmpty(query)) {
//                            //查询配单表，拿到装卸点，装货配单号
//                            //调用入库质量确认单打印
//                            EiInfo info = new EiInfo();
//                            info.set("segNo", inInfo.get("segNo"));
//                            info.set("allocateVehicleNo", allocateVehicleNo);
//                            info.set("carTraceNo", carTraceNo);
//                            info.set("factoryBuilding", factoryBuilding);
//                            //插入车辆排序表
//                            info.set(EiConstant.serviceName, "LIRLInterfacePda");
//                            info.set(EiConstant.methodName, "printPutinQualityConfirm");
//                            EiInfo outInfo1 = XLocalManager.call(info);
//                            if (outInfo1.getStatus() < 0) {
//                                inInfo.setStatus(EiConstant.STATUS_SUCCESS);
//                            }
//                        }
                    }
                }else {
                    inInfo.setMsg("该车辆没有配单信息！");
                }
            }
        }

//        if (true){
//            throw new RuntimeException("异常");
//        }
        inInfo.set("userId",userId);
        inInfo.set("userName",userName);
        inInfo.set("teamId","10");
        inInfo.set("workingShift","10");
        inInfo.set(EiConstant.serviceId,"S_UC_PR_0402");
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;
            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);

        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }
        //更新库位占用
        String isLocationOccupancyUpdateEnabled = new SwitchUtils().getProcessSwitchValue((String) inInfo.get("segNo"), "IS_LOCATION_OCCUPANCY_UPDATE_ENABLED", dao);
        if ("1".equals(isLocationOccupancyUpdateEnabled)) {
            EiInfo info = new EiInfo();
            info.set("messageBody", rowList);
            info.set("segNo", info.get("segNo"));
            //插入车辆排序表
            info.set(EiConstant.serviceName, "LIDS0606");
            info.set(EiConstant.methodName, "updateLocationToOccupied");
            EiInfo outInfo1 = XLocalManager.call(info);
            if (outInfo1.getStatus() < 0) {
                outInfo1.setStatus(EiConstant.STATUS_SUCCESS);
                return outInfo1;
            }
        }
        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }
        return outInfo;
    }
    /***
     * PDA无计划入库
     * 物流服务：S_UC_PR_230635
     *
     * ServiceId:S_LI_RL_0021
     */

    public EiInfo noPlanWarehousingPda(EiInfo inInfo){

        EiInfo outInfo = new EiInfo();
        String validString = validNoPlanWarehousingParam(inInfo.getAttr());
        if (!StringUtils.isBlank(validString)) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(validString);
            return outInfo;
        }
        String factoryBuilding = (String) inInfo.get("factoryBuilding");
        String factoryArea = (String) inInfo.get("factoryArea");
        try {
            outInfo = validToken(inInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                return outInfo;
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("token无效！");
            return outInfo;
        }
        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }
        String userId = String.valueOf(inInfo.get("userId"));
        String userName = String.valueOf(inInfo.get("userName"));

        //查询开关，判断如果开关为开,生成IMOM实物库存表数据
        String ifInboundReconciliation = new SwitchUtils().getProcessSwitchValue(inInfo.get("segNo").toString(), "IF_INBOUND_RECONCILIATION", dao);
        if ("1".equals(ifInboundReconciliation)) {
            if ("F1".equals(factoryBuilding) && "CQBG".equals(factoryArea)) {
                EiInfo sendInfo = new EiInfo();
                sendInfo.set(EiConstant.serviceName, "LIDSInterfacesPda");
                sendInfo.set(EiConstant.methodName, "generateInventoryData");
                sendInfo.set("segNo", inInfo.getString("segNo"));
                sendInfo.set("userId", userId);
                sendInfo.set("userName", userName);
                sendInfo.set("rowList", inInfo.getAttr().get("rowList"));
                sendInfo = XLocalManager.callNewTx(sendInfo);
                if (sendInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                    log("调用LIDSInterfacesPda.generateInventoryData补充IMOM库存失败：" + sendInfo.getMsg());
                    System.out.println("调用LIDSInterfacesPda.generateInventoryData补充IMOM库存失败：" + sendInfo.getMsg());
                }
            }
        }

        inInfo.set("userId",userId);
        inInfo.set("userName",userName);
        inInfo.set("teamId","10");
        inInfo.set("workingShift","10");
        inInfo.set(EiConstant.serviceId,"S_UC_PR_0403");
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;
            }
            List<Map> list = (List) inInfo.getAttr().get("rowList");


            //判断是否是无计划入库的材料
            HashMap<String, String> lirl0308HashMap = new HashMap<>();
            lirl0308HashMap.put("carTraceNo",(String) list.get(0).get("carTraceNo"));
            lirl0308HashMap.put("vehicleNo",(String) list.get(0).get("vehicleNo"));
            lirl0308HashMap.put("segNo",(String) list.get(0).get("segNo"));
            List<LIRL0301> listLIRL0301 = this.dao.query(LIRL0301.QUERY, lirl0308HashMap);
            if (CollectionUtils.isEmpty(listLIRL0301))
            {
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("车辆跟踪信息不存在，请检查!");
                return inInfo;
            }
            //插入出入库实绩表
            List<HashMap> result = (List<HashMap>) outInfo.get("result");
            for (HashMap hashMap : result) {
                hashMap.put("unitCode",MapUtils.getString(hashMap,"segNo"));
                hashMap.put("putinId","");
                hashMap.put("putoutId"," ");
                hashMap.put("voucherNum"," ");
                hashMap.put("driverName",listLIRL0301.get(0).getDriverName());
                hashMap.put("driverTel",listLIRL0301.get(0).getTelNum());
                hashMap.put("driverIdentity",listLIRL0301.get(0).getIdCard());
                hashMap.put("deliverType"," ");
                hashMap.put("deliverName"," ");
                hashMap.put("matInnerId"," ");
                hashMap.put("factoryOrderNum"," ");
                hashMap.put("prodTypeId"," ");
                hashMap.put("shopsign"," ");
                hashMap.put("prodTypeName"," ");
                hashMap.put("specDesc"," ");
                hashMap.put("weight",0);
                hashMap.put("quantity",0);
                hashMap.put("customerId"," ");
                hashMap.put("customerName"," ");
                hashMap.put("vehicleNo",list.get(0).get("vehicleNo"));
                hashMap.put("putInOutFlag","10");
                hashMap.put("recCreator",inInfo.get("userId"));
                hashMap.put("recCreatorName",inInfo.get("userName"));
                hashMap.put("recCreateTime",DateUtil.curDateTimeStr14());
                hashMap.put("recRevisor",inInfo.get("userId"));
                hashMap.put("recRevisorName",inInfo.get("userName"));
                hashMap.put("recReviseTime",DateUtil.curDateTimeStr14());
                hashMap.put("archiveFlag","0");
                hashMap.put("delFlag","0");
                hashMap.put("remark","无计划入库");
                hashMap.put("sysRemark","无计划入库");
                hashMap.put("uuid",UUIDUtils.getUUID());
                hashMap.put("tenantId"," ");
                hashMap.put("putoutDate"," ");
                hashMap.put("signatureFlag","0");
                hashMap.put("finalDestination"," ");
                hashMap.put("perNo", list.get(0).get("perNo"));
                hashMap.put("perName", list.get(0).get("perName"));
                hashMap.put("handPointId", list.get(0).get("currentHandPointId"));
                hashMap.put("loadId", list.get(0).get("loadId"));
                this.dao.insert(LIRL0308.INSERT,hashMap);
            }

            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }
        return outInfo;
    }
    /**
     * 校验token
     *
     * @param inInfo
     * @return
     */
    public EiInfo validToken(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        if (null == inInfo.get("accessToken")
                || StringUtils.isBlank(inInfo.get("accessToken").toString())) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            logger.error("UCPR0000.validToken校验token入参accessToken为空");
            outInfo.setMsg("token为空！");
            return outInfo;
        }
        String accessToken = inInfo.get("accessToken").toString();
        Map<String,String> userTokenMap = getUserAccessToken(inInfo.get("userId").toString());
        if(MapUtils.isEmpty(userTokenMap) ||
                StringUtils.isBlank(MapUtils.getString(userTokenMap,"accessToken")) ||
                !accessToken.equals(MapUtils.getString(userTokenMap,"accessToken"))){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            logger.error("UCPR0000.validToken校验redisToken无效!");
            outInfo.setMsg("token无效，请重新登录！");
            return outInfo;
        }
        setUserAccessToken(inInfo.get("userId").toString(),userTokenMap);
        outInfo.setAttr(userTokenMap);
        outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        outInfo.setMsg("成功");
        return outInfo;
    }


    /**
     * 设置accessToken
     *
     */
    public void setUserAccessToken(String userId,Map<String,String> map) {
        redisTemplate.opsForValue().set(USER_ID_TOKEN + userId, map, 360 , TimeUnit.MINUTES);
    }

    /**
     * 设置accessToken
     *
     */
    public void setUserAccessToken(String userId,String userName, String accessToken) {
        Map<String,String> map = Maps.newConcurrentMap();
        map.put("userId",userId);
        map.put("userName",userName);
        map.put("accessToken",accessToken);
        redisTemplate.opsForValue().set(USER_ID_TOKEN + userId, map, 6 , TimeUnit.HOURS);
    }

    /**
     * 获取accessToken
     *
     * @param userId
     */
    public Map<String,String> getUserAccessToken(String userId) {
        Object refreshTokenObject = redisTemplate.opsForValue().get(USER_ID_TOKEN + userId);
        if (null == refreshTokenObject) {
            logger.error("LIRLInterfaces.getUserAccessToken获取userId"+userId+"redisToken为空");
            return Maps.newConcurrentMap();
        }
        return (Map<String, String>) refreshTokenObject;
    }

    /**
     * 校验入库必填参数
     *
     * @param paramMap
     * @return
     */
    private String validWarehousingParam(Map paramMap) {


        if (null == paramMap.get("segNo") || StringUtils.isBlank(paramMap.get("segNo").toString())) {
            return "业务单元号为空！";
        }


        if (null == paramMap.get("warehouseCode") || StringUtils.isBlank(paramMap.get("warehouseCode").toString()) ||
                null == paramMap.get("warehouseName") || StringUtils.isBlank(paramMap.get("warehouseName").toString())) {
            return "仓库编码或名称为空！";
        }

       /* if (null == paramMap.get("putinType") || StringUtils.isBlank(paramMap.get("putinType").toString())) {
            return "入库类型为空！";
        }*/

        if (null == paramMap.get("rowList") || !(paramMap.get("rowList") instanceof List)
                || CollectionUtils.isEmpty((List) paramMap.get("rowList"))) {
            return "没有入库明细！";
        }

        List<Map> rowMapList = (List) paramMap.get("rowList");
        for (Map rowMap : rowMapList) {
            if (null == rowMap.get("location") || StringUtils.isBlank(rowMap.get("location").toString())) {
                return "库位为空！";
            }

            String locationName = (String) rowMap.get("locationName");
            if (null == locationName || StringUtils.isBlank(locationName)){
                rowMap.put("locationName",rowMap.get("location"));
            }
            if (null == rowMap.get("locationName") || StringUtils.isBlank(rowMap.get("locationName").toString())) {
                return "库位名称为空！";
            }
            if (null == rowMap.get("packId") || StringUtils.isBlank(rowMap.get("packId").toString())) {
                return "捆包号为空！";
            }
            if (null == rowMap.get("lgsSequenceFlag") || StringUtils.isBlank(rowMap.get("lgsSequenceFlag").toString())) {
                return "一二程标记为空！";
            }
            if (null == rowMap.get("putinPlanId") || StringUtils.isBlank(rowMap.get("putinPlanId").toString())) {
                return "车辆跟踪号为空！";
            }
            if (null == rowMap.get("carTraceNo") || StringUtils.isBlank(rowMap.get("carTraceNo").toString())) {
                return "车辆跟踪号为空！";
            }
            if (null == rowMap.get("vehicleNo") || StringUtils.isBlank(rowMap.get("vehicleNo").toString())) {
                return "车牌号号为空！";
            }
            if (rowMap.get("lgsSequenceFlag").toString().equals("2")) {
                if (null == rowMap.get("putinType") || StringUtils.isBlank(rowMap.get("putinType").toString())) {
                    return "入库类型为空！";
                }
                if (null == rowMap.get("factoryOrderNum") || StringUtils.isBlank(rowMap.get("factoryOrderNum").toString())) {
                    return "钢厂订单号为空！";
                }
                String putinType = rowMap.get("putinType").toString();
                if (!putinType.equals("3130") && !putinType.equals("3140")) {
                    if (!(putinType.equals("3110") && rowMap.get("tradeCode").toString().equals("0"))) {
                        if (null == rowMap.get("dUserNum") || StringUtils.isBlank(rowMap.get("dUserNum").toString())) {
                            return "分户号为空！";
                        }
                        if (null == rowMap.get("settleUserNum") || StringUtils.isBlank(rowMap.get("settleUserNum").toString())) {
                            return "客户代码为空！";
                        }
                    }
                }

            }
        }
        if (null != paramMap.get("noInPackList") && paramMap.get("noInPackList") instanceof List
                && !CollectionUtils.isEmpty((List) paramMap.get("noInPackList"))) {
            List<Map> rowNoPlanMapList = (List) paramMap.get("noInPackList");
            for (Map rowMapNo : rowNoPlanMapList) {
                if (null == rowMapNo.get("location") || StringUtils.isBlank(rowMapNo.get("location").toString())) {
                    return "库位为空！";
                }
                if (null == rowMapNo.get("locationName") || StringUtils.isBlank(rowMapNo.get("locationName").toString())) {
                    return "库位名称为空！";
                }
                if (null == rowMapNo.get("packId") || StringUtils.isBlank(rowMapNo.get("packId").toString())) {
                    return "捆包号为空！";
                }
                if (null == rowMapNo.get("carTraceNo") || StringUtils.isBlank(rowMapNo.get("carTraceNo").toString())) {
                    return "车辆跟踪号为空！";
                }
                if (null == rowMapNo.get("vehicleNo") || StringUtils.isBlank(rowMapNo.get("vehicleNo").toString())) {
                    return "车牌号号为空！";
                }
            }
        }

        return "";
    }

    /**
     * 校验入库必填参数
     *
     * @param paramMap
     * @return
     */
    private String validNoPlanWarehousingParam(Map paramMap) {


        if (null == paramMap.get("segNo") || StringUtils.isBlank(paramMap.get("segNo").toString())) {
            return "业务单元号为空！";
        }


        if (null == paramMap.get("warehouseCode") || StringUtils.isBlank(paramMap.get("warehouseCode").toString()) ||
                null == paramMap.get("warehouseName") || StringUtils.isBlank(paramMap.get("warehouseName").toString())) {
            return "仓库编码或名称为空！";
        }

        if (null == paramMap.get("rowList") || !(paramMap.get("rowList") instanceof List)
                || CollectionUtils.isEmpty((List) paramMap.get("rowList"))) {
            return "没有入库明细！";
        }

        List<Map> rowMapList = (List) paramMap.get("rowList");
        for (Map rowMap : rowMapList) {
            if (null == rowMap.get("location") || StringUtils.isBlank(rowMap.get("location").toString())) {
                return "库位为空！";
            }
            if (null == rowMap.get("locationName") || StringUtils.isBlank(rowMap.get("locationName").toString())) {
                return "库位名称为空！";
            }
            if (null == rowMap.get("packId") || StringUtils.isBlank(rowMap.get("packId").toString())) {
                return "捆包号为空！";
            }
            if (null == rowMap.get("carTraceNo") || StringUtils.isBlank(rowMap.get("carTraceNo").toString())) {
                return "车辆跟踪号为空！";
            }
            if (null == rowMap.get("vehicleNo") || StringUtils.isBlank(rowMap.get("vehicleNo").toString())) {
                return "车牌号号为空！";
            }
        }

        return "";
    }

    /***
     *
     * PAD查询厂区
     *
     * ServiceId::S_LI_RL_0008
     * @param inInfo
     * @return
     */
    public EiInfo queryFactoryPda(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        if (null == inInfo.get("segNo") || StringUtils.isBlank(inInfo.get("segNo").toString())) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("业务单元为空！");
            return outInfo;
        }
        /*
        try {
            outInfo = validToken(inInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                return outInfo;
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("token无效！");
            return outInfo;
        }*/

        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("segNo", inInfo.get("segNo"));
        hashMap.put("status","20");
        //查询厂区
        List<LIDS0102> queryLIDS0102 = this.dao.query(LIDS0102.QUERY_FACTORY, hashMap);
        outInfo.set("list",queryLIDS0102);
        outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        return outInfo;
    }


    /***
     *
     * PAD查询仓库列表
     *
     * ServiceId::S_LI_RL_0009
     * @param inInfo
     * @return
     */
    public EiInfo queryWarehouseListPda(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        if (null == inInfo.get("segNo") || null == inInfo.get("factoryArea")
                || StringUtils.isBlank(inInfo.get("segNo").toString())
                || StringUtils.isBlank(inInfo.get("factoryArea").toString())) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            logger.error("UCPR0000.queryWarehouseListPda业务单元或厂区编码为空!");
            outInfo.setMsg("业务单元或厂区编码为空!");
            return outInfo;
        }

        String segNo = (String)inInfo.get("segNo");
        // 安徽还是用原先的逻辑
        if (!"JC000000".equals(segNo)) {
                    try {
            outInfo = validToken(inInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                return outInfo;
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("token无效！");
            return outInfo;
        }

        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }

        inInfo.set(EiConstant.serviceId,"S_UC_PR_0409");
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;

            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }
        } else {
            try {
                HashMap<String, Object> hashMap = new HashMap<>();
                hashMap.put("segNo", segNo);
                hashMap.put("factoryBuilding", inInfo.get("factoryArea"));
                List list = this.dao.query(LIDS0601.QUERY_WAREHOUSE, hashMap);
                outInfo.set("result",list);
                outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            } catch (Exception ex) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg(ex.getMessage());
            }
        }
        return outInfo;
    }

    /***
     * 查询手机提单普通列表
     * 物流服务：S_UC_PR_230653 查询手机提单普通列表
     * ServiceId:S_LI_RL_0036
     */

    public EiInfo queryLadingBillListByMobileNormal(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        if (null == inInfo.get("segNo") || StringUtils.isBlank(inInfo.get("segNo").toString()) ||
                null == inInfo.get("mobile") || StringUtils.isBlank(inInfo.get("mobile").toString())){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("业务单元或手机号为空!");
            return outInfo;
        }

        try {
            outInfo = validToken(inInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                return outInfo;
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("token无效！");
            return outInfo;
        }

        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }

        inInfo.set(EiConstant.serviceId,"S_UC_PR_0405");
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;

            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }
        return outInfo;
    }

    /***
     * 查询车牌提单号普通列表
     * 物流服务：S_UC_PR_230657 查询车牌提单号普通列表
     * ServiceId:S_LI_RL_0037
     */

    public EiInfo queryLadingBillListByVehicleNoNormal(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        if (null == inInfo.get("segNo") || StringUtils.isBlank(inInfo.get("segNo").toString()) ||
                null == inInfo.get("vehicleNo") || StringUtils.isBlank(inInfo.get("vehicleNo").toString())){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("业务单元或车牌号为空!");
            return outInfo;
        }

        try {
            outInfo = validToken(inInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                return outInfo;
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("token无效！");
            return outInfo;
        }

        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }

        inInfo.set(EiConstant.serviceId,"S_UC_PR_0406");
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;

            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }
        return outInfo;
    }

    /***
     * 查询提货单对象列表
     * 物流服务：S_UC_PR_230628 查询提货单对象列表
     * ServiceId:S_LI_RL_0040
     */

    public EiInfo queryLadingBillMapListPda(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        if (null == inInfo.get("segNo") || StringUtils.isBlank(inInfo.get("segNo").toString()) ||
                null == inInfo.get("warehouseCode") || StringUtils.isBlank(inInfo.get("warehouseCode").toString())
                || null == inInfo.get("factoryArea") || StringUtils.isBlank(inInfo.get("factoryArea").toString())) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("业务单元号或仓库或厂区为空，请重试！");
            return outInfo;
        }
        try {
            outInfo = validToken(inInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                return outInfo;
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("token无效！");
            return outInfo;
        }

        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }

        inInfo.set(EiConstant.serviceId,"S_UC_PR_0410");
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;

            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }
        return outInfo;
    }

    /***
     * 查询提货单重量件数
     * 物流服务：S_UC_PR_230621 查询提货单重量件数
     * ServiceId:S_LI_RL_0041
     */

    public EiInfo queryLadingBillWeight(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        if (null == inInfo.get("segNo") || StringUtils.isBlank(inInfo.get("segNo").toString()) ||
                null == inInfo.get("warehouseCode") || StringUtils.isBlank(inInfo.get("warehouseCode").toString())) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("业务单元号或仓库为空，请重试！");
            return outInfo;
        }
        if (null == inInfo.get("ladingBillIdList") || StringUtils.isBlank(inInfo.get("ladingBillIdList").toString())) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("提货单列表为空，请重试！");
            return outInfo;
        }
        List<String> ladingBillIdList = (List<String>) inInfo.get("ladingBillIdList");
        if (CollectionUtils.isEmpty(ladingBillIdList)) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("提货单列表为空，请重试！");
            return outInfo;
        }

        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }

        inInfo.set(EiConstant.serviceId,"S_UC_PR_0411");
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;
            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }
        return outInfo;
    }

    /***
     * 查询出库捆包
     * 物流服务：S_UC_PR_230602 查询出库捆包
     * ServiceId:S_LI_RL_0042
     */

    public EiInfo queryPackInfoOutPda(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        if (null == inInfo.get("segNo") || StringUtils.isBlank(inInfo.get("segNo").toString())
                || null == inInfo.get("packId") || StringUtils.isBlank(inInfo.get("packId").toString())
                || null == inInfo.get("warehouseCode") || StringUtils.isBlank(inInfo.get("warehouseCode").toString())) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("业务单元号、仓库编码或捆包号为空，请重试！");
            return outInfo;
        }
        try {
            outInfo = validToken(inInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                return outInfo;
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("token无效！");
            return outInfo;
        }

        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }

        inInfo.set(EiConstant.serviceId,"S_UC_PR_0412");
        inInfo.set("planParticle",inInfo.get("planParticle")); //判断是否形式提单
//        inInfo.set("planParticle","20"); //判断是否形式提单
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;

            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }
        return outInfo;
    }


    /***
     * PDA出库
     * 物流服务： PDA出库
     * ServiceId:S_LI_RL_0038
     */

    public EiInfo exWarehousePdaNew(EiInfo inInfo){

        EiInfo outInfo = new EiInfo();
        String validString = validExWarehousingParam(inInfo.getAttr(),inInfo);

        if (!StringUtils.isBlank(validString)) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(validString);
            return outInfo;
        }

        try {
            outInfo = validToken(inInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                return outInfo;
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("token无效！");
            return outInfo;
        }


        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }

        inInfo.set("teamId","10");
        inInfo.set("workingShift","10");
        inInfo.set(EiConstant.serviceId,"S_UC_PR_0404");
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;

            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }
        return outInfo;
    }

    /**
     * 校验出库必填参数
     *
     * @param paramMap
     * @return
     */
    private String validExWarehousingParam(Map paramMap,EiInfo info) {
        if (null == paramMap.get("segNo") || StringUtils.isBlank(paramMap.get("segNo").toString())) {
            return "业务单元号为空！";
        }

        if (null == paramMap.get("warehouseCode") || StringUtils.isBlank(paramMap.get("warehouseCode").toString()) ||
                null == paramMap.get("warehouseName") || StringUtils.isBlank(paramMap.get("warehouseName").toString())) {
            return "仓库编码或名称为空！";
        }

      /*  if (null == paramMap.get("putoutType") || StringUtils.isBlank(paramMap.get("putoutType").toString())) {
            return "出库类型为空！";
        }*/

        if (null == paramMap.get("vehicleNo") || StringUtils.isBlank(paramMap.get("vehicleNo").toString())) {
            return "车牌号为空！";
        }

        if (null == paramMap.get("rowList") || !(paramMap.get("rowList") instanceof List)
                || CollectionUtils.isEmpty((List) paramMap.get("rowList"))) {
            return "没有出库明细！";
        }

        List<Map> rowMapList = (List) paramMap.get("rowList");
        for (Map rowMap : rowMapList) {
            if (null == rowMap.get("packId") || StringUtils.isBlank(rowMap.get("packId").toString())) {
                return "捆包号为空！";
            }
            if (null == rowMap.get("putoutType") || StringUtils.isBlank(rowMap.get("putoutType").toString())) {
                return "出库类型为空！";
            }
            String putoutType = rowMap.get("putoutType").toString();
            if (!putoutType.equals("3030")) {
                if (null == rowMap.get("dUserNum") || StringUtils.isBlank(rowMap.get("dUserNum").toString())) {
                    return "分户号为空！";
                }
                if (null == rowMap.get("settleUserNum") || StringUtils.isBlank(rowMap.get("settleUserNum").toString())) {
                    return "客户代码为空！";
                }
            }
            if (null == rowMap.get("matInnerId") || StringUtils.isBlank(rowMap.get("matInnerId").toString())) {
                return "材料管理号为空！";
            }
            if (null == rowMap.get("putoutPlanId") || StringUtils.isBlank(rowMap.get("putoutPlanId").toString())) {
                return "出库计划编号为空！";
            }
//            if (null == rowMap.get("carTraceNo") || StringUtils.isBlank(rowMap.get("carTraceNo").toString())) {
//                return "车辆跟踪号为空！";
//            }
            if (null == rowMap.get("factoryOrderNum") || StringUtils.isBlank(rowMap.get("factoryOrderNum").toString())) {
                return "钢厂资源号为空！";
            }
            rowMap.put("vehicleNo",info.get("vehicleNo"));
            rowMap.put("vehicleNo",paramMap.get("vehicleNo"));
        }
        return "";
    }

    /***
     * 查询车辆所有的装卸点
     * @param eiInfo
     * @serviceId：S_LI_RL_0039
     * @return
     */
    public EiInfo queryNextTargetPoint(EiInfo eiInfo){

        String segNo = (String)eiInfo.get("segNo"); //目标装卸点
        // String targetHandPointId = (String)eiInfo.get("handPointId"); //当前装卸点
        String carTraceNo = (String)eiInfo.get("carTraceNo"); //当前装卸点
        String vehicleNo = (String)eiInfo.get("vehicleNo"); //当前装卸点
        String factoryArea = (String)eiInfo.get("factoryArea"); //当前装卸点

        if (StringUtils.isBlank(segNo)  ||
                StringUtils.isBlank(carTraceNo) || StringUtils.isBlank(vehicleNo) ||
                StringUtils.isBlank(factoryArea)) {
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg("账套、车辆跟踪号、车牌号、厂区不能为空！");
            return eiInfo;
        }

        //效验token
        EiInfo outInfo = validToken(eiInfo);
        if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("token无效！");
            return outInfo;
        }
        //根据目标装卸点查询车辆跟踪表
        HashMap<Object, Object> hashMap = new HashMap<>();
        hashMap.put("segNo",segNo);
        // hashMap.put("handPointId",targetHandPointId);
        hashMap.put("carTraceNo",carTraceNo);
        hashMap.put("vehicleNo",vehicleNo);
        hashMap.put("factoryArea",factoryArea);

        List<LIRL0301> listLIRL0301 = this.dao.query(LIRL0301.QUERY_NEXT_TARGET_POINT, hashMap);
        eiInfo.set("list",listLIRL0301);
        eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
        return eiInfo;
    }

    /***
     * 发送短信
     * @param lirl0401 排序主表
     * @return
     */
    public EiInfo senMessage(Map lirl0401) {
        EiInfo vzbmInfo = new EiInfo();
        List list = new ArrayList();
        String driverTel = "";//司机手机号
        String driverName = "";//司机姓名
        String targetHandPointId = MapUtils.getString(lirl0401, "targetHandPointId", "");//装卸点
        String carTraceNo = MapUtils.getString(lirl0401, "carTraceNo", "");//装卸点
        String vehicleNo = MapUtils.getString(lirl0401, "vehicleNo", "");//装卸点
        String segNo = MapUtils.getString(lirl0401, "segNo", "");//装卸点
        String handPointName= "";

        //根据车辆跟踪号、车牌号、账套查询司机信息
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("carTraceNo",carTraceNo);
        hashMap.put("vehicleNo",vehicleNo);
        hashMap.put("segNo",segNo);
        hashMap.put("handPointId",targetHandPointId);
        //查询装卸点名称
        List<LIRL0304> queryLIRL0304 = this.dao.query(LIRL0304.QUERY, hashMap);
        if (queryLIRL0304.size()>0){
            handPointName = queryLIRL0304.get(0).getHandPointName();
        }
        List<LIRL0301> queryLIRL0301 = this.dao.query(LIRL0301.QUERY, hashMap);
        if (queryLIRL0301.size()>0){
            driverName= queryLIRL0301.get(0).getDriverName();
            driverTel= queryLIRL0301.get(0).getTelNum();
            vehicleNo=queryLIRL0301.get(0).getVehicleNo();
        }

//        String content = "车牌号："+vehicleNo+",司机："+driverName+",现可进厂,请到"+handPointName+"装卸点装卸货,谢谢。";

        //短信内容
        list.add(vehicleNo);//司机姓名
        list.add(driverName);//验证码
        list.add(handPointName);//装卸点名称

        //短信接收人信息
        List list2 = new ArrayList();
        HashMap hashMap2 = new HashMap<>();
        hashMap2.put("mobile",driverTel);
        hashMap2.put("receiverJobNo",driverName);
        hashMap2.put("receiverName", driverName);
        list2.add(hashMap2);

        vzbmInfo.set("params",list);
        vzbmInfo.set("receiver",list2);
        vzbmInfo.set("msgTemplateId","MT0000001001");//短信登记号
        vzbmInfo.set(EiConstant.serviceId, "S_VI_PM_9067");
        vzbmInfo = EServiceManager.call(vzbmInfo, TokenUtils.getXplatToken());
        if (vzbmInfo.getStatus() == EiConstant.STATUS_FAILURE) {
            //打印日志到elk
            log(  "排队叫号定时任务：短信接口传入参数：" + vzbmInfo + "\n" + "排队叫号定时任务 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
            //输出到应用日志
            System.out.println("排队叫号定时任务：短信接口传入参数：" + vzbmInfo + "\n" + "排队叫号定时任务 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
            throw new RuntimeException(vzbmInfo.toJSONString());
        } else {
            //打印日志到elk
            log( "排队叫号定时任务：短信接口传入参数：" + vzbmInfo + "\n" + "排队叫号定时任务 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
            //输出到应用日志
            System.out.println("排队叫号定时任务：短信接口传入参数：" + vzbmInfo + "\n" + "排队叫号定时任务 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
        }
        return vzbmInfo;
    }

    public EiInfo updateHandPointJobNumber(String segNo, String lv_min_hand_point, int in_current_job_number, int in_pre_job_number, int in_trace_type) {
        EiInfo eiInfo = new EiInfo();
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("segNo",segNo);
        hashMap.put("handPointId",lv_min_hand_point);
        hashMap.put("currentJobNumber",in_current_job_number);
        hashMap.put("preJobNumber",in_pre_job_number);
        hashMap.put("remark"," ");
        hashMap.put("sysRemark"," ");
        hashMap.put("delFlag","0");
        hashMap.put("tenantId","");
        hashMap.put("archiveFlag","0");
        hashMap.put("recCreator", "system");
        hashMap.put("recCreatorName", "system");
        hashMap.put("recRevisor", "system");
        hashMap.put("recRevisorName", "system");
        // 修改时间
        hashMap.put("recReviseTime", DateUtil.curDateTimeStr14());

        // 创建时间
        hashMap.put("recCreateTime", DateUtil.curDateTimeStr14());

        RecordUtils.setRevisor(hashMap);
        if (in_trace_type == 20 || in_trace_type == 42) {
            //--如果是进厂删除叫号表，预计作业数都是-1+1
            //更新装卸点进车跟踪表
            this.dao.update(LIRL0306.UPDATE_JOB_NUMBER_BY_HAND_POINT, hashMap);
        } else if (in_trace_type == 21) {
            //更新装卸点进车跟踪表
            this.dao.update(LIRL0306.UPDATE_JOB_NUMBER_BY_HAND_POINT, hashMap);
        } else if (in_trace_type == 60) {
            dao.update(LIRL0306.UPDATE_JOB_NUMBER_BY_HAND_POINT4, hashMap);
        } else if (in_trace_type == 40) {
            dao.update(LIRL0306.UPDATE_JOB_NUMBER_BY_HAND_POINT3, hashMap);
        } else if (in_trace_type == 50) {
            dao.update(LIRL0306.UPDATE_JOB_NUMBER_BY_HAND_POINT2, hashMap);
        }else {
            //更新装卸点进车跟踪表
            this.dao.update(LIRL0306.UPDATE_JOB_NUMBER_BY_HAND_POINT, hashMap);
        }
        //调用状态变化过程
        eiInfo = procHandPointStatusTrace(segNo, lv_min_hand_point);
        return eiInfo;
    }

    /***
     * 更新装卸状态
     * @param segNo 系统账套
     * @param lv_min_hand_point 装卸点代码
     */
    private EiInfo procHandPointStatusTrace(String segNo, String lv_min_hand_point) {
        EiInfo eiInfo = new EiInfo();
        // --作业状态：
        // --10车辆饱和：当前+预计>=最大容纳车辆数
        // --20可以进车：当前作业数=0，预计作业数=0  当前+预计<最大容纳车辆数
        // --30暂停进车：装卸点暂不用
        //查询装卸点状态跟踪表
        Map<String, String> hashMap = new HashMap<>();
        hashMap.put("segNo", segNo);
        hashMap.put("handPointId", lv_min_hand_point);
        // 创建时间
        hashMap.put("recCreateTime", DateUtil.curDateTimeStr14());
        String lv_status = "";
        List<LIRL0306> queryLIRL0306 = this.dao.query(LIRL0306.QUERY, hashMap);
        if (queryLIRL0306.size() > 0) {
            BigDecimal currentJobNumber = queryLIRL0306.get(0).getCurrentJobNumber(); //当前作业数
            BigDecimal preJobNumber = queryLIRL0306.get(0).getPreJobNumber();//预计作业数
            //查此装卸点的最大容纳车辆数
            List<HashMap> queryLIRL0304 = this.dao.query(LIRL0304.QUERY_VEHICLE_NUMER, hashMap);
            if (queryLIRL0304.size() > 0) {
                String vehicleNumer = MapUtils.getString(queryLIRL0304.get(0), "vehicleNumer", "");
                // 10车辆饱和：当前+预计>=最大容纳车辆数

                if (Float.parseFloat(String.valueOf(currentJobNumber))+Float.parseFloat(String.valueOf(preJobNumber))>=Float.parseFloat(vehicleNumer)&&Float.parseFloat(vehicleNumer)!=0) {
                    lv_status = "10";//饱和
                }
                // --20可以进车：当前作业数=0，预计作业数=0  当前+预计<最大容纳车辆数
                //TODO 分开转换
                if (Float.parseFloat(String.valueOf(currentJobNumber))+Float.parseFloat(String.valueOf(preJobNumber))<Float.parseFloat(vehicleNumer)) {
                    lv_status = "20";//可以进车
                }

                if (Float.parseFloat(String.valueOf(currentJobNumber))+Float.parseFloat(String.valueOf(preJobNumber))>Float.parseFloat(vehicleNumer)&&Float.parseFloat(vehicleNumer)==0) {
                    lv_status = "10";//饱和
                }

                if (Float.parseFloat(String.valueOf(currentJobNumber))+Float.parseFloat(String.valueOf(preJobNumber))==0&&Float.parseFloat(vehicleNumer)==0){
                    lv_status = "20";//可以进车
                }
                hashMap.put("status", lv_status);
                hashMap.put("recCreator", "system");
                hashMap.put("recCreatorName", "system");
                hashMap.put("recRevisor", "system");
                hashMap.put("recRevisorName", "system");
                // 修改时间
                hashMap.put("recReviseTime", DateUtil.curDateTimeStr14());
                // 更新车辆跟踪的进车状态
                this.dao.update(LIRL0306.UPDATELV_STATUS, hashMap);
            }

        }
        return eiInfo;
    }

    /**
     * PDA附件上传
     * S_LI_RL_0053
     */
    public EiInfo pdaFileUpload(EiInfo inInfo) {
        try {
            // 获取前端参数
            List<String> stringList = (List<String>) inInfo.get("pictureList");
            String segNo = (String)inInfo.get("segNo");
            String packId = (String)inInfo.get("packId");//附件Id
            List<String> urlList = new ArrayList<>();
            // List<String> listUrl = new ArrayList<>();
            for (int i = 0; i < stringList.size(); i++) {
                String base64String = stringList.get(i);
                CommonsMultipartFile file = Base64ToMultipartFileConverter.convertToMultipartFile(base64String);
                String fileName = file.getOriginalFilename();
                if (StrUtil.isBlank(fileName)) {
                    fileName = "";
                }
                // 生成文件ID
                String fileId = StrUtil.getUUID();
                // 获取文件后缀
                String suffix = fileName.substring(fileName.lastIndexOf("."));

                String directory ="/PDA/"+segNo;
                String downloadUrl = FileUtils.uploadFile(file, directory);
                if (downloadUrl == null) {
                    throw new PlatException("文件上传失败");
                }
                // 待新增的附件记录
                LIRL0312 lirl0312 = new LIRL0312();
                lirl0312.setRelevanceId(packId);
                // 文件信息
                lirl0312.setUploadFileName(fileName);
                lirl0312.setFifleType(suffix);
                lirl0312.setFifleSize(new BigDecimal(file.getSize()));
                lirl0312.setFileId(fileId);
                // 设置文件下载路径
                lirl0312.setUploadFilePath(downloadUrl);
                lirl0312.setRecCreator("System");
                lirl0312.setRecCreatorName("System");
                lirl0312.setRecRevisor("System");
                lirl0312.setRecRevisorName("System");
                lirl0312.setSignatureMark("PDA");
                lirl0312.setSegNo(segNo);
                lirl0312.setUnitCode(segNo);
                lirl0312.setUuid(StrUtil.getUUID());
                lirl0312.setRelevanceType("PDA");
                lirl0312.setSignatureMark("0");
                Map insMap = lirl0312.toMap();
                dao.insert(LIRL0312.INSERT, insMap);
                urlList.add(downloadUrl);

            }
            inInfo.set("list",urlList);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("附件上传成功!");
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

    /***
     * 查询货损类型成功
     * 物流服务： 查询货损类型成功
     * ServiceId:S_LI_RL_0054
     */

    public EiInfo queryCargoDamagePda(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        if (null == inInfo.get("segNo") || StringUtils.isBlank(inInfo.get("segNo").toString())) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("账套为空！");
            return outInfo;
        }

        try {
            outInfo = validToken(inInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                return outInfo;
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("token无效！");
            return outInfo;
        }


        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }

        inInfo.set(EiConstant.serviceId,"S_UC_PR_0416");
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;

            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }
        return outInfo;
    }


    /***
     * PDA查询正在排队和叫号的车辆
     *	S_LI_RL_0057
     */
    public EiInfo queryInqueueVehicle(EiInfo eiInfo){

        EiInfo outInfo = new EiInfo();
        try {
            //获取账套、人员工号
            String segNo = (String) eiInfo.get("segNo"); //账套
            String perNo = (String) eiInfo.get("perNo"); //人员工号
            String factoryArea = (String) eiInfo.get("factoryArea"); //人员工号
            String factoryBuilding = (String) eiInfo.get("factoryBuilding"); //人员工号
            if (StringUtils.isBlank(segNo)  ||StringUtils.isBlank(perNo) ) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("账套、人员工号不能为空！");
                return eiInfo;
            }
            try {
//                outInfo = validToken(eiInfo);
//                if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
//                    outInfo.setStatus(EiConstant.STATUS_DEFAULT);
//                    return outInfo;
//                }
            } catch (Exception e) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("token无效！");
                return outInfo;
            }
            //查询工号是行车工还是叉车工
            HashMap<Object, Object> hashMap = new HashMap<>();
            hashMap.put("segNo",segNo);
            hashMap.put("perNo",perNo);
            List<HashMap>  persList = dao.query("LIRL0502.queryPersonType", hashMap);
            String perType= "";
            if (persList.size()<=0 && CollectionUtils.isEmpty(persList)){
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("此人员没有配置人员类型,请检查人员为叉车工还是行车工,并配置数据!");
                return eiInfo;
            }
            List<HashMap> VehicleList=new ArrayList<>();
            for (HashMap map : persList) {
                HashMap<Object, Object> vehhashMap = new HashMap<>();
                vehhashMap.put("segNo",segNo);
                vehhashMap.put("perType",map.get("perType"));
                if ("1".equals(map.get("perType"))){
                    //叉车工
                    //叉车工只能看：资材卸货、周转架、存货退货、零件退货
                    List<HashMap>  VehicleList1 = this.dao.query(LIRL0301.QUREY_VEHICLE_LIST_CC, vehhashMap);
                    if (CollectionUtils.isNotEmpty(VehicleList1)){
                        List<HashMap> validVehicles = new ArrayList<>();
                        for (HashMap hashMap1 : VehicleList1) {
                            hashMap1.put("segNo",segNo);
                            hashMap1.put("factoryArea",factoryArea);
                            hashMap1.put("factoryBuilding",factoryBuilding);
                            List<String> queryMap = this.dao.query(LIRL0304.QUERY_WAIT_ALL_CALL_HAND_ID, hashMap1);
                            if (CollectionUtils.isNotEmpty(queryMap)){
                                hashMap1.put("handPointName",String.join(",",queryMap));
                                validVehicles.add(hashMap1);
                            }
                        }
                        VehicleList.addAll(validVehicles);
                    }
                }else if ("2".equals(map.get("perType"))){
                    //行车工
                    //行车工只能看:装货、卸货、卸货+装货、废料提货
                    List<HashMap>  VehicleList2 = this.dao.query(LIRL0301.QUREY_VEHICLE_LIST_HC, vehhashMap);
                    if (CollectionUtils.isNotEmpty(VehicleList2)){
                        List<HashMap> validVehicles = new ArrayList<>();
                        for (HashMap hashMap1 : VehicleList2) {
                            hashMap1.put("segNo",segNo);
                            hashMap1.put("factoryArea",factoryArea);
                            hashMap1.put("factoryBuilding",factoryBuilding);
                            List<String> queryMap = this.dao.query(LIRL0304.QUERY_WAIT_ALL_CALL_HAND_ID, hashMap1);
                            if (CollectionUtils.isNotEmpty(queryMap)){
                                hashMap1.put("handPointName",String.join(",",queryMap));
                                validVehicles.add(hashMap1);
                            }
                        }
                        VehicleList.addAll(validVehicles);
                    }
                }
            }
            eiInfo.set("list",VehicleList);
            eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg(e.getMessage());
            return eiInfo;
        }
        return eiInfo;
    }

    /***
     * PDA更新车辆信息显示标记为1
     *	S_LI_RL_0063
     */
    public EiInfo updateVehicleShowFlag(EiInfo eiInfo){

        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> result = (List<HashMap>)eiInfo.get("result");
            if (CollectionUtils.isEmpty(result)){
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("明细数据不存在!");
                return eiInfo;
            }
            //获取账套、人员工号
            String segNo = (String) eiInfo.get("segNo"); //账套
            String perNo = (String) eiInfo.get("perNo"); //人员工号
            if (StringUtils.isBlank(segNo)  ||StringUtils.isBlank(perNo) ) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("账套、人员工号不能为空！");
                return eiInfo;
            }
            try {
                outInfo = validToken(eiInfo);
                if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                    outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                    return outInfo;
                }
            } catch (Exception e) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("token无效！");
                return outInfo;
            }
            for (HashMap hashMap : result) {
                hashMap.put("showFlag", 1);//状态
                // 修改人工号
                hashMap.put("recRevisor", eiInfo.get("userId"));
                // 修改人姓名
                hashMap.put("recRevisorName", eiInfo.get("userName"));
                // 修改时间
                hashMap.put("recReviseTime", DateUtil.curDateTimeStr14());
                dao.update("LIRL0502.updateShowFlag", hashMap);
            }
            eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg(e.getMessage());
            return eiInfo;
        }
        return eiInfo;
    }

    /***
     * PDA找料查询车牌信息
     *  S_LI_RL_0085
     */
    public EiInfo queryVehicleInfo(EiInfo eiInfo){

        String segNo = (String) eiInfo.get("segNo");
        if (StringUtils.isBlank(segNo)){
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg("账套为空！");
            return eiInfo;
        }
        HashMap<Object, Object> hashMap = new HashMap<>();
        hashMap.put("segNo",segNo);
        hashMap.put("allocType","10");
        hashMap.put("status","20");
        List<LIRL0502> queryLIRL0502 = this.dao.query("LIRL0502.queryVehicleInfo", hashMap);
        eiInfo.set("list",queryLIRL0502);
        eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
        return eiInfo;
    }


    /***
     * PDA找料
     * S_LI_RL_0074
     */
    public EiInfo findMaterial(EiInfo eiInfo){

        EiInfo outInfo = new EiInfo();
        try {
            String segNo = (String) eiInfo.get("segNo"); //系统账套
            String vehicleNo = (String) eiInfo.get("vehicleNo"); //车牌号
            String voucherNum = (String) eiInfo.get("voucherNum"); //提单号
            String allocateVehicleNo = (String) eiInfo.get("allocateVehicleNo");//配车单号
            if (StringUtils.isBlank(segNo) ) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("账套不能为空！");
                throw new Exception("账套不能为空！");
            }

            try {
                outInfo = validToken(eiInfo);
                if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                    outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                    return outInfo;
                }
            } catch (Exception e) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("token无效！");
                return outInfo;
            }

            //查询车牌号下的所有提单捆包明细
            Map<String, String> hashMap = new HashMap<>();
            hashMap.put("segNo",segNo);
            hashMap.put("vehicleNo",vehicleNo);
            hashMap.put("voucherNum",voucherNum);
            hashMap.put("allocateVehicleNo",allocateVehicleNo);
            List<Map> queryLIRL0503 = this.dao.query(LIRL0503.QUERY_PACK_INFO, hashMap);
            eiInfo.set("list",queryLIRL0503);
            eiInfo.setStatus(EiConstant.STATUS_SUCCESS);

        } catch (Exception e) {
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg(e.getMessage());
            return eiInfo;
        }
        return eiInfo;
    }

    /***
     * 重庆PDA根据库位查询库位捆包示意图
     * S_LI_RL_0148
     */
    public EiInfo findMaterialByLocation(EiInfo eiInfo){

        EiInfo outInfo = new EiInfo();
        try {
            String segNo = (String) eiInfo.get("segNo"); //系统账套
            String locationId = (String) eiInfo.get("locationId"); //库位代码
            if (StringUtils.isBlank(segNo) ) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("账套不能为空！");
                throw new Exception("账套不能为空！");
            }

            try {
                outInfo = validToken(eiInfo);
                if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                    outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                    return outInfo;
                }
            } catch (Exception e) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("token无效！");
                return outInfo;
            }

            //查询车牌号下的所有提单捆包明细
            Map<String, String> hashMap = new HashMap<>();
            hashMap.put("segNo",segNo);
            hashMap.put("areaCode",locationId);
            hashMap.put("positionSeq","X");//坐标轴方位，待定，先取X顺序
            List<Map> queryLIDS0901 = this.dao.query("queryMatByLoaction", hashMap);
            String posDirCode ="0";
            int j =0 ;
            List<Map> returnResultList = new ArrayList();
            for (int i = 0;i<queryLIDS0901.size(); i++ )
            {   //循环获取层数标记，加上序号
                Map lids0901Map = queryLIDS0901.get(i);
                if (lids0901Map.get("posDirCode").equals(posDirCode))
                {
                    j++;
                }else{
                    //赋值层数，序号变成1
                    posDirCode = lids0901Map.get("posDirCode").toString();
                    j = 1;
                }
                lids0901Map.put("seqNum",j);
                returnResultList.add(lids0901Map);
            }
            eiInfo.set("list",returnResultList);
            eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
            eiInfo.setMsg("查询成功！");
        } catch (Exception e) {
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg(e.getMessage());
            return eiInfo;
        }
        return eiInfo;
    }


    /***
     * S_LI_RL_0110
     * 重庆，启动叫号
     */
    public EiInfo callNumber(EiInfo eiInfo) {
        // 参数提取和验证
        String segNo = (String) eiInfo.get("segNo");
        String handPointId = (String) eiInfo.get("handPointId");
        String vehicleNo = (String) eiInfo.get("vehicleNo");
        String carTraceNo = (String) eiInfo.get("carTraceNo");
        String driverName = (String) eiInfo.get("driverName");
        String driverTel = (String) eiInfo.get("driverTel");
        String recRevisor = (String) eiInfo.get("recRevisor");
        String recCreator = (String) eiInfo.get("recCreator");
        String recCreatorName = (String) eiInfo.get("recCreatorName");
        String recRevisorName = (String) eiInfo.get("recRevisorName");
        String factoryArea = (String) eiInfo.get("factoryArea");
        String allocateVehicleNo1 = (String) eiInfo.get("allocateVehicleNo");

        // 参数验证
        if (StringUtils.isBlank(segNo) || StringUtils.isBlank(handPointId) || StringUtils.isBlank(vehicleNo)) {
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg("账套、装卸点、车牌号不能为空！");
            return eiInfo;
        }

        String key = vehicleNo + "_" + carTraceNo;
        Object lock = lockMap.computeIfAbsent(key, k -> new Object());
        synchronized (lock) {
            try {
                // 使用单个查询获取所有必要的车辆信息
                Map<String, Object> vehicleInfoQuery = new HashMap<>();
                vehicleInfoQuery.put("segNo", segNo);
                vehicleInfoQuery.put("carTraceNo", carTraceNo);
                vehicleInfoQuery.put("vehicleNo", vehicleNo);
                vehicleInfoQuery.put("handPointId", handPointId);

                // 通过一个综合查询获取车辆跟踪信息、叫号记录和排队信息
                List<HashMap> vehicleInfoList = dao.query(LIRL0402.QUERY_VEHICLE_COMPLETE_INFO, vehicleInfoQuery);

                if (CollectionUtils.isEmpty(vehicleInfoList)) {
                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                    eiInfo.setMsg("车辆跟踪数据为空,不能叫号！");
                    return eiInfo;
                }

                HashMap vehicleInfo = vehicleInfoList.get(0);
                Integer callCount = MapUtils.getInteger(vehicleInfo, "callCount", 0);
                driverTel = MapUtils.getString(vehicleInfo, "telNum");
                driverName = MapUtils.getString(vehicleInfo, "driverName");

//            // 检查叫号次数限制
//            if (callCount >= 3) {
//                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
//                eiInfo.setMsg("该车辆" + vehicleNo + "只能叫号3次,不能再次叫号!");
//                return eiInfo;
//            }
                //判断此车辆是否被叫号
                HashMap<String, Object> stringObjectHashMap = new HashMap<>();
                stringObjectHashMap.put("segNo", segNo);
                stringObjectHashMap.put("carTraceNo", carTraceNo);
                stringObjectHashMap.put("vehicleNo", vehicleNo);
                List<LIRL0402> query = this.dao.query(LIRL0402.QUERY, stringObjectHashMap);
                // 如果已经叫过号，只更新次数和发送短信
                if (callCount > 0|| query.size()>0) {
                    updateCallCount(segNo, carTraceNo, vehicleNo, allocateVehicleNo1, handPointId,
                            callCount + 1, recRevisor, recRevisorName);

                    String handPointName = MapUtils.getString(vehicleInfo, "handPointName", "");
                    sendDriverNotification(driverTel, driverName, vehicleNo, handPointName);

                    eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
                    eiInfo.setMsg("叫号成功！");
                    return eiInfo;
                }

                // 首次叫号处理
                return processFirstTimeCall(eiInfo, segNo, handPointId, vehicleNo, carTraceNo, driverName,
                        driverTel, recRevisor, recCreator, recCreatorName, recRevisorName,
                        factoryArea, allocateVehicleNo1, vehicleInfo);

            } catch (Exception e) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg(e.getMessage());
                return eiInfo;
            }finally {
                //释放锁
                lockMap.remove(key);
            }
        }
    }

    private EiInfo processFirstTimeCall(EiInfo eiInfo, String segNo, String handPointId, String vehicleNo,
                                        String carTraceNo, String driverName, String driverTel, String recRevisor,
                                        String recCreator, String recCreatorName, String recRevisorName,
                                        String factoryArea, String allocateVehicleNo1, HashMap vehicleInfo) throws Exception {

        // 检查装卸点是否有其他作业中的车辆
        int workingVehicleCount = MapUtils.getInteger(vehicleInfo, "workingVehicleCount", 0);
        HashMap<Object, Object> hashMap = new HashMap<>();
        hashMap.put("segNo",segNo);
        hashMap.put("handPointId",handPointId);
        hashMap.put("carTraceNo",carTraceNo);
        List<LIRL0301> query = this.dao.query(LIRL0301.QUERY_HAND_POINT_EXZIST, hashMap);
        if (CollectionUtils.isNotEmpty(query)){
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg("当前装卸点下只允许有一辆作业中的车辆！");
            return eiInfo;
        }

        // 检查车辆是否正在作业
        hashMap.put("handPointId","");
        List<LIRL0301> query1 = this.dao.query(LIRL0301.QUERY, hashMap);
        if (CollectionUtils.isNotEmpty(query1)){
            String status = query1.get(0).getStatus();
            if ("30".equals(status)){
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("当前车辆正在作业，不允许再次启动叫号！");
                return eiInfo;
            }
        }
        // 处理F1厂区的配单逻辑
        if ("F1".equals(factoryArea)) {
            processF1FactoryAllocation(segNo, handPointId, vehicleNo, carTraceNo, allocateVehicleNo1);
        }

//        // 处理打印逻辑
//        processPrintingLogic(segNo, vehicleNo, carTraceNo, factoryArea);
//
        // 处理特殊装卸点逻辑（2号门倒车进入）
        String alternativeMessage = processSpecialHandlingPoint(segNo, handPointId, driverName, vehicleNo);

//        try {
            // 执行叫号主要逻辑
            executeCallNumberMainLogic(segNo, handPointId, vehicleNo, carTraceNo, allocateVehicleNo1,
                    recCreator, recCreatorName, recRevisor, recRevisorName, vehicleInfo,factoryArea);
//        } catch (DuplicateKeyException e) {
//            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
//            eiInfo.setMsg("叫号成功！");
//            return eiInfo;
//        }

        // 发送短信通知
        String finalMessage = StringUtils.isNotBlank(alternativeMessage) ? alternativeMessage :
                buildStandardMessage(driverName, vehicleNo, MapUtils.getString(vehicleInfo, "handPointName", ""));
        sendDriverNotification(driverTel, driverName, vehicleNo, MapUtils.getString(vehicleInfo, "handPointName", ""));

        eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
        eiInfo.setMsg("叫号成功！");
        return eiInfo;
    }

    private void updateCallCount(String segNo, String carTraceNo, String vehicleNo, String allocateVehicleNo1,
                                 String handPointId, int newCallCount, String recRevisor, String recRevisorName) {
        Map<String, Object> updateParams = new HashMap<>();
        updateParams.put("segNo", segNo);
        updateParams.put("carTraceNo", carTraceNo);
        updateParams.put("vehicleNo", vehicleNo);
        updateParams.put("voucherNum", allocateVehicleNo1);
        updateParams.put("handPointId", handPointId);
        updateParams.put("callCount", newCallCount);
        updateParams.put("recRevisor", recRevisor);
        updateParams.put("recRevisorName", recRevisorName);
        updateParams.put("recReviseTime", DateUtil.curDateTimeStr14());

        this.dao.update(LIRL0402.UPDATE_CALL_COUNT, updateParams);
    }

    private void processF1FactoryAllocation(String segNo, String handPointId, String vehicleNo,
                                            String carTraceNo, String allocateVehicleNo1) throws Exception {
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("segNo", segNo);
        queryParams.put("handPointId", handPointId);
        queryParams.put("vehicleNo", vehicleNo);
        queryParams.put("carTraceNo", carTraceNo);

        List<LIRL0502> lirl0502List = this.dao.query(LIRL0502.QUERY, queryParams);
        if (CollectionUtils.isNotEmpty(lirl0502List)) {
            LIRL0502 lirl0502 = lirl0502List.get(0);
            String proformaVoucherNum = lirl0502.getProformaVoucherNum();

            if (StringUtils.isBlank(proformaVoucherNum)) {
                String allocType = lirl0502.getAllocType();
                String allocateVehicleNo = lirl0502.getAllocateVehicleNo();

                // 检查是否有提单
                Map<String, Object> billQuery = new HashMap<>();
                billQuery.put("segNo", segNo);
                billQuery.put("allocateVehicleNo", allocateVehicleNo);
                List<LIRL0503> lirl0503List = this.dao.query(LIRL0503.QUERY, billQuery);

                if (CollectionUtils.isNotEmpty(lirl0503List)) {
                    generateCraneOrder(segNo, handPointId, allocateVehicleNo, allocType);
                }
            }
        }
    }

    private void generateCraneOrder(String segNo, String handPointId, String allocateVehicleNo, String allocType) throws Exception {
        EiInfo orderInfo = new EiInfo();
        orderInfo.set("segNo", segNo);
        orderInfo.set("loadingPointNo", handPointId);
        orderInfo.set("allocateVehicleNo", allocateVehicleNo);

        if ("10".equals(allocType)) {
            orderInfo.set(EiConstant.serviceName, "LIDSInterfaces");
            orderInfo.set(EiConstant.methodName, "autoGenerateCraneOrder");
        } else if ("20".equals(allocType)) {
            orderInfo.set(EiConstant.serviceName, "LIDS1101");
            orderInfo.set(EiConstant.methodName, "WarehousingCreateCraneOrderId");
        } else {
            throw new Exception("配单类型错误！");
        }

        EiInfo result = XLocalManager.call(orderInfo);
        if (result.getStatus() < 0) {
            throw new Exception("生成行车作业清单失败: " + result.getMsg());
        }
    }

    private void processPrintingLogic(String segNo, String vehicleNo, String carTraceNo, String factoryArea) {
        // 查询车辆装卸记录
        Map<String, String> vehicleRecordQuery = new HashMap<>();
        vehicleRecordQuery.put("segNo", segNo);
        vehicleRecordQuery.put("vehicleNo", vehicleNo);
        vehicleRecordQuery.put("carTraceNo", carTraceNo);

        List<LIRL0406> lirl0406List = this.dao.query(LIRL0406.QUERY, vehicleRecordQuery);

        if (CollectionUtils.isEmpty(lirl0406List)) {
            // 首次装卸点，打印本厂区捆包明细
            printBundleDetails(segNo, carTraceNo, factoryArea);
        } else {
            // 检查是否到达新厂区
            for (LIRL0406 record : lirl0406List) {
                if (ObjectUtils.notEqual(factoryArea, record.getFactoryArea())) {
                    printBundleDetails(segNo, carTraceNo, factoryArea);
                    break;
                }
            }
        }
    }

    private void printBundleDetails(String segNo, String carTraceNo, String factoryArea) {
        Map<String, Object> bundleQuery = new HashMap<>();
        bundleQuery.put("segNo", segNo);
        bundleQuery.put("carTraceNo", carTraceNo);
        bundleQuery.put("factoryArea", factoryArea);

        List<LIRL0503> bundleList = this.dao.query(LIRL0503.QUERY, bundleQuery);
        // TODO: 实际打印逻辑
    }

    private String processSpecialHandlingPoint(String segNo, String handPointId, String driverName, String vehicleNo) {
        // 查询装卸点名称（使用缓存优化）
        Map<String, String> handPointQuery = new HashMap<>();
        handPointQuery.put("segNo", segNo);
        handPointQuery.put("handPointId", handPointId);
        handPointQuery.put("status", "30");

        List<LIRL0304> handPointList = this.dao.query(LIRL0304.QUERY, handPointQuery);
        if (CollectionUtils.isEmpty(handPointList)) {
            return null;
        }

        String handPointName = MapUtils.getString(handPointList.get(0).toMap(), "handPointName");
        if (!"2号门".equals(handPointName)) {
            return null;
        }

        // 检查3号门和1号门的作业状态
        boolean canUseAlternative = checkAlternativeRoute(segNo);
        if (canUseAlternative) {
            return "尊敬的司机" + driverName + "您好，请到6号门装卸点倒车进入进行装卸货，谢谢。";
        }

        return null;
    }

    private boolean checkAlternativeRoute(String segNo) {
        // 批量查询3号门和1号门的作业状态
        Map<String, Object> batchQuery = new HashMap<>();
        batchQuery.put("segNo", segNo);
        batchQuery.put("handPointNames", java.util.Arrays.asList("3号门", "1号门"));
        batchQuery.put("status", "30");

        List<Map> workingPoints = this.dao.query("LIRL0304.QUERY_WORKING_POINTS_BATCH", batchQuery);

        boolean gate3Working = workingPoints.stream().anyMatch(p -> "3号门".equals(p.get("handPointName")));
        boolean gate1Working = workingPoints.stream().anyMatch(p -> "1号门".equals(p.get("handPointName")));

        return gate3Working && !gate1Working;
    }

    private void executeCallNumberMainLogic(String segNo, String handPointId, String vehicleNo, String carTraceNo,
                                            String allocateVehicleNo1, String recCreator, String recCreatorName,
                                            String recRevisor, String recRevisorName, HashMap vehicleInfo,String factoryArea) throws Exception {

        String currentTime = DateUtil.curDateTimeStr14();

        // 获取最大排队号
        Integer maxQueueNumber = MapUtils.getInteger(vehicleInfo, "maxQueueNumber", 0);

        // 批量执行数据库操作
        List<Map<String, Object>> batchOperations = new ArrayList<>();


        // 1. 插入叫号记录
        Map<String, Object> callRecord = new HashMap<>();
        callRecord.put("uuid", UUIDUtils.getUUID());
        callRecord.put("segNo", segNo);
        callRecord.put("unitCode", segNo);
        callRecord.put("queueNumber", maxQueueNumber + 1);
        callRecord.put("carTraceNo", carTraceNo);
        callRecord.put("vehicleNo", vehicleNo);
        callRecord.put("voucherNum", allocateVehicleNo1);
        callRecord.put("priorityLevel", "99");
        callRecord.put("queueDate", currentTime);
        callRecord.put("targetHandPointId", handPointId);
        callRecord.put("factoryArea",factoryArea);
        callRecord.put("remark", currentTime + ":排队叫号");
        callRecord.put("sysRemark", currentTime + ":排队叫号");
        callRecord.put("callCount", 1);
        callRecord.put("recCreator", recCreator);
        callRecord.put("recCreatorName", recCreatorName);
        callRecord.put("recCreateTime", currentTime);
        callRecord.put("recRevisor", recRevisor);
        callRecord.put("recRevisorName", recRevisorName);
        callRecord.put("recReviseTime", currentTime);
        callRecord.put("archiveFlag", "0");
        callRecord.put("tenantId", " ");
        callRecord.put("delFlag", "0");

        this.dao.insert(LIRL0402.INSERT, callRecord);


        // 2. 更新车辆跟踪状态
        Map<String, Object> trackingUpdate = new HashMap<>();
        trackingUpdate.put("segNo", segNo);
        trackingUpdate.put("carTraceNo", carTraceNo);
        trackingUpdate.put("status", "25");
        trackingUpdate.put("targetHandPointId", handPointId);
        trackingUpdate.put("allocateVehicleNo", allocateVehicleNo1);
        trackingUpdate.put("recRevisor", recRevisor);
        trackingUpdate.put("recRevisorName", recRevisorName);
        trackingUpdate.put("recReviseTime", currentTime);
        trackingUpdate.put("factoryArea", factoryArea);
        trackingUpdate.put("callTime", currentTime);
        this.dao.update(LIRL0301.UPDATE_TARGET_HAND_POINT, trackingUpdate);

        // 3. 备份并删除排队记录
        backupAndDeleteQueueRecords(segNo, carTraceNo, vehicleNo, currentTime);
    }

    private void backupAndDeleteQueueRecords(String segNo, String carTraceNo, String vehicleNo, String currentTime) {
        Map<String, String> queueQuery = new HashMap<>();
        queueQuery.put("segNo", segNo);
        queueQuery.put("carTraceNo", carTraceNo);
        queueQuery.put("vehicleNo", vehicleNo);

        List<LIRL0401> queueRecords = this.dao.query(LIRL0401.QUERY_INFO, queueQuery);
        if (CollectionUtils.isNotEmpty(queueRecords)) {
            // 批量备份
            List<Map<String, Object>> backupRecords = new ArrayList<>();
            for (LIRL0401 record : queueRecords) {
                Map<String, Object> backupRecord = record.toMap();
                backupRecord.put("uuid", UUIDUtils.getUUID());
                backupRecord.put("backDate", currentTime);
                backupRecords.add(backupRecord);
            }

            // 使用批量插入
            if (!backupRecords.isEmpty()) {
                for (Map<String, Object> backup : backupRecords) {
                    this.dao.insert(LIRL0410.INSERT, backup);
                }
            }

            // 删除原记录
            this.dao.delete(LIRL0401.DELETE, queueQuery);
        }
    }

    private void sendDriverNotification(String driverTel, String driverName, String vehicleNo, String handPointName) {
        try {
            HashMap<String, Object> messageParams = new HashMap<>();
            messageParams.put("param1", driverTel);
            messageParams.put("param2", driverName);
            messageParams.put("param3", vehicleNo);
            messageParams.put("param4", handPointName);
            MessageUtils.sendMessage(messageParams, "MT0000001012");
        } catch (Exception e) {
            // 短信发送失败不影响主流程
            logger.warn("短信发送失败: " + e.getMessage());
        }
    }

    private String buildStandardMessage(String driverName, String vehicleNo, String handPointName) {
        return "尊敬的司机" + driverName + "您好，请到" + handPointName + "装卸点装卸货，谢谢。";
    }

    /***
     * PDA取消叫号
     * 	S_LI_RL_0078
     */
    public EiInfo cancelCallNumber(EiInfo eiInfo) {
        String segNo = (String) eiInfo.get("segNo"); //账套
        String targetHandPointId = (String) eiInfo.get("handPointId");//装卸点
        String carTraceNo = (String) eiInfo.get("carTraceNo");//车辆跟踪号
        String vehicleNo = (String) eiInfo.get("vehicleNo");//车牌号
        String userId = (String) eiInfo.get("userId");//登录人账号
        String recRevisor = (String) eiInfo.get("recRevisor"); //修改人工号
        String recCreator = (String) eiInfo.get("recCreator"); //创建人工号
        String recCreatorName = (String) eiInfo.get("recCreatorName"); //创建人姓名
        String recRevisorName = (String) eiInfo.get("recRevisorName"); //修改人姓名
        if (StringUtils.isEmpty(targetHandPointId)|| StringUtils.isEmpty(carTraceNo)|| StringUtils.isEmpty(segNo)){
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg("账套、装卸点、车辆跟踪、车牌号号为空！");
            return eiInfo;
        }

        try {
            //查询叫号表
            Map<String, Object> hashMap = new HashMap<>();
            hashMap.put("segNo", segNo);
            hashMap.put("targetHandPointId", targetHandPointId);
            hashMap.put("carTraceNo", carTraceNo);
            hashMap.put("vehicleNo", vehicleNo);
            // hashMap.put("callCount", "1");
            hashMap.put("userId", userId);
            List<LIRL0402> queryLIRL0402 = this.dao.query(LIRL0402.QUERY, hashMap);
            if (CollectionUtils.isEmpty((queryLIRL0402))){
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("该车辆没有叫号！");
                return eiInfo;
            }
            hashMap.put("perNo", userId);
            //判断PDA操作人是行车工还是仓库管理人员
            List<LIRL0504> queryLIRL0504 = this.dao.query(LIRL0504.QUERY, hashMap);
            if (CollectionUtils.isEmpty((queryLIRL0504))){
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("该操作人员不是行车工，也不是仓库管理员不能取消叫号！");
                throw new Exception("该操作人员不是行车工，也不是仓库管理员不能取消叫号！");
            }
            Map<String, Object> hashMap1 = new HashMap<>();
            hashMap1.put("segNo", segNo);
//            hashMap1.put("targetHandPointId", targetHandPointId);
            hashMap1.put("carTraceNo", carTraceNo);
            hashMap1.put("vehicleNo", vehicleNo);
            //判断当前状态是否可以取消叫号
            List<LIRL0301> query1 = this.dao.query(LIRL0402.QUERY, hashMap1);
            if (CollectionUtils.isEmpty((query1))){
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("叫号车辆不存在,不能取消叫号！");
                return eiInfo;
            }
//            for (LIRL0504 lirl0504 : queryLIRL0504) {
                String perType = queryLIRL0504.get(0).getPerType();//2：行车工，3：仓库管理员
                for (LIRL0402 lirl0402 : queryLIRL0402) {
                    Map map = lirl0402.toMap();
                    String callCount = lirl0402.getCallCount();
                    String recCreateTime = lirl0402.getRecCreateTime();//第一次叫号时间
                    Integer queueNumber = 0;

                    if ("1".equals(callCount)){
                        //如果当前时间<=第一次叫号时间+10分钟
                        long curTime = Long.parseLong(DateUtil.curDateTimeStr14());
                        if (curTime <= (Long.parseLong(recCreateTime) + 600)) {
                            if (!"3".equals(perType)){
                                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                                eiInfo.setMsg("开始叫号不在十分钟之内，只能由仓库管理员进行取消叫号！");
                                return eiInfo;
                            }
                            //直接取消叫号
                            //删除叫号表，插入排队表，更新车辆跟踪表状态
                            //查询次装卸点最大顺序号
                            HashMap<Object, Object> hashMapLirl0401 = new HashMap<>();
                            hashMapLirl0401.put("segNo",segNo);
                            List<String> query = this.dao.query(LIRL0401.QUERY_QUEUE_NUMBER, hashMapLirl0401);
                            if (CollectionUtils.isEmpty(query)){
                                queueNumber=0;
                            }else {
                                queueNumber = Integer.parseInt(query.get(0));
                            }
                            this.dao.delete(LIRL0402.DELETE, map);
                            map.put("recCreator",recCreator);
                            map.put("recCreatorName",recCreatorName);
                            map.put("recCreateTime",DateUtil.curDateTimeStr14());
                            map.put("recRevisor",recRevisor);
                            map.put("recRevisorName",recRevisorName);
                            map.put("recReviseTime",DateUtil.curDateTimeStr14());
                            map.put("status","20");
                            map.put("queueNumber",queueNumber+1);//顺序号
                            this.dao.insert(LIRL0401.INSERT, map);

                            map.put("targetHandPointId"," ");
                            map.put("carTraceNo",carTraceNo);
                            this.dao.update(LIRL0301.UPDATE_TARGET_HAND_POINT, map);
//                            //车辆跟踪状态赋值20进厂
//                            this.dao.update(LIRL0301.UPDATE, map);
                        }else {
                            //第一次叫号超时，车辆顺序后延续一位
                            //删除叫号表，插入排队表，更新车辆跟踪表状态
                            //查询次装卸点最大顺序号
                            HashMap<Object, Object> hashMapLirl0401 = new HashMap<>();
                            hashMapLirl0401.put("segNo",segNo);
                            hashMapLirl0401.put("vehicleNo",vehicleNo);
                            List<String> query = this.dao.query(LIRL0401.QUERY_QUEUE_NUMBER, hashMapLirl0401);
                            if (CollectionUtils.isEmpty(query)){
                                queueNumber=0;
                            }else {
                                queueNumber = Integer.parseInt(query.get(0));
                            }
                            this.dao.delete(LIRL0402.DELETE, map);
                            map.put("recCreator",recCreator);
                            map.put("recCreatorName",recCreatorName);
                            map.put("recCreateTime",DateUtil.curDateTimeStr14());
                            map.put("recRevisor",recRevisor);
                            map.put("recRevisorName",recRevisorName);
                            map.put("recReviseTime",DateUtil.curDateTimeStr14());
                            map.put("status","20");
                            map.put("queueNumber",queueNumber+1);
                            this.dao.insert(LIRL0401.INSERT, map);
                            //车辆跟踪状态赋值20进厂
                            map.put("targetHandPointId"," ");
                            map.put("carTraceNo",carTraceNo);
                            this.dao.update(LIRL0301.UPDATE_TARGET_HAND_POINT, map);
                            this.dao.delete(LIRL0402.DELETE, map);
//                            //车辆跟踪状态赋值20进厂
//                            this.dao.update(LIRL0301.UPDATE, hashMap);
                        }
                    }else if ("2".equals(callCount) || "3".equals(callCount)){
                        long curTime = Long.parseLong(DateUtil.curDateTimeStr14());
                        if (curTime <= (Long.parseLong(recCreateTime) + 600)) {
                            if (!"3".equals(perType)){
                                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                                eiInfo.setMsg("开始叫号不在十分钟之内，只能由仓库管理员进行取消叫号！");
                                return eiInfo;
                            }
                        }
                        // 第二次叫号超时，放到此装卸点的最后一位
                        //删除叫号表，插入排队表，更新车辆跟踪表状态
                        //查询次装卸点最大顺序号
                        HashMap<Object, Object> hashMapLirl0401 = new HashMap<>();
                        hashMapLirl0401.put("segNo",segNo);
                        hashMapLirl0401.put("handPointId",targetHandPointId);
                        List<String> query = this.dao.query(LIRL0401.QUERY_QUEUE_NUMBER, hashMapLirl0401);
                        if (CollectionUtils.isEmpty(query)){
                            queueNumber=0;
                        }else {
                            queueNumber = Integer.parseInt(query.get(0));
                        }
                        this.dao.delete(LIRL0402.DELETE, map);
                        map.put("recCreator",recCreator);
                        map.put("recCreatorName",recCreatorName);
                        map.put("recCreateTime",DateUtil.curDateTimeStr14());
                        map.put("recRevisor",recRevisor);
                        map.put("recRevisorName",recRevisorName);
                        map.put("recReviseTime",DateUtil.curDateTimeStr14());
                        map.put("status","20");
                        map.put("queueNumber",queueNumber+1);
                        this.dao.insert(LIRL0401.INSERT, map);
//                        //车辆跟踪状态赋值20进厂
                        map.put("targetHandPointId"," ");
                        map.put("carTraceNo",carTraceNo);
                        this.dao.update(LIRL0301.UPDATE_TARGET_HAND_POINT, map);
                    }
                }
//            }
            eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
            eiInfo.setMsg("取消叫号成功！");
        }
        catch (Exception e) {
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg(e.getMessage());
            return eiInfo;
        }
        return eiInfo;
    }


    /***
     * 更改叫号装卸点
     *	S_LI_RL_0079
     */
    public EiInfo updateHandPointIdByPda(EiInfo eiInfo) {
        String segNo = (String) eiInfo.get("segNo"); //账套
        String targetHandPointId = (String) eiInfo.get("handPointId");//更改装卸点
        String oldHandPointId = (String) eiInfo.get("oldHandPointId");//原始装卸点
        String carTraceNo = (String) eiInfo.get("carTraceNo");//车辆跟踪号
        String factoryArea = (String) eiInfo.get("factoryArea");//厂区
        String vehicleNo = (String) eiInfo.get("vehicleNo");//车牌号
        String userId = (String) eiInfo.get("userId");//登录人账号
        String recRevisor = (String) eiInfo.get("recRevisor"); //修改人工号
        String recCreator = (String) eiInfo.get("recCreator"); //创建人工号
        String recCreatorName = (String) eiInfo.get("recCreatorName"); //创建人姓名
        String recRevisorName = (String) eiInfo.get("recRevisorName"); //修改人姓名
        if (StringUtils.isEmpty(targetHandPointId)|| StringUtils.isEmpty(carTraceNo)|| StringUtils.isEmpty(segNo)){
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg("账套、装卸点、车辆跟踪、车牌号号为空！");
            return eiInfo;
        }

        try {
            //更改装卸点
            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("segNo",segNo);
            hashMap.put("carTraceNo",carTraceNo);
            hashMap.put("targetHandPointId",targetHandPointId);
            hashMap.put("factoryArea",factoryArea);
            hashMap.put("vehicleNo",vehicleNo);
            hashMap.put("recCreator",recCreator);
            hashMap.put("recCreatorName",recCreatorName);
            hashMap.put("recCreateTime",DateUtil.curDateTimeStr14());
            hashMap.put("recRevisor",recRevisor);
            hashMap.put("recRevisorName",recRevisorName);
            hashMap.put("recReviseTime",DateUtil.curDateTimeStr14());
            hashMap.put("oldHandPointId",oldHandPointId);

            //先判断是否是同一厂区装卸点
            List<LIRL0301> queryLIRL0301 = this.dao.query(LIRL0301.QUERY, hashMap);
            if (CollectionUtils.isEmpty(queryLIRL0301)){
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("车辆跟踪数据不存在！");
                return eiInfo;
            }

            Map<String, Object> hashMap1 = new HashMap<>();
            hashMap1.put("segNo", segNo);
//            hashMap1.put("targetHandPointId", targetHandPointId);
            hashMap1.put("carTraceNo", carTraceNo);
            hashMap1.put("vehicleNo", vehicleNo);
            hashMap1.put("status", "30");
            //判断当前状态是否可以取消叫号
            List query1 = this.dao.query(LIRL0301.QUERY, hashMap1);
            if (CollectionUtils.isNotEmpty((query1))){
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("当前车辆状态为开始装卸货，不允许更改装卸点！");
                return eiInfo;
            }

            LIRL0301 lirl0301 = queryLIRL0301.get(0);
            String handPointId = lirl0301.getTargetHandPointId();//目标装卸点
            String allocateVehicleNo = lirl0301.getAllocateVehicleNo();//目标装卸点
            List<String> listAll = new ArrayList<>();
            listAll.add(handPointId);
            listAll.add(targetHandPointId);//修改的装卸点
            Map<String, Object> hashMapLIRL0304 = new HashMap<>();
            hashMapLIRL0304.put("segNo",segNo);
            hashMapLIRL0304.put("handPointIdList",listAll);
            hashMapLIRL0304.put("status","30");
            //查询装卸点表中的厂区
            List<LIRL0304> queryLIRL0304 = this.dao.query(LIRL0304.QUERY, hashMapLIRL0304);
            if (CollectionUtils.isEmpty(queryLIRL0304)){
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("该装卸点不存在！");
            }
//            for (LIRL0304 lirl0304 : queryLIRL0304) {
//                String factoryAreaNew = lirl0304.getFactoryBuilding();
//                if (ObjectUtils.notEqual(factoryAreaNew,factoryArea)){
//                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
//                    eiInfo.setMsg("更换的装卸点不是该厂区的装卸点！");
//                    return eiInfo;
//                }
//            }
            String factory = "";
            if (StringUtils.isNotBlank(targetHandPointId)){
                Map queryLIRL03011 = new HashMap();
                queryLIRL03011.put("segNo",segNo);
                queryLIRL03011.put("handPointId",targetHandPointId);
                queryLIRL03011.put("status","30");
                List<LIRL0304> queryLIRL03041 = this.dao.query(LIRL0304.QUERY, queryLIRL03011);
                if (CollectionUtils.isEmpty(queryLIRL03041)){
                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                    eiInfo.setMsg("装卸点没有厂区！");
                    return eiInfo;
                }
                factory=queryLIRL03041.get(0).getFactoryBuilding();
            }

            //判断是否是排队中的车
            HashMap<String, Object> stringObjectHashMap = new HashMap<>();
            stringObjectHashMap.put("segNo",segNo);
            stringObjectHashMap.put("carTraceNo",carTraceNo);
            stringObjectHashMap.put("vehicleNo",vehicleNo);
            stringObjectHashMap.put("targetHandPointId",oldHandPointId);
            List<HashMap> query = this.dao.query(LIRL0402.QUERY_INFO, stringObjectHashMap);
            if (CollectionUtils.isNotEmpty(query)){
                //存在叫号中
                HashMap map = query.get(0);
                map.put("targetHandPointId",targetHandPointId);
                map.put("recCreator",recCreator);
                map.put("recCreatorName",recCreatorName);
                map.put("recCreateTime",DateUtil.curDateTimeStr14());
                map.put("recRevisor",recRevisor);
                map.put("recRevisorName",recRevisorName);
                map.put("recReviseTime",DateUtil.curDateTimeStr14());
                map.put("queueDate",DateUtil.curDateTimeStr14());
                map.put("factory",factory);
                this.dao.insert(LIRL0401.INSERT, map);

                //删除老的
                HashMap<String, Object> hashMap0402 = new HashMap<>();
                hashMap0402.put("segNo",segNo);
                hashMap0402.put("carTraceNo",carTraceNo);
                hashMap0402.put("vehicleNo",vehicleNo);
                hashMap0402.put("targetHandPointIdNEq",oldHandPointId);
                this.dao.delete(LIRL0402.DELETE, hashMap0402);
            }

            hashMap.put("factoryArea",factory);
            //修改车辆跟踪中的装卸点
            this.dao.update(LIRL0301.UPDATE_TARGET_HAND_POINT, hashMap);
            //修改0402车辆叫号表的装卸点
            this.dao.update("LIRL0301.updateTargetHandPoint0402", hashMap);
            //修改0503配单表的装卸点
//            this.dao.update("LIRL0503.updateTargetHandPointId", hashMap);

            hashMap.put("oldHandPointId",oldHandPointId);
            hashMap.put("allocateVehicleNo",StringUtils.isNotEmpty(allocateVehicleNo)?allocateVehicleNo:"");
            //更新排队表
            hashMap.put("remark","更改装卸点，原始装卸点："+oldHandPointId+"变更装卸点："+targetHandPointId);
            this.dao.update(LIRL0401.UPDATE_HAND_POINT_ID_C_Q, hashMap);

            //更新叫号表
            this.dao.update(LIRL0402.UPDATE_HAND_POINT_ID_C_Q, hashMap);

            //删除老的
            HashMap<String, Object> hashMap0402 = new HashMap<>();
            hashMap0402.put("segNo",segNo);
            hashMap0402.put("carTraceNo",carTraceNo);
            hashMap0402.put("vehicleNo",vehicleNo);
            hashMap0402.put("targetHandPointIdNEq",oldHandPointId);
            this.dao.delete(LIRL0402.DELETE, hashMap0402);

            eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
            eiInfo.setMsg("修改装卸点成功！");
        } catch (Exception e) {
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg(e.getMessage());
            return eiInfo;
        }
        return eiInfo;
    }

    /***
     * PDA 车辆开始装卸货
     * prog.wl_vehicle_plan_mgr.vehicle_load_good
     *
     *0080
     */
    public EiInfo vehicleLoadGoodCQ(EiInfo eiInfo) {

        try {
            String stringDate = DateUtil.curDateTimeStr14();//修改时间

            //获取车辆跟踪号,车牌号,账套
            String segNo = (String) eiInfo.get("segNo");//账套
            if (null == eiInfo.get("segNo") || StringUtils.isBlank(eiInfo.get("segNo").toString())) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("业务单元代码为空，请重试！");
                return eiInfo;
            }

            String carTraceNo = (String) eiInfo.get("carTraceNo"); //车辆跟踪单号
            if (null == eiInfo.get("carTraceNo") || StringUtils.isBlank(eiInfo.get("carTraceNo").toString())) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("车辆跟踪单号为空，请重试！");
                return eiInfo;
            }
            String vehicleNo = eiInfo.getString("vehicleNo");//车牌号
            if (null == eiInfo.get("vehicleNo") || StringUtils.isBlank(eiInfo.get("vehicleNo").toString())) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("车牌号为空，请重试！");
                return eiInfo;
            }
            String recCreator = eiInfo.getString("recCreator");//当前登录人
            if (null == eiInfo.get("recCreator") || StringUtils.isBlank(eiInfo.get("recCreator").toString())) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("当前登录人为空，请重试！");
                return eiInfo;
            }
            String recCreatorName = eiInfo.getString("recCreatorName");//当前登录人姓名
            if (null == eiInfo.get("recCreatorName") || StringUtils.isBlank(eiInfo.get("recCreatorName").toString())) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("当前登录人姓名为空，请重试！");
                return eiInfo;
            }
            String currentHandPointId = eiInfo.getString("currentHandPointId");//页面传入当前装卸点
            if (null == eiInfo.get("currentHandPointId") || StringUtils.isBlank(eiInfo.get("currentHandPointId").toString())) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("当前装卸点为空，请重试！");
                return eiInfo;
            }

            String factoryArea = eiInfo.getString("factoryArea");//页面传入当前装卸点
            if (null == eiInfo.get("factoryArea") || StringUtils.isBlank(eiInfo.get("factoryArea").toString())) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("厂区为空，请重试！");
                return eiInfo;
            }

            String status  = (String) eiInfo.get("status");//判断是否正在作业
            //如果status为30就代表为正在装卸货的车辆只需要跟新时间

            //效验token
            EiInfo outInfo = validToken(eiInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                outInfo.setMsg("token无效！");
                return outInfo;
            }

            String loadId="";
            //判断车辆是否被叫号
            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("segNo", segNo);
            hashMap.put("vehicleNo", vehicleNo);
            hashMap.put("carTraceNo", carTraceNo);
            hashMap.put("targetHandPointId", currentHandPointId);
            List<HashMap> LIRL0401List = dao.query(LIRL0402.QUERY_CALL_NUM_INFO, hashMap);
            if (CollectionUtils.isEmpty(LIRL0401List)){
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("车辆没有被叫号，请先叫号！");
                return eiInfo;
            }

            //查询出勾选的数据是否当前装卸点事否有数据
            Map queryLIRL0301 = new HashMap();
            queryLIRL0301.put("segNo", segNo);
            queryLIRL0301.put("carTraceNo", carTraceNo);
            queryLIRL0301.put("vehicleNo", vehicleNo);
            List<LIRL0301> lirl0301s = dao.query(LIRL0301.QUERY, queryLIRL0301);
            if (lirl0301s.size() < 1){
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("车辆跟踪数据不存在!");
                return eiInfo;
            }
            LIRL0301 lirl0301 = lirl0301s.get(0);
            lirl0301.setFactoryArea(factoryArea);
            if (StringUtils.isBlank(lirl0301.getCurrentHandPointId())) {
                //如果没0则是第一次进行开始装卸货
                //修改跟踪表的数据，修改当前装卸点
                lirl0301.setCurrentHandPointId(currentHandPointId);
                dao.update(LIRL0301.UPDATE, lirl0301);
                //插入开始装卸货表
                String strSeqTypeId = "TLIRL_SEQ0406";//开始装卸货流水号
                Date date = new Date(System.currentTimeMillis());
                String[] args = {segNo.substring(0, 2), date.toString(), ""};
                loadId = SequenceGenerator.getNextSequence(strSeqTypeId, "", args);
                Map insertLIRL0406 = new HashMap();
                RecordUtils.setCreator(insertLIRL0406);
                insertLIRL0406.put("segNo", lirl0301.getSegNo());
                insertLIRL0406.put("unitCode", lirl0301.getSegNo());
                insertLIRL0406.put("loadId", loadId);
                insertLIRL0406.put("status", "10");
                insertLIRL0406.put("loadDate", stringDate);
                insertLIRL0406.put("carTraceNo", lirl0301.getCarTraceNo());
                insertLIRL0406.put("dateSource", "20");
                insertLIRL0406.put("vehicleNo", vehicleNo);
                insertLIRL0406.put("targetHandPointId", lirl0301.getTargetHandPointId());
                insertLIRL0406.put("currentHandPointId", lirl0301.getCurrentHandPointId());
                insertLIRL0406.put("factoryArea", lirl0301.getFactoryArea());
                insertLIRL0406.put("documentType", 10);
                insertLIRL0406.put("recCreator", recCreator);
                insertLIRL0406.put("recCreatorName", recCreatorName);
                insertLIRL0406.put("recCreateTime", stringDate);
                insertLIRL0406.put("recRevisor", recCreator);
                insertLIRL0406.put("recRevisorName", recCreatorName);
                insertLIRL0406.put("recReviseTime", stringDate);
                insertLIRL0406.put("uuid", StrUtil.getUUID());
                insertLIRL0406.put("cancelLoadDate", " ");
                insertLIRL0406.put("voucherNum", " ");
                insertLIRL0406.put("sysRemark", " ");
                insertLIRL0406.put("remark", " ");
                LIRL0406 lirl0406 = new LIRL0406();
                lirl0406.fromMap(insertLIRL0406);
                dao.insert(LIRL0406.INSERT, lirl0406);
                //修改跟踪表的状态和清空目标装卸点和开始装卸货的时间
                lirl0301.setStatus("30");
                lirl0301.setTargetHandPointId("");
                //判断开始时间是否有值
                String beginEntruckingTime = lirl0301.getBeginEntruckingTime();
                if (StringUtils.isBlank(beginEntruckingTime)){
                    lirl0301.setBeginEntruckingTime(stringDate);
                }
                lirl0301.setRecRevisor(recCreator);
                lirl0301.setRecRevisorName(recCreatorName);
                lirl0301.setRecReviseTime(stringDate);
                dao.update(LIRL0301.UPDATE, lirl0301);

            } else {
                //查询出勾选的数据是否当前装卸点事否有数据
                queryLIRL0301 = new HashMap();
                queryLIRL0301.put("segNo", segNo);
                queryLIRL0301.put("carTraceNo", carTraceNo);
                if (!"30".equals(status)){
                    status="20";
                }
                queryLIRL0301.put("status", status);
                lirl0301s = dao.query(LIRL0301.QUERY, queryLIRL0301);
                if (lirl0301s.size() == 0) {
                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                    eiInfo.setMsg("车辆跟踪数据不存在!");
                    return eiInfo;
                }

                lirl0301 = lirl0301s.get(0);
                String currentHandPointId2 = lirl0301.getCurrentHandPointId();//当前装卸点代码
                if (currentHandPointId.equals(currentHandPointId2)) {
                    //当前的装卸点可以进行第二次的开始装卸货,修改开始装卸货的时间，开始装卸货的状态不发生改变。
                    lirl0301.setStatus("30");

                    //判断开始时间是否有值 不修改作业开始时间
                    String beginEntruckingTime = lirl0301.getBeginEntruckingTime();
                    if (StringUtils.isBlank(beginEntruckingTime)){
                        lirl0301.setBeginEntruckingTime(stringDate);
                    }
                    lirl0301.setBeginEntruckingTime(stringDate);
                    lirl0301.setRecRevisor(recCreator);
                    lirl0301.setRecRevisorName(recCreatorName);
                    lirl0301.setRecReviseTime(stringDate);
                    lirl0301.setVehicleNo(vehicleNo);
                    dao.update(LIRL0301.UPDATE, lirl0301);

                    Map insertLIRL0406 = new HashMap();
                    insertLIRL0406.put("segNo", segNo);
                    insertLIRL0406.put("unitCode", segNo);
                    String strSeqTypeId = "TLIRL_SEQ0406";//开始装卸货流水号
                    Date date = new Date(System.currentTimeMillis());
                    String[] args = {segNo.substring(0, 2), date.toString(), ""};
                    loadId = SequenceGenerator.getNextSequence(strSeqTypeId, "", args);
                    RecordUtils.setCreator(insertLIRL0406);

                    insertLIRL0406.put("loadId", loadId);
                    insertLIRL0406.put("status", 10);
                    insertLIRL0406.put("loadDate", stringDate);
                    insertLIRL0406.put("carTraceNo", carTraceNo);
                    insertLIRL0406.put("vehicleNo", lirl0301.getVehicleNo());
                    insertLIRL0406.put("targetHandPointId", lirl0301.getTargetHandPointId());
                    insertLIRL0406.put("currentHandPointId", lirl0301.getCurrentHandPointId());
                    insertLIRL0406.put("factoryArea", lirl0301.getFactoryArea());
                    insertLIRL0406.put("documentType", 20);
                    insertLIRL0406.put("recCreator", recCreator);
                    insertLIRL0406.put("recCreatorName", recCreatorName);
                    insertLIRL0406.put("recCreateTime", stringDate);
                    insertLIRL0406.put("recRevisor", recCreator);
                    insertLIRL0406.put("recRevisorName", recCreatorName);
                    insertLIRL0406.put("recReviseTime", stringDate);
                    insertLIRL0406.put("uuid", StrUtil.getUUID());
                    insertLIRL0406.put("dateSource", "20");
                    insertLIRL0406.put("cancelLoadDate", " ");
                    insertLIRL0406.put("voucherNum", " ");
                    insertLIRL0406.put("sysRemark", " ");
                    insertLIRL0406.put("remark", " ");
                    dao.insert(LIRL0406.INSERT, insertLIRL0406);
                    // }
                } else {
                    //需要把车辆跟踪号原本的装卸点的数据给结束装卸货，然后再开始新的装卸货
                    //查询出当前车辆跟踪号生成的开始装卸货的数据
                    Map queryLIRL0406 = new HashMap();
                    queryLIRL0406.put("segNo", segNo);
                    queryLIRL0406.put("carTraceNo", carTraceNo);
                    queryLIRL0406.put("status", 10);
                    queryLIRL0406.put("currentHandPointId", currentHandPointId2);
                    queryLIRL0406.put("delFlag", 0);
                    List<LIRL0406> lirl0406s = dao.query(LIRL0406.QUERY, queryLIRL0406);
                    if (lirl0406s.size() == 0) {
                        eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                        eiInfo.setMsg("车辆开始装卸货表数据不存在!");
                        return eiInfo;
                    }
                    //然后修改这条数据的状态
                    LIRL0406 lirl0406 = lirl0406s.get(0);
                    lirl0406.setStatus("00");
                    dao.update(LIRL0406.UPDATE, lirl0406);
                    //同时修改跟踪表中的数据  修改状态为进厂，目标装卸点有   当前装卸点为空
                    lirl0301.setStatus("10");
                    lirl0301.setCurrentHandPointId("");
                    lirl0301.setTargetHandPointId(lirl0406.getTargetHandPointId());
                    lirl0301.setRecRevisor(recCreator);
                    lirl0301.setRecRevisorName(recCreatorName);
                    lirl0301.setRecReviseTime(stringDate);
                    //然后开始新的装卸货
                    //修改跟踪表的数据，修改当前装卸点
                    lirl0301.setCurrentHandPointId(currentHandPointId);
                    lirl0301.setVehicleNo(vehicleNo);
                    dao.update(LIRL0301.UPDATE, lirl0301);
                    //把跟踪表中的数据添加到开始装卸货的表中
                    Map insertLIRL0406 = new HashMap();
                    insertLIRL0406.put("segNo", segNo);
                    insertLIRL0406.put("unitCode", segNo);
                    String strSeqTypeId = "TLIRL_SEQ0406";//开始装卸货流水号
                    Date date = new Date(System.currentTimeMillis());
                    String[] args = {segNo.substring(0, 2), date.toString(), ""};
                    loadId = SequenceGenerator.getNextSequence(strSeqTypeId, "", args);
                    RecordUtils.setCreator(insertLIRL0406);

                    insertLIRL0406.put("loadId", loadId);
                    insertLIRL0406.put("status", 10);
                    insertLIRL0406.put("loadDate", stringDate);
                    insertLIRL0406.put("carTraceNo", carTraceNo);
                    insertLIRL0406.put("vehicleNo", lirl0301.getVehicleNo());
                    insertLIRL0406.put("targetHandPointId", lirl0301.getTargetHandPointId());
                    insertLIRL0406.put("currentHandPointId", lirl0301.getCurrentHandPointId());
                    insertLIRL0406.put("factoryArea",factoryArea);
                    insertLIRL0406.put("documentType", 10);
                    insertLIRL0406.put("recCreator", recCreator);
                    insertLIRL0406.put("recCreatorName", recCreatorName);
                    insertLIRL0406.put("recCreateTime", stringDate);
                    insertLIRL0406.put("recRevisor", recCreator);
                    insertLIRL0406.put("recRevisorName", recCreatorName);
                    insertLIRL0406.put("recReviseTime", stringDate);
                    insertLIRL0406.put("uuid", StrUtil.getUUID());
                    insertLIRL0406.put("dateSource", "20");
                    insertLIRL0406.put("cancelLoadDate", " ");
                    insertLIRL0406.put("voucherNum", " ");
                    insertLIRL0406.put("sysRemark", " ");
                    insertLIRL0406.put("remark", " ");
                    dao.insert(LIRL0406.INSERT, insertLIRL0406);
                    //修改跟踪表的状态和清空目标装卸点
                    lirl0301.setStatus("30");
                    lirl0301.setTargetHandPointId("");
                    //判断开始时间是否有值 不修改作业开始时间
                    String beginEntruckingTime = lirl0301.getBeginEntruckingTime();
                    if (StringUtils.isBlank(beginEntruckingTime)){
                        lirl0301.setBeginEntruckingTime(stringDate);
                    }
                    dao.update(LIRL0301.UPDATE, lirl0301);

                }

            }
            // //判断目标装卸点和当前装卸点是否一致
            // isTheSiteConsistent(stringDate, segNo, recCreator, recCreatorName, lirl0301);

            //插入叫号备份表
            //插入备份主表
            //删除叫号表
            Map<String, String> hashMapLirl0502 = new HashMap<>();
            hashMapLirl0502.put("segNo", segNo);
            hashMapLirl0502.put("carTraceNo", carTraceNo);
//                hashMapLirl0502.put("targetHandPointId", targetHandPointId);
//                hashMapLirl0502.put("allocateVehicleNo", allocateVehicleNo);
            hashMapLirl0502.put("vehicleNo", vehicleNo);
//            List<LIRL0402> queryLIRL0402 = this.dao.query(LIRL0402.QUERY_CALL_NUM_INFO, hashMapLirl0502);
            HashMap<Object, Object> hashMap1 = new HashMap<>();
            hashMap1.put("segNo", segNo);
            hashMap1.put("unitCode", segNo);
            hashMap1.put("uuid", StrUtil.getUUID());
            hashMap1.put("carTraceNo", carTraceNo);
            hashMap1.put("vehicleNo", vehicleNo);
            hashMap1.put("queueNumber", 0);
            hashMap1.put("voucherNum"," ");
            hashMap1.put("priorityLevel","99");
            hashMap1.put("queueDate",DateUtil.curDateTimeStr14());
            hashMap1.put("targetHandPointId",currentHandPointId);
            hashMap1.put("factoryArea",factoryArea);
            hashMap1.put("recCreator",recCreator);
            hashMap1.put("recCreatorName",recCreatorName);
            hashMap1.put("recCreateTime",DateUtil.curDateTimeStr14());
            hashMap1.put("recRevisor",recCreator);
            hashMap1.put("recRevisorName",recCreatorName);
            hashMap1.put("recReviseTime",DateUtil.curDateTimeStr14());
            hashMap1.put("delFlag","0");
            hashMap1.put("archiveFlag","0");
            hashMap1.put("remark","开始装卸货添加到备份排队子表");
            hashMap1.put("sysRemark","开始装卸货添加到备份排队子表");
            hashMap1.put("tenantId"," ");
            hashMap1.put("overtimeCount","0");
            dao.insert(LIRL0409.INSERT,hashMap1);
            //删除叫号表，排队表
            this.dao.update(LIRL0402.DELETE, hashMapLirl0502);
            this.dao.update(LIRL0401.DELETE, hashMapLirl0502);

            eiInfo.set("loadId",loadId);
            eiInfo.set("currentHandPointId",currentHandPointId);
            eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg(e.getMessage());
            return eiInfo;
        }
        return eiInfo;
    }
    /***
     * PDA 车辆结束装卸货
     * prog.wl_vehicle_plan_mgr.vehicle_finish_load_good
     * 	S_LI_RL_0081
     */
    public EiInfo vehicleFinishLoadGoodCQ(EiInfo eiInfo) {

        try {
            String stringDate = DateUtil.curDateTimeStr14();//修改时间
            String in_seg_no = (String) eiInfo.get("segNo");//账套
            if (null == eiInfo.get("segNo") || StringUtils.isBlank(eiInfo.get("segNo").toString())) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("业务单元代码为空，请重试！");
                return eiInfo;
            }
            String in_car_trace_no = (String) eiInfo.get("carTraceNo"); //车辆跟踪单号
            if (null == eiInfo.get("carTraceNo") || StringUtils.isBlank(eiInfo.get("carTraceNo").toString())) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("车辆跟踪单号为空，请重试！");
                return eiInfo;
            }
            String in_modi_person = eiInfo.getString("recCreator");//当前登录人
            if (null == eiInfo.get("recCreator") || StringUtils.isBlank(eiInfo.get("recCreator").toString())) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("当前登录人为空，请重试！");
                return eiInfo;
            }
            String in_modi_person_name = eiInfo.getString("recCreatorName");//当前登录人姓名
            if (null == eiInfo.get("recCreatorName") || StringUtils.isBlank(eiInfo.get("recCreatorName").toString())) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("当前登录人姓名为空，请重试！");
                return eiInfo;
            }
            String in_target_hand_point_id = eiInfo.getString("targetHandPointId");//页面传入目标装卸点
            if (null == eiInfo.get("targetHandPointId") || StringUtils.isBlank(eiInfo.get("targetHandPointId").toString())) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("目标装卸点为空，请重试！");
                return eiInfo;
            }
            String in_next_target = eiInfo.getString("nextTarget");//下一装卸点
            if (null == eiInfo.get("nextTarget") || StringUtils.isBlank(eiInfo.get("nextTarget").toString())) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("下一装卸点为空，请重试！");
                return eiInfo;
            }

            String allocateVehicleNo = eiInfo.getString("allocateVehicleNo");//配单号

            String loadIdNew = eiInfo.getString("loadId");//
//            if (null == eiInfo.get("allocateVehicleNo") || StringUtils.isBlank(eiInfo.get("allocateVehicleNo").toString())) {
//                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
//                eiInfo.setMsg("配单号为空，请重试！");
//                return eiInfo;
//            }


            //效验token
            /*EiInfo outInfo = validToken(eiInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                outInfo.setMsg("token无效！");
                return outInfo;
            }*/

            //首先查询出勾选数据的当前装卸点
            Map queryLIRL0301 = new HashMap();
            queryLIRL0301.put("segNo",in_seg_no);
            queryLIRL0301.put("carTraceNo",in_car_trace_no);
            List<LIRL0301> lirl0301s = dao.query(LIRL0301.QUERY, queryLIRL0301);
            if (lirl0301s.size() == 0){
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("车辆跟踪数据不存在!");
                return eiInfo;
            }
            LIRL0301 lirl0301 = lirl0301s.get(0);
            String currentHandPointId = lirl0301.getCurrentHandPointId();//当前装卸点代码
            String vehicleNo = lirl0301.getVehicleNo();//车牌号


            //卸货点当前作业数-1
            Map updateLIRL0306 = new HashMap();
            updateLIRL0306.put("segNo", in_seg_no);
            updateLIRL0306.put("handPointId", lirl0301.getCurrentHandPointId());
            updateLIRL0306.put("currentJobNumber",new BigDecimal("1"));
            updateLIRL0306.put("recRevisor", in_modi_person);
            updateLIRL0306.put("recRevisorName", in_modi_person_name);
            updateLIRL0306.put("recReviseTime", stringDate);
            // dao.update(LIRL0306.UPDATE_JOB_NUMBER_BY_HAND_POINT2,updateLIRL0306);

            //控制程序
            //如果前台页面传的下一个目标为10（下一个装卸点）
            //那么目标装卸点为必填，如果为空，那么就提升报错
            if ("10".equals(in_next_target)&&StringUtils.isEmpty(in_target_hand_point_id)){
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("车辆跟踪号"+in_car_trace_no+"的下个目标为下一装卸点，所以下一装卸点字段不能为空!");
                return eiInfo;
            }else if ("20".equals(in_next_target)){
                //离厂 目标装卸点为空
                in_target_hand_point_id = "";
                // 结束装卸货选择出厂时，增加校验，若此次车辆作业的捆包未在MES生成实物库存，则给与报错，不可做结束装卸货。
            }

            String factory = "";
            if (StringUtils.isNotBlank(in_target_hand_point_id)){
                Map queryLIRL03011 = new HashMap();
                queryLIRL03011.put("segNo",in_seg_no);
                queryLIRL03011.put("handPointId",in_target_hand_point_id);
                queryLIRL03011.put("status","30");
                List<LIRL0304> queryLIRL0304 = this.dao.query(LIRL0304.QUERY, queryLIRL03011);
                if (CollectionUtils.isEmpty(queryLIRL0304)){
                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                    eiInfo.setMsg("装卸点没有厂区！");
                    return eiInfo;
                }
                factory=queryLIRL0304.get(0).getFactoryBuilding();
            }

//            //查询最后一次开始装卸货的车辆
//            Map queryLIRL0406 = new HashMap();
//            queryLIRL0406.put("segNo",in_seg_no);
//            queryLIRL0406.put("vehicleNo",vehicleNo);
//            queryLIRL0406.put("carTraceNo",in_car_trace_no);
//            List<LIRL0406> lirl0406s = dao.query(LIRL0406.QUERY_ALL, queryLIRL0406);
//            //然后标记当前装卸点为1 暂时没用到
//            String loadIdNew="";
//            String targetHandPointId="";
//            String currentHandPointIdNew="";
//            if (CollectionUtils.isNotEmpty(lirl0406s)){
//                loadIdNew = lirl0406s.get(0).getLoadId();
//                targetHandPointId = lirl0406s.get(0).getTargetHandPointId();
//                currentHandPointIdNew = lirl0406s.get(0).getCurrentHandPointId();
//            }
            //如果是其他家的 根据客户勾选的目标装卸点来
            //先修改跟踪表中的目标装卸点（下一装卸点）
            Map updateLIRL0301 = new HashMap();
            updateLIRL0301.put("segNo",in_seg_no);
            updateLIRL0301.put("targetHandPointId",in_target_hand_point_id);
            updateLIRL0301.put("carTraceNo",in_car_trace_no);
            updateLIRL0301.put("delFlag",0);
            updateLIRL0301.put("factoryArea",factory);
            dao.update(LIRL0301.UPDATE_LIRL0301TARGET_HAND_POINT_ID,updateLIRL0301);

            // if ("20".equals(in_next_target)&&StringUtils.isBlank(in_target_hand_point_id)){
            //     in_target_hand_point_id="离厂";
            // }
            //插入车辆结束装卸货表
            Map insertLIRL0407 = new HashMap<>();
            insertLIRL0407.put("segNo",in_seg_no);
            insertLIRL0407.put("unitCode",in_seg_no);
            String strSeqTypeId = "TLIRL_SEQ0407";//开始装卸货流水号
            Date date = new Date(System.currentTimeMillis());
            String[] args = {in_seg_no.substring(0, 2), date.toString(), ""};
            String loadId = SequenceGenerator.getNextSequence(strSeqTypeId, "", args);
            insertLIRL0407.put("finishLoadId",loadId);
            insertLIRL0407.put("status",10);
            insertLIRL0407.put("finishLoadDate",stringDate);
            insertLIRL0407.put("carTraceNo",lirl0301.getCarTraceNo());
            insertLIRL0407.put("dateSource",20);
            insertLIRL0407.put("vehicleNo",lirl0301.getVehicleNo());
            insertLIRL0407.put("currentHandPointId",lirl0301.getCurrentHandPointId());
            insertLIRL0407.put("tatgetHandPointId",in_target_hand_point_id);
            insertLIRL0407.put("factoryArea",factory);
            insertLIRL0407.put("nextTatget",in_next_target);
            insertLIRL0407.put("recCreator", in_modi_person);
            insertLIRL0407.put("recCreatorName", in_modi_person_name);
            insertLIRL0407.put("recCreateTime", stringDate);
            insertLIRL0407.put("recRevisor", in_modi_person);
            insertLIRL0407.put("recRevisorName", in_modi_person_name);
            insertLIRL0407.put("recReviseTime", stringDate);
            insertLIRL0407.put("uuid", StrUtil.getUUID());
            insertLIRL0407.put("delFlag", 0);
//            insertLIRL0407.put("loadId", loadIdNew);
            LIRL0407 lirl0407 = new LIRL0407();
            lirl0407.fromMap(insertLIRL0407);
            lirl0407.setLoadId(loadIdNew);
            dao.insert(LIRL0407.INSERT,lirl0407);
            //根据结束装卸货中开是否有目标装卸点重是否有值
            //如果有值   跟踪表的状态为30开始装卸货状态
            //否者    为结束装卸货状态
            Map queryLIRL0407 = new HashMap();
            queryLIRL0407.put("segNo",in_seg_no);
            queryLIRL0407.put("carTraceNo",in_car_trace_no);
            queryLIRL0407.put("finishLoadId",loadId);
            queryLIRL0407.put("delFlag",0);
            int count = super.count(LIRL0407.COUNT, queryLIRL0407);
            if (count>0){
                updateLIRL0301 = new HashMap<>();
                updateLIRL0301.put("segNo",in_seg_no);
                updateLIRL0301.put("status",40);
                updateLIRL0301.put("currentHandPointId","");
                updateLIRL0301.put("tatgetHandPointId",in_target_hand_point_id);
                updateLIRL0301.put("completeUninstallTime",stringDate);
                updateLIRL0301.put("recRevisor", in_modi_person);
                updateLIRL0301.put("recRevisorName", in_modi_person_name);
                updateLIRL0301.put("recReviseTime", stringDate);

                updateLIRL0301.put("carTraceNo", in_car_trace_no);
                updateLIRL0301.put("segNO", in_seg_no);
                updateLIRL0301.put("delFlag", 0);
                dao.update(LIRL0301.UPDATE_LIRL0301JSZX,updateLIRL0301);
            }else {
                updateLIRL0301 = new HashMap<>();
                updateLIRL0301.put("segNo",in_seg_no);
                updateLIRL0301.put("status",40);
                updateLIRL0301.put("currentHandPointId","");
                updateLIRL0301.put("tatgetHandPointId","");
                updateLIRL0301.put("completeUninstallTime",stringDate);
                updateLIRL0301.put("recRevisor", in_modi_person);
                updateLIRL0301.put("recRevisorName", in_modi_person_name);
                updateLIRL0301.put("recReviseTime", stringDate);

                updateLIRL0301.put("carTraceNo", in_car_trace_no);
                updateLIRL0301.put("segNO", in_seg_no);
                updateLIRL0301.put("delFlag", 0);
                dao.update(LIRL0301.UPDATE_LIRL0301JSZX2,updateLIRL0301);
            }
            if (StringUtils.isNotBlank(in_target_hand_point_id)){
//                for (String allocateVehicleNo1 : allocateVehicleNo.split(",")) {
                    //更新配单目标装卸点
                    Map<String, Object> hashMapLirl0502 = new HashMap<>();
                    hashMapLirl0502.put("segNo", in_seg_no);
                    hashMapLirl0502.put("carTraceNo", in_car_trace_no);
                    hashMapLirl0502.put("targetHandPointId", in_target_hand_point_id);
                    hashMapLirl0502.put("allocateVehicleNo", allocateVehicleNo);
                    hashMapLirl0502.put("recRevisor", in_modi_person);
                    hashMapLirl0502.put("recRevisorName", in_modi_person_name);
                    hashMapLirl0502.put("recReviseTime", stringDate);
//                    this.dao.update(LIRL0503.UPDATE_TARGET_HAND_POINT_ID, hashMapLirl0502);
                    //删除叫号表数据
                    this.dao.update(LIRL0402.DELETE, hashMapLirl0502);
                    //插入排队表
                    HashMap queryQueMap = new HashMap();
                    queryQueMap.put("segNo",in_seg_no);
                    List<HashMap> queryQueReslist = dao.query("LIRL0401.queryMaxQueueNumber", queryQueMap);
                    HashMap queryQueRes =queryQueReslist.get(0);
                    String queueNumberStr =String.valueOf(queryQueRes.get("maxQueueNumber"));
                    Integer queueNumber = 0;
                    try{
                        queueNumber = new Integer(queueNumberStr);
                    }
                    catch (Exception e){
                        eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                        eiInfo.setMsg("配车单排序转换错误，配车单号:" + allocateVehicleNo);
                        return eiInfo;
                    }
                    hashMapLirl0502.put("queueNumber",queueNumber);
                    hashMapLirl0502.put("priorityLevel","1");//厂内转运
                    hashMapLirl0502.put("queueDate",DateUtil.curDateTimeStr14());
                    hashMapLirl0502.put("factoryArea"," ");
                    hashMapLirl0502.put("delFlag","0");
                    hashMapLirl0502.put("sysRemark","车辆到达下一目标装卸点");
                    hashMapLirl0502.put("remark","车辆到达下一目标装卸点");
                    hashMapLirl0502.put("recCreator",in_modi_person);
                    hashMapLirl0502.put("recCreatorName",in_modi_person_name);
                    hashMapLirl0502.put("recCreateTime",DateUtil.curDateTimeStr14());
                    hashMapLirl0502.put("uuid",UUIDUtils.getUUID());
                    hashMapLirl0502.put("archiveFlag",0);
                    hashMapLirl0502.put("vehicleNo",vehicleNo);
                    hashMapLirl0502.put("voucherNum",StringUtils.isNotEmpty(allocateVehicleNo)?allocateVehicleNo:" ");
                    hashMapLirl0502.put("unitCode",in_seg_no);
                    this.dao.insert(LIRL0401.INSERT,hashMapLirl0502);
//                }

            }
            if ("20".equals(in_next_target)){
                //清空车辆排队表
                //删除排队表中的信息
                HashMap<String, Object> stringStringHashMap = new HashMap<>();
                stringStringHashMap.put("segNo",in_seg_no);
                stringStringHashMap.put("carTraceNo",in_car_trace_no);
                stringStringHashMap.put("vehicleNo",vehicleNo);
                //删除排队表
                this.dao.delete(LIRL0401.DELETE, stringStringHashMap);
                //删除叫号表
                this.dao.delete(LIRL0402.DELETE, stringStringHashMap);
                //删除超时表
                this.dao.delete(LIRL0403.DELETE, stringStringHashMap);
            }
            eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg(e.getMessage());
            return eiInfo;
        }
        return eiInfo;
    }


    /**
     * 判断是否是卸货完成，完成后提示新增附件信息
     * 查询配单表，拿到装卸点，装货配单号
     *
     * S_LI_RL_0185
     * @param eiInfo
     * @return
     */
    public EiInfo isUnloadComplete(EiInfo eiInfo) {

        String segNo = (String) eiInfo.get("segNo");
        String carTraceNo = (String) eiInfo.get("carTraceNo");
        String allocateVehicleNo = (String) eiInfo.get("allocateVehicleNo");
        if (StringUtils.isBlank(segNo) || StringUtils.isBlank(carTraceNo)) {
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg("车辆跟踪号、账套为空！");
            return eiInfo;
        }
        try {
            if (StringUtils.isNotBlank(allocateVehicleNo)) {

                Map<String, Object> hashMapLirl0502 = new HashMap<>();
                hashMapLirl0502.put("segNo", segNo);
                hashMapLirl0502.put("carTraceNo", carTraceNo);
//                hashMapLirl0502.put("status", "20");
                hashMapLirl0502.put("allocType", "20");
                hashMapLirl0502.put("delFlag", "0");
                hashMapLirl0502.put("allocateVehicleNo", allocateVehicleNo);
                //判断装卸类型是否为卸货或者卸货+装货
                List<LIRL0301> queryLIRL0301 = this.dao.query(LIRL0301.QUERY, hashMapLirl0502);
                if (CollectionUtils.isEmpty(queryLIRL0301)){
                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                    eiInfo.setMsg("车辆跟踪信息不存在！");
                    return eiInfo;
                }
                String handType = queryLIRL0301.get(0).getHandType();
                if ("20".equals(handType)||"30".equals(handType)){
                    //查询配单
                    List<LIRL0502> queryLIRL0502=  this.dao.query(LIRL0502.QUERY, hashMapLirl0502);
                    List<LIRL0502> queryLIRL05021=new ArrayList<>();
                    if (CollectionUtils.isEmpty(queryLIRL0502)){
                        HashMap<Object, Object> hashMapLirl05021 = new HashMap<>();
                        hashMapLirl05021.put("segNo", segNo);
                        hashMapLirl05021.put("carTraceNo", carTraceNo);
                        hashMapLirl05021.put("allocType", "10");
                        hashMapLirl05021.put("delFlag", "0");
                        hashMapLirl05021.put("allocateVehicleNo", allocateVehicleNo);
                        queryLIRL05021 = this.dao.query(LIRL0502.QUERY, hashMapLirl05021);
                        if (CollectionUtils.isEmpty(queryLIRL05021)){
                            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                            eiInfo.setMsg("卸货配单信息不存在！");
                            return eiInfo;
                        }

                    }
                    List<String> collect=new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(queryLIRL0502)){
                        collect  = queryLIRL0502.stream().map(lirl0503 -> lirl0503.getAllocateVehicleNo()).collect(Collectors.toList());
                    }else {
                        collect  = queryLIRL05021.stream().map(lirl0503 -> lirl0503.getAllocateVehicleNo()).collect(Collectors.toList());
                    }
                    hashMapLirl0502.put("allocateVehicleNoAdd", collect);
                    hashMapLirl0502.put("outPackFlag", "0");//去除自带货
                    hashMapLirl0502.put("voucherNumFlag", "0"); //去除无计划
                    hashMapLirl0502.put("statusEq", "20"); //去除无计划
                    hashMapLirl0502.put("voucherNumEq", "20"); //去除无计划
                    if (CollectionUtils.isEmpty(queryLIRL0502)){
                        hashMapLirl0502.put("allocateVehicleNoAdd", "");
                        hashMapLirl0502.put("nextAlcVehicleNoIn", collect.get(0));
                    }
                    //判断卸货配单是否全部卸完
                    List<HashMap> query = this.dao.query(LIRL0503.QUERY_ALL_PACK_NEW, hashMapLirl0502);
                    if (CollectionUtils.isEmpty(query)){
                        eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
                        eiInfo.setMsg("捆包已全部卸载完成，是否上传入库质量确认单附件？");
                    }
                }
            }
            eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg(e.getMessage());
        }
        return eiInfo;
    }

    /***
     * 下一目标装卸点，自动推荐
     *
     * 	S_LI_RL_0082
     */
    public EiInfo nextTargetHandPoint(EiInfo inInfo) {
        String segNo = (String) inInfo.get("segNo");
        String carTraceNo = (String) inInfo.get("carTraceNo");
        String vehicleNo = (String) inInfo.get("vehicleNo");

        //查询所有的装卸点

        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("segNo", segNo);
        List<LIRL0304> queryLIRL0304 = this.dao.query(LIRL0304.QUERY, hashMap);
        if (CollectionUtils.isEmpty(queryLIRL0304)){
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("未查询到装卸点信息!");
            return inInfo;
        }
        List<Map> listAll = new ArrayList<>();

        //查询配单表
        List<HashMap> queryLIRL0503 = this.dao.query(LIRL0503.QUERY_ALL_ALLOCATE_VEHICLE_NO_INFO, hashMap);
        if (CollectionUtils.isNotEmpty(queryLIRL0503)) {
            for (HashMap lirl0503 : queryLIRL0503) {
                String handPointId = lirl0503.get("targetHandPointId").toString();
                String handPointName = lirl0503.get("handPointName").toString();
                HashMap<Object, Object> hashMapAll = new HashMap<>();
                hashMapAll.put("handPointId", handPointId);
                hashMapAll.put("handPointName", handPointName);
                listAll.add(hashMapAll);
            }
        }

        // HashMap<String, String> hashMapAll = new HashMap<>();
        //查询排队表中的装卸点信息
        Map<String, Object> hashMapLirl0401 = new HashMap<>();
        hashMapLirl0401.put("segNo", segNo);
        hashMapLirl0401.put("carTraceNo", carTraceNo);
        hashMapLirl0401.put("vehicleNo", vehicleNo);

        List<HashMap> queryLIRL0401 = this.dao.query(LIRL0401.QUERY_HAND_POINT_NAME, hashMapLirl0401);
        //排队表中的信息放在最上面
        for (HashMap lirl0401 : queryLIRL0401) {
            listAll.add(lirl0401);
        }

        for (LIRL0304 lirl0304 : queryLIRL0304) {
            String handPointId = lirl0304.getHandPointId();
            String handPointName = lirl0304.getHandPointName();
            HashMap<Object, Object> hashMapAll = new HashMap<>();
            hashMapAll.put("handPointId", handPointId);
            hashMapAll.put("handPointName", handPointName);
            listAll.add(hashMapAll);
        }

        inInfo.set("list", listAll);
        if (CollectionUtils.isEmpty(queryLIRL0304) && CollectionUtils.isEmpty(queryLIRL0503)) {
            List<Map<String, Object>> mapList = queryLIRL0304.stream()
                    .map(item -> {
                        Map<String, Object> map = new HashMap<>();
                        map.put("id", item.getHandPointId());
                        map.put("name", item.getHandPointName());
                        return map;
                    })
                    .collect(Collectors.toList());
            inInfo.set("list", mapList);
        }

        return inInfo;
    }

    /***
     * 出库确认查询出厂的车辆信息(重庆)
     * S_LI_RL_0087
     */
    public EiInfo queryOutFactoryInfo(EiInfo inInfo) {
        String segNo = (String) inInfo.get("segNo");
        if (StrUtil.isBlank(segNo)){
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("系统账套不能为空!");
            return inInfo;
        }
        try {
            List<String> status = new ArrayList<>();
            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("segNo", segNo);
            hashMap.put("status","40");
            hashMap.put("delFlag","0");
            hashMap.put("targetHandPointIdNull","");//出厂车辆
            List<HashMap> queryLIRL0301 = this.dao.query(LIRL0301.QUERY_VEHICLE, hashMap);
            inInfo.set("list", queryLIRL0301);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        }
        catch (Exception e){
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            return inInfo;
        }
        return inInfo;
    }


    /***
     *
     * PDA出库确认根据车牌号查询捆包(重庆)
     * S_LI_RL_0091
     */
    public EiInfo outFactoryConfirmByVehicleNo(EiInfo inInfo) {
        String segNo = (String) inInfo.get("segNo"); //账套
        String vehicleNo = (String) inInfo.get("vehicleNo"); //车牌号
        String carTraceNo = (String) inInfo.get("carTraceNo");//车辆跟踪号
        if (StrUtil.isBlank(segNo)|| StrUtil.isBlank(vehicleNo)|| StrUtil.isBlank(carTraceNo)){
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("系统账套、车牌号、车辆跟踪号不能为空!");
            return inInfo;
        }
        try {
            //查询配单表拿到配单主项号查询行车作业清单
            Map<String, Object> hashMap = new HashMap<>();
            hashMap.put("segNo", segNo);
            hashMap.put("vehicleNo", vehicleNo);
            hashMap.put("carTraceNo", carTraceNo);
            hashMap.put("allocType", "10");
//            hashMap.put("flag", "10");
            List<LIRL0502> queryLIRL0502 = this.dao.query(LIRL0502.QUERY, hashMap);
            if (CollectionUtils.isEmpty(queryLIRL0502)){
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("未查询到装货配单信息!");
                return inInfo;
            }
            String allocateVehicleNo =  queryLIRL0502.get(0).getAllocateVehicleNo();
            hashMap.put("voucherNum",allocateVehicleNo);
            List<String> queryList = this.dao.query(LIDS1101.QUERY_PACK_INFO, hashMap);
            if (CollectionUtils.isEmpty(queryList)){
                inInfo.set("list", queryList);
                inInfo.setStatus(EiConstant.STATUS_SUCCESS);
                return inInfo;
            }
            //根据捆包查询配单子表信息
            Map<String, Object> lirl0503Map = new HashMap<>();
            lirl0503Map.put("allocateVehicleNo", allocateVehicleNo);
            lirl0503Map.put("packList", queryList);
            lirl0503Map.put("status","20");
            lirl0503Map.put("outPackFlag", "0");
            lirl0503Map.put("voucherNumFlag", "0");
            List<LIRL0503> queryLIRL0503 = this.dao.query(LIRL0503.QUERY, lirl0503Map);
            if (CollectionUtils.isEmpty(queryLIRL0503)){
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("未查询到配单子项信息!");
                return inInfo;
            }
            List<Map> lirl0503List = new ArrayList<>();
            for (LIRL0503 lirl0503 : queryLIRL0503) {
                lirl0503List.add(lirl0503.toMap());
            }
            inInfo.set("list", lirl0503List);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

    /***
     * PDA出库确认自动打印出库码单(重庆)
     *
     * S_LI_RL_0092
     */
    public EiInfo outFactoryConfirm(EiInfo inInfo) {

        EiInfo outInfo = new EiInfo();
        String validString = validExWarehousingParam(inInfo.getAttr(),inInfo);

        String segNo = (String) inInfo.get("segNo");
        if (!StringUtils.isBlank(validString)) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(validString);
            return outInfo;
        }

        try {
            outInfo = validToken(inInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                return outInfo;
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("token无效！");
            return outInfo;
        }


        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }
        //组装list
        List<String> addList = new ArrayList<>();
        List<String> urlList = new ArrayList<>();
        List<String> addUrlList = new ArrayList<>();
        List<Map> resultList = new ArrayList<>();
        List<String> docUrlList = new ArrayList<>();

        inInfo.set("teamId","10");
        inInfo.set("workingShift","10");
        inInfo.set(EiConstant.serviceId,"S_UC_PR_0404");
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;
            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            List<HashMap> reslut = (List<HashMap>) outInfo.get("reslut");
            //自动打印出库单
            for (HashMap hashMap : reslut) {
                EiInfo eiInfo = new EiInfo();
                String segNo1 = MapUtils.getString(hashMap, "segNo");
                String putoutId = MapUtils.getString(hashMap, "putoutId");
                //根据捆包号、材料管理号、提单号查询出库服务
                EiInfo outInfo1 = new EiInfo();
                outInfo1.set("segNo", segNo1);
                outInfo1.set("putoutId", putoutId);//提单号
                // outInfo1.set("packId", packId);//捆包
                outInfo1.set("offset", "0");
                outInfo1.set("limit", "100");
                outInfo1.set("redFlag", "0");
                outInfo1.set(EiConstant.serviceId, "S_UC_EW_0287");
                EiInfo outInfo0287 = EServiceManager.call(outInfo1, TokenUtils.getXplatToken());
                if (outInfo0287.getStatus() == -1) {
                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                    eiInfo.setMsg("调用物流中心返回报错,出库单查询服务失败:" + outInfo0287.getMsg());
                    throw new RuntimeException(outInfo0287.getMsg());
                }
                List<Map> result0287 = (List<Map>) outInfo0287.get("result");
                if (CollectionUtils.isNotEmpty(result0287) && result0287.size() == 1) {
                    if (resultList.size()==0){
                        for (Map map0287 : result0287) {
                            String uuid = MapUtils.getString(map0287, "uuid", "");//出库单子项号
                            String putOutId = MapUtils.getString(map0287, "putoutId", "");//出库单子项号
                            String reportUrl = PlatApplicationContext.getProperty("billPrint") + PlatApplicationContext.getProperty("P.printParam")+"&uuid=" + uuid + "&putoutStackingRecNum=" + putOutId + "&segNo=" + segNo1+"&format=PDF";
                            addUrlList.add(reportUrl);
                            // eiInfo.set("docUrlList", reportUrl);
                            urlList.add(reportUrl);
                            resultList .add(map0287);
                        }
                    }else {
                        resultList.add(result0287.get(0));
                        String uuids = "";
                        String putOutIds = "";
                        int size = resultList.size();
                        int count = 1;
                        for (Map map1 : resultList) {
                            String uuid = MapUtils.getString(map1, "uuid", "");//出库单子项号
                            String putOutId = MapUtils.getString(map1, "putoutId", "");//出库单子项号
                            if (count < size) {
                                uuids += uuid.concat("','");
                                putOutIds += putOutId.concat("','");
                            } else {
                                uuids += uuid;
                                putOutIds += putOutId;
                            }
                            // vehicleNoAdd.add(putOutId.concat(String.valueOf(count)));
                            count++;
                        }
                        String reportUrl = PlatApplicationContext.getProperty("billPrint") + PlatApplicationContext.getProperty("P.printParam")+"&uuid=" + uuids + "&putoutStackingRecNum=" + putOutIds + "&segNo=" + segNo1+"&format=PDF";
                        addUrlList.add(reportUrl);
                    }
                } else if (CollectionUtils.isNotEmpty(result0287)){
                    String uuids = "";
                    String putOutIds = "";
                    int size = result0287.size();
                    int count = 1;
                    for (Map map1 : result0287) {
                        String uuid = MapUtils.getString(map1, "uuid", "");//出库单子项号
                        String putOutId = MapUtils.getString(map1, "putoutId", "");//出库单子项号
                        if (count < size) {
                            uuids += uuid.concat("','");
                            putOutIds += putOutId.concat("','");
                        } else {
                            uuids += uuid;
                            putOutIds += putOutId;
                        }
                        count++;
                    }
                    String reportUrl = PlatApplicationContext.getProperty("billPrint") + PlatApplicationContext.getProperty("P.printParam")+"&uuid=" + uuids + "&putoutStackingRecNum=" + putOutIds + "&segNo=" + segNo1+"&format=PDF";
                    addUrlList.add(reportUrl);
                }else {
                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                    eiInfo.setMsg("调用物流中心返回报错,出库单查询服务失败:出库明细为空！");
                    throw new RuntimeException("调用物流中心返回报错,出库单查询服务失败:出库明细为空！");
                }
            }

            //判断自动打印开关是否开启
            String ifRemotePrint = new SwitchUtils().getProcessSwitchValue(segNo, "IF_REMOTE_PRINT", dao);
            String printName="";
            String printPort="";
            if ("1".equals(ifRemotePrint)) {
                String printName1 = findPrintName(segNo,"20");
                if (StringUtils.isNotBlank(printName1)){
                    String[] split = printName1.split("-");
                    printName= split[0];
                    printPort= split[1];
                }
                printName = removeLastHyphen(printName);
                for (String url : addUrlList) {
                    new ServiceLIRLInterface().uploadPrintFile(url, "/"+segNo + ".pdf", segNo, "URLTC",printName,printPort);
                }
            }

        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }
        return outInfo;
    }

    /**
     * 下载并上传文件
     *
     * @param url
     * @param fileName
     */
    private void uploadPrintFile(String url, String fileName, String segNo, String itemCode) {
        try {
            // 发起 HTTP 请求获取文件内容
            ResponseEntity<byte[]> rsp = restTemplate.getForEntity(url, byte[].class);
            // 获取当前工作目录，并使用 File.separator 作为文件分隔符
            String directoryPath = "/apps/upload";
            File directory = new File(directoryPath);
            if (!directory.exists()) {
                if (directory.mkdirs()) {
                    System.out.println("目录创建成功");
                } else {
                    System.out.println("目录创建失败");
                }
            }
            // 构建完整的文件存储路径
            fileName = directoryPath + fileName;
            // 将文件内容保存到本地
            saveFile(fileName, rsp.getBody());
            String printerName = "NPIE1166C";
            //printPDF(fileName, printerName,LANDSCAPE);
            //查询配置表 获取打印机地址
            Map querytlirl0314 = new HashMap();
            querytlirl0314.put("segNo",segNo);
            querytlirl0314.put("itemCode",itemCode);
            List<LIRL0314> lirl0314s = dao.query(LIRL0314.QUERY, querytlirl0314);
            if (lirl0314s.size()>0){
                LIRL0314 lirl0314 = lirl0314s.get(0);
                printPDF(fileName, lirl0314.getItemCname(), 9100,10000);
            }
        } catch (HttpClientErrorException e) {
            // 处理客户端错误，比如 400、404 等错误码对应的情况
            System.err.println("客户端错误: " + e.getStatusCode() + ", 错误信息: " + e.getMessage());
        } catch (HttpServerErrorException e) {
            // 处理服务器端错误，比如 500 等错误码对应的情况
            System.err.println("服务器端错误: " + e.getStatusCode() + ", 错误信息: " + e.getMessage());
        } catch (Exception e) {
            // 处理其他通用异常情况
            System.err.println("其他异常: " + e.getMessage());
        }
    }

    private void uploadPrintFile2(String url, String fileName, String segNo, String itemCode) {

        try {
            // 获取当前工作目录，并使用 File.separator 作为文件分隔符
            String directoryPath = "/apps/upload";
            File directory = new File(directoryPath);
            if (!directory.exists()) {
                if (directory.mkdirs()) {
                    System.out.println("目录创建成功");
                } else {
                    System.out.println("目录创建失败");
                }
            }
            // 构建完整的文件存储路径
            fileName = directoryPath + fileName;
            // 保存文件
            try (BufferedInputStream in = new BufferedInputStream(new URL(url).openStream());
                 FileOutputStream fileOutputStream = new FileOutputStream(fileName)) {
                byte[] dataBuffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = in.read(dataBuffer, 0, 1024))!= -1) {
                    fileOutputStream.write(dataBuffer, 0, bytesRead);
                }
            } catch (IOException e) {
                // 处理文件下载过程中的异常，如网络错误、文件操作错误等
                System.err.println("文件下载异常: " + e.getMessage());
            }

            String printerName = "NPIE1166C";
            // printPDF(fileName, printerName,LANDSCAPE);
            // 查询配置表 获取打印机地址
            Map querytlirl0314 = new HashMap();
            querytlirl0314.put("segNo", segNo);
            querytlirl0314.put("itemCode", itemCode);
            List<LIRL0314> lirl0314s = dao.query(LIRL0314.QUERY, querytlirl0314);
            if (lirl0314s!= null && lirl0314s.size() > 0) {
                LIRL0314 lirl0314 = lirl0314s.get(0);
                printPDF(fileName, lirl0314.getItemCname(), 9100, 10000);
            }
        } catch (Exception e) {
            // 处理其他通用异常情况
            System.err.println("其他异常: " + e.getMessage());
        }
    }

    private void saveFile(String fileName, byte[] content) {
        try (OutputStream outputStream = new FileOutputStream(new File(fileName))) {
            outputStream.write(content);
        } catch (IOException e) {
            System.err.println("保存文件时出错: " + e.getMessage());
        }
    }

    public static void printPDF(String filePath, String ip, int port, int timeout) {
        boolean connected = false;
        int retries = 5; // 增加重试次数，可根据实际情况调整
        int retryInterval = 3000; // 重试间隔时间，单位为毫秒，可根据实际情况调整
        while (!connected && retries > 0) {
            try {
                // 使用 iText 对 PDF 进行缩放处理
                PdfReader reader = new PdfReader(filePath);
                // 创建一个新的 Document，页面大小设置为横向 A4
                Document document = new Document(PageSize.A4.rotate());

                PdfWriter writer = PdfWriter.getInstance(document, new FileOutputStream("/apps/upload/scaledPDF.pdf"));
                document.open();

                for (int i = 1; i <= reader.getNumberOfPages(); i++) {
                    document.newPage();
                    // 修正 getImportedPage 的使用方式
                    PdfImportedPage importedPage = writer.getImportedPage(reader, i);
                    // 将 PdfImportedPage 转换为 Image 元素
                    Image image = Image.getInstance(importedPage);
                    // 计算缩放比例，使内容适应 A4 尺寸
                    float originalWidth = importedPage.getWidth();
                    float originalHeight = importedPage.getHeight();
                    float scaleX = PageSize.A4.getWidth() / originalWidth;
                    float scaleY = PageSize.A4.getHeight() / originalHeight;
                    float scaleFactor = Math.min(scaleX, scaleY);
                    // 对 Image 元素进行缩放
                    image.scalePercent(scaleFactor * 130);
                    document.add(image);
                }
                document.close();
                writer.close();
                reader.close();

                // 检查网络可达性
                if (isReachable(ip)) {
                    // 连接打印机并发送缩放后的 PDF
                    File scaledFile = new File("/apps/upload/scaledPDF.pdf");
                    Socket socket = new Socket();
                    try {
                        socket.connect(new InetSocketAddress(ip, port), timeout);
                        if (socket.isConnected()) {
                            OutputStream out = socket.getOutputStream();
                            FileInputStream fis = new FileInputStream(scaledFile);
                            byte[] buf = new byte[1024];
                            int len;
                            long totalBytes = scaledFile.length();
                            long bytesSent = 0;
                            // 读取文件并发送
                            while ((len = fis.read(buf))!= -1) {
                                out.write(buf, 0, len);
                                bytesSent += len;
                                System.out.println("已发送 " + bytesSent + " / " + totalBytes + " 字节");
                            }
                            // 告诉服务端，文件已传输完毕
                            socket.shutdownOutput();
                            connected = true; // 成功连接并发送文件后标记为已连接
                            out.close();
                        } else {
                            System.err.println("连接失败，未能连接到打印机。");
                        }
                    } catch (java.net.SocketTimeoutException e) {
                        System.err.println("连接打印机超时，正在重试。重试次数: " + retries);
                        System.err.println("详细错误信息: " + e.getMessage());
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                } else {
                    System.err.println("打印机网络不可达，请检查网络设置。");
                }
            } catch (DocumentException | IOException e) {
                e.printStackTrace();
            }
            retries--;
            if (retries > 0) {
                try {
                    TimeUnit.MILLISECONDS.sleep(retryInterval); // 等待一段时间后重试
                } catch (InterruptedException ex) {
                    Thread.currentThread().interrupt();
                    System.err.println("重试等待被中断。");
                }
            }
        }
        if (!connected) {
            System.err.println("多次重试后仍未能连接到打印机。");
        }
    }

    private static boolean isReachable(String ip) {
        try {
            InetAddress address = InetAddress.getByName(ip);
            return address.isReachable(5000); // 尝试在 5 秒内检查是否可达，可根据实际情况调整
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    /***
     * PDA查询车辆装卸示意图
     *	S_LI_RL_0095
     */
    public EiInfo queryVehicleAllocPack(EiInfo eiInfo){

        EiInfo outInfo = new EiInfo();
        try {
            //获取账套、人员工号
            String segNo = (String) eiInfo.get("segNo"); //账套
//            String allocateVehicleNo = (String) eiInfo.get("allocateVehicleNo"); //配单号
            if (StringUtils.isBlank(segNo)   ) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("账套不能为空！");
                return eiInfo;
            }
            try {
                outInfo = validToken(eiInfo);
                if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                    outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                    return outInfo;
                }
            } catch (Exception e) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("token无效！");
                return outInfo;
            }
            HashMap<Object, Object> hashMap = new HashMap<>();
            hashMap.put("segNo",segNo);
//            hashMap.put("allocateVehicleNo",allocateVehicleNo);
            List<HashMap> packList = dao.query("LIRL0503.queryAllAlocPack", hashMap);
            eiInfo.set("list",packList);
            eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg(e.getMessage());
            return eiInfo;
        }
        return eiInfo;
    }


    /***
     * PDA车辆排序
     *	S_LI_RL_0097
     */
    public EiInfo queryQueueByhandPointId(EiInfo eiInfo){

        EiInfo outInfo = new EiInfo();
        try {
            //获取账套、人员工号
            String segNo = (String) eiInfo.get("segNo"); //账套
            String handPointId = (String) eiInfo.get("handPointId"); //装卸点
            String userId = (String) eiInfo.get("userId"); //登录人工号
            if (StringUtils.isBlank(segNo)) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("账套不能为空！");
                return eiInfo;
            }

            HashMap<Object, Object> hashMap = new HashMap<>();
            hashMap.put("segNo",segNo);
            hashMap.put("handPointId",StringUtils.isNotBlank(handPointId)?handPointId:"");
            List<HashMap> packList = dao.query("LIRL0401.queryQueueByhandPointId", hashMap);
            eiInfo.set("list",packList);
            eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg(e.getMessage());
            return eiInfo;
        }
        return eiInfo;
    }

    /***
     * PDA车辆排序调整
     * S_LI_RL_0098
     */
    public EiInfo updateQueueByhandPointId(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            HashMap result = (HashMap) inInfo.get("result");
            String recRevisor = inInfo.get("userId")==null?"":inInfo.get("userId").toString();
            String recRevisorName = inInfo.get("userName")==null?"":inInfo.get("userName").toString();
            String segNo = inInfo.get("segNo")==null?"":inInfo.get("segNo").toString();
            String sort = inInfo.get("sort")==null?"":inInfo.get("sort").toString();
            String carTraceNo = result.get("carTraceNo")==null?"":result.get("carTraceNo").toString();
            String vehicleNo = result.get("vehicleNo")==null?"":result.get("vehicleNo").toString();
            String recReviseTime = DateUtil.curDateTimeStr14();

            //查询叫号表
            Map<String, Object> hashMapPer = new HashMap<>();
            hashMapPer.put("segNo", segNo);
            hashMapPer.put("status", "20");
            hashMapPer.put("perNo", recRevisor);
            //判断PDA操作人是行车工还是仓库管理人员
            List<LIRL0504> queryLIRL0504 = this.dao.query(LIRL0504.QUERY, hashMapPer);
            if (CollectionUtils.isEmpty((queryLIRL0504))){
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("该操作人不属于库管理员不能调整排队顺序！");
                return inInfo;
            }else {
                String perType = queryLIRL0504.get(0).getPerType();
                if (!"3".equals(perType)){
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg("该操作人不属于库管理员不能调整排队顺序！");
                    return inInfo;
                }
            }

            // 获取所有车辆的排队信息
            Map<String, Object> queryMap = new HashMap<>();
            queryMap.put("segNo", segNo);
            queryMap.put("handPointId", result.get("handPointId"));
            List<HashMap> allVehicles = this.dao.query("LIRL0401.queryQueueByhandPointIdAll", queryMap);

            if (CollectionUtils.isNotEmpty(allVehicles)){
                //要调整顺序的车辆序号
                int count = Integer.parseInt(sort);
                Long queueNumber = (Long) allVehicles.get(count-1).get("queueNumber");
                String vehicleNoNew = (String) allVehicles.get(count-1).get("vehicleNo");
                String carTraceNoNew = (String) allVehicles.get(count-1).get("carTraceNo");

                HashMap<String, Object> updateMap = new HashMap<>();
//                updateMap.put("uuid", uuid);
                updateMap.put("segNo", segNo);
                updateMap.put("queueNumber", queueNumber);
                updateMap.put("carTraceNo", carTraceNo);
                updateMap.put("vehicleNo", vehicleNo);
                updateMap.put("handPointId", result.get("handPointId"));
                updateMap.put("recRevisor", recRevisor);
                updateMap.put("recRevisorName", recRevisorName);
                updateMap.put("recReviseTime", recReviseTime);
                this.dao.update("LIRL0401.updateQueueByhandPointId", updateMap);
                int  queueNumberCount=1;
                for (int i = 0; i < allVehicles.size(); i++){
                    String carTraceNoOld = (String) allVehicles.get(i).get("carTraceNo");
                    if (i!=count-1){
                        if (ObjectUtils.notEqual(carTraceNo,carTraceNoOld)){
                            //判断有没有这个序号
                            if (allVehicles.get(i).get("queueNumber") != null){
                                forMethod(allVehicles, i, queueNumberCount, updateMap);
                                queueNumberCount++;
                            }
                        }
                    }
                }
                //原第二辆车顺序下移
                updateMap.put("queueNumber", queueNumber+1);
                updateMap.put("carTraceNo", carTraceNoNew);
                updateMap.put("vehicleNo", vehicleNoNew);
                this.dao.update("LIRL0401.updateQueueByhandPointId", updateMap);

            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
        }
        return outInfo;
    }

    /**
     * 递归遍历相同序号递增
     * @param allVehicles
     * @param i
     * @param queueNumber
     * @param updateMap
     */
    private void forMethod(List<HashMap> allVehicles, int i, int queueNumber, HashMap<String, Object> updateMap) {
                Long queueNumberNew = (Long) allVehicles.get(i).get("queueNumber");
                updateMap.put("queueNumber", queueNumberNew+queueNumber);
                updateMap.put("carTraceNo", allVehicles.get(i).get("carTraceNo"));
                updateMap.put("vehicleNo", allVehicles.get(i).get("vehicleNo"));
                updateMap.put("handPointId", allVehicles.get(i).get("handPointId"));
                this.dao.update("LIRL0401.updateQueueByhandPointId", updateMap);
    }

    /***
     * PDA离厂回退车辆查询
     *S_LI_RL_0103
     */
    public EiInfo vehicleLeaveFactoryQuery(EiInfo eiInfo){

        try {
            //获取账套、人员工号
            String segNo = (String) eiInfo.get("segNo"); //账套
            if (StringUtils.isBlank(segNo) ) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("账套不能为空！");
                return eiInfo;
            }
            HashMap<String, String> hashMap = new HashMap<>();
            hashMap.put("segNo",segNo);
            hashMap.put("nextTatget","20");
            hashMap.put("flag","20");
            hashMap.put("flag2","20");
            List<LIRL0407> queryLirl0407 = this.dao.query(LIRL0407.QUERY,hashMap);
            eiInfo.set("list",queryLirl0407);
            eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
        }catch (Exception e){
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg(e.getMessage());
            return eiInfo;
        }
        return eiInfo;
    }

    /***
     * 离厂回退
     * S_LI_RL_0104
     */
    public EiInfo leaveFactoryBack(EiInfo eiInfo){
        String segNo = (String) eiInfo.get("segNo"); //账套
        String carTraceNo = (String) eiInfo.get("carTraceNo"); //车辆跟踪号
        String vehicleNo = (String) eiInfo.get("vehicleNo"); //车号
        String currentHandPointId = (String) eiInfo.get("currentHandPointId"); //当前装卸点
        String finishLoadId = (String) eiInfo.get("finishLoadId"); //结束装卸点编码

        if (StringUtils.isBlank(segNo) ||StringUtils.isBlank(vehicleNo)||StringUtils.isBlank(carTraceNo)||StringUtils.isBlank(currentHandPointId)||StringUtils.isBlank(finishLoadId)){
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg("账套、车牌号、车辆跟踪号、当前装卸点、结束装卸点编码不能为空！");
            return eiInfo;
        }

        String in_modi_person = eiInfo.getString("recCreator");//当前登录人
        if (null == eiInfo.get("recCreator") || StringUtils.isBlank(eiInfo.get("recCreator").toString())) {
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg("当前登录人为空，请重试！");
            return eiInfo;
        }
        String in_modi_person_name = eiInfo.getString("recCreatorName");//当前登录人姓名
        if (null == eiInfo.get("recCreatorName") || StringUtils.isBlank(eiInfo.get("recCreatorName").toString())) {
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg("当前登录人姓名为空，请重试！");
            return eiInfo;
        }
        try {
            //更新车辆跟踪表数据，目标装卸点更新成当前装卸点
            Map<String, String> hashMap = new HashMap<>();
            hashMap.put("segNo", segNo);
            hashMap.put("carTraceNo", carTraceNo);
            hashMap.put("targetHandPointId", currentHandPointId);
            hashMap.put("finishLoadId", finishLoadId);
            this.dao.update(LIRL0301.UPDATE_TARGET_HAND_POINT, hashMap);
            //预计作业书+1
            updateHandPointJobNumber(segNo, currentHandPointId, 0, 1, 10);
            //更新结束装卸货状态为下一目标，备注 离厂回退+时间
            hashMap.put("tatgetHandPointId",currentHandPointId);
            hashMap.put("recRevisor",in_modi_person);
            hashMap.put("recRevisorName",in_modi_person_name);
            hashMap.put("recReviseTime",DateUtil.curDateTimeStr14());
            hashMap.put("nextTatget","10");
            hashMap.put("remark","离厂回退:"+DateUtil.curDateTimeStr14());
            hashMap.put("sysRemark","离厂回退:"+DateUtil.curDateTimeStr14());
            this.dao.update(LIRL0407.UPDATE_STATUS, hashMap);
            eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg(e.getMessage());
            return eiInfo;
        }
        return eiInfo;
    }


    /***
     * 未装离厂查询
     *
     * S_LI_RL_0105
     */
    public EiInfo vehicleLeaveFactoryUnloadQuery(EiInfo eiInfo){
        String segNo = eiInfo.getString("segNo");
        if (StringUtils.isBlank(segNo)){
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg("账套为空！");
            return eiInfo;
        }
        List<HashMap> queryLIRL0401=new ArrayList<>();
        HashMap<String, String> stringStringHashMap = new HashMap<>();
        stringStringHashMap.put("segNo", segNo);
        String segNo1 = stringStringHashMap.put("segNo", segNo);
        if ("JC000000".equals(segNo1)){
            queryLIRL0401 = this.dao.query(LIRL0401.QUERY_LEAVING_THE_FACTORY_WITHOUT_LOADING_CQ, stringStringHashMap);
        }else {
            queryLIRL0401 = this.dao.query(LIRL0401.QUERY_LEAVING_THE_FACTORY_WITHOUT_LOADING, stringStringHashMap);
        }
        eiInfo.set("list",queryLIRL0401);
        eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
        return eiInfo;
    }

    /***
     * 未装离厂
     *S_LI_RL_0105
     */
    public EiInfo leaveFactoryUnload(EiInfo eiInfo){
        String segNo = (String) eiInfo.get("segNo");
        String carTraceNo = (String) eiInfo.get("carTraceNo");
        String vehicleNo = (String) eiInfo.get("vehicleNo");
        String handPointId = (String) eiInfo.get("handPointId");
        String flag = (String) eiInfo.get("flag");
        if (StringUtils.isBlank(segNo) ||StringUtils.isBlank(vehicleNo)||StringUtils.isBlank(carTraceNo)){
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg("账套、车辆跟踪号、车牌号为空！");
            return eiInfo;
        }

        String in_modi_person = eiInfo.getString("recCreator");//当前登录人
        if (null == eiInfo.get("recCreator") || StringUtils.isBlank(eiInfo.get("recCreator").toString())) {
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg("当前登录人为空，请重试！");
            return eiInfo;
        }
        String in_modi_person_name = eiInfo.getString("recCreatorName");//当前登录人姓名
        if (null == eiInfo.get("recCreatorName") || StringUtils.isBlank(eiInfo.get("recCreatorName").toString())) {
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg("当前登录人姓名为空，请重试！");
            return eiInfo;
        }
        try {
            HashMap<String, String> stringStringHashMap = new HashMap<>();
            stringStringHashMap.put("segNo",segNo);
            stringStringHashMap.put("carTraceNo",carTraceNo);
            stringStringHashMap.put("vehicleNo",vehicleNo) ;
            if ("排队".equals(flag)){
                //查询预约单
                List<HashMap> query = this.dao.query(LIRL0201.QUERY_REVERSION1, stringStringHashMap);
                stringStringHashMap.put("recRevisor",in_modi_person);
                stringStringHashMap.put("recRevisorName",in_modi_person_name);
                stringStringHashMap.put("recReviseTime",DateUtil.curDateTimeStr14());
                if ("KF000000".equals(segNo)) {
                    stringStringHashMap.put("handPointId", handPointId);
                }
                if (CollectionUtils.isNotEmpty(query)){
                    for (HashMap hashMap : query) {
                        String reservationNumber = MapUtils.getString(hashMap, "reservationNumber");
                        //预约单修改为99
                        hashMap.put("status","99");
                        hashMap.put("recRevisor",in_modi_person);
                        hashMap.put("recRevisorName",in_modi_person_name);
                        hashMap.put("recReviseTime",DateUtil.curDateTimeStr14());
                        hashMap.put("delFlag","0");
                        hashMap.put("remark","未装离厂:"+DateUtil.curDateTimeStr14());
                        hashMap.put("sysRemark","未装离厂:"+DateUtil.curDateTimeStr14());
                        hashMap.put("segNo",segNo);
                        this.dao.update(LIRL0201.UPDATE_STATUS, hashMap);
                    }
                    stringStringHashMap.put("remark","未装离厂:"+DateUtil.curDateTimeStr14());
                    stringStringHashMap.put("sysRemark","未装离厂:"+DateUtil.curDateTimeStr14());
                    stringStringHashMap.put("unloadLeaveFlag","10");
                    //删除排队表
                    this.dao.delete(LIRL0401.DELETE, stringStringHashMap);
                    //删除叫号表
                    this.dao.delete(LIRL0402.DELETE, stringStringHashMap);
                    //删除超时表
                    this.dao.delete(LIRL0403.DELETE, stringStringHashMap);
                    //更新车辆车辆跟踪未装离厂状态
                    stringStringHashMap.put("status","50");
                    this.dao.update(LIRL0301.DELETE_STATUS, stringStringHashMap);
                    if ("KF000000".equals(segNo)) {
                        //登记为撤销
                        stringStringHashMap.put("status","00");
                        this.dao.update(LIRL0302.DELETE_STATUS, stringStringHashMap);
                    }
                    if ("JC000000".equals(segNo)){
                        //撤销配单信息
                        HashMap<String, Object> stringObjectHashMap = new HashMap<>();
                        stringObjectHashMap.put("segNo",segNo);
                        stringObjectHashMap.put("carTraceNo",carTraceNo);
                        stringObjectHashMap.put("status","20");
                        List<LIRL0502> query1 = this.dao.query(LIRL0502.QUERY, stringObjectHashMap);
                        if (CollectionUtils.isNotEmpty(query1)){
                            List<String> collect = query1.stream().distinct().map(lirl0502 -> lirl0502.getAllocateVehicleNo()).collect(Collectors.toList());
                            //更新主项为完成
                            HashMap<String, Object> stringObjectHashMap1 = new HashMap<>();
                            stringObjectHashMap1.put("segNo",segNo);
                            stringObjectHashMap1.put("allocateVehicleNoAdd",collect);
                            stringObjectHashMap1.put("recRevisor",in_modi_person);
                            stringObjectHashMap1.put("recRevisorName",in_modi_person_name);
                            stringObjectHashMap1.put("recReviseTime",DateUtil.curDateTimeStr14());
                            stringObjectHashMap1.put("remark","未装离厂"+DateUtil.curDateTimeStr14());
                            stringObjectHashMap1.put("status","99");
                            //更新子项为完成
                            this.dao.update(LIRL0502.UPDATE_STATUS,stringObjectHashMap1);
                            //更新主项为完成
                            this.dao.update(LIRL0503.UPDATE_STATUS,stringObjectHashMap1);
                        }
                    }
                }else {
                    if ("JC000000".equals(segNo)) {
                        stringStringHashMap.put("remark", "未装离厂:" + DateUtil.curDateTimeStr14());
                        stringStringHashMap.put("sysRemark", "未装离厂:" + DateUtil.curDateTimeStr14());
                        stringStringHashMap.put("unloadLeaveFlag", "10");
                        stringStringHashMap.put("carTraceNo", carTraceNo);
                        stringStringHashMap.put("vehicleNo", vehicleNo);
                        //删除排队表
                        this.dao.delete(LIRL0401.DELETE, stringStringHashMap);
                        //删除叫号表
                        this.dao.delete(LIRL0402.DELETE, stringStringHashMap);
                        //删除超时表
                        this.dao.delete(LIRL0403.DELETE, stringStringHashMap);
                        //更新车辆车辆跟踪未装离厂状态
                        stringStringHashMap.put("status", "50");
                        this.dao.update(LIRL0301.DELETE_STATUS, stringStringHashMap);

                        //撤销配单信息
                        HashMap<String, Object> stringObjectHashMap = new HashMap<>();
                        stringObjectHashMap.put("segNo", segNo);
                        stringObjectHashMap.put("carTraceNo", carTraceNo);
                        stringObjectHashMap.put("status", "20");
                        List<LIRL0502> query1 = this.dao.query(LIRL0502.QUERY, stringObjectHashMap);
                        if (CollectionUtils.isNotEmpty(query1)) {
                            List<String> collect = query1.stream().distinct().map(lirl0502 -> lirl0502.getAllocateVehicleNo()).collect(Collectors.toList());
                            //更新主项为完成
                            HashMap<String, Object> stringObjectHashMap1 = new HashMap<>();
                            stringObjectHashMap1.put("segNo", segNo);
                            stringObjectHashMap1.put("allocateVehicleNoAdd", collect);
                            stringObjectHashMap1.put("recRevisor", in_modi_person);
                            stringObjectHashMap1.put("recRevisorName", in_modi_person_name);
                            stringObjectHashMap1.put("recReviseTime", DateUtil.curDateTimeStr14());
                            stringObjectHashMap1.put("remark", "未装离厂" + DateUtil.curDateTimeStr14());
                            stringObjectHashMap1.put("status", "99");
                            //更新子项为完成
                            this.dao.update(LIRL0502.UPDATE_STATUS, stringObjectHashMap1);
                            //更新主项为完成
                            this.dao.update(LIRL0503.UPDATE_STATUS, stringObjectHashMap1);

                        }
                    }
                }
            }else if ("等待作业".equals(flag)){
                //更新车辆跟踪表状态为40结束装卸货，离厂标记为未装离厂
                stringStringHashMap.put("recRevisor",in_modi_person);
                stringStringHashMap.put("recRevisorName",in_modi_person_name);
                stringStringHashMap.put("recReviseTime",DateUtil.curDateTimeStr14());
                stringStringHashMap.put("remark","未装离厂:"+DateUtil.curDateTimeStr14());
                stringStringHashMap.put("sysRemark","未装离厂:"+DateUtil.curDateTimeStr14());
                stringStringHashMap.put("status","40");
                stringStringHashMap.put("targetHandPointId"," ");
                stringStringHashMap.put("unloadLeaveFlag","10");
                if ("JC000000".equals(segNo)){
                    stringStringHashMap.put("status","50");
                }
                this.dao.update(LIRL0301.DELETE_STATUS, stringStringHashMap);

                if ("KF000000".equals(segNo)){
                //同步预计作业数-1
                updateHandPointJobNumber(segNo, handPointId, 0, 1, 50);
                }

                if ("JC000000".equals(segNo)){
                    //撤销配单信息
                    HashMap<String, Object> stringObjectHashMap = new HashMap<>();
                    stringObjectHashMap.put("segNo",segNo);
                    stringObjectHashMap.put("carTraceNo",carTraceNo);
                    stringObjectHashMap.put("status","20");
                    List<LIRL0502> query1 = this.dao.query(LIRL0502.QUERY, stringObjectHashMap);
                    if (CollectionUtils.isNotEmpty(query1)){
                        List<String> collect = query1.stream().distinct().map(lirl0502 -> lirl0502.getAllocateVehicleNo()).collect(Collectors.toList());
                        //更新主项为完成
                        HashMap<String, Object> stringObjectHashMap1 = new HashMap<>();
                        stringObjectHashMap1.put("segNo",segNo);
                        stringObjectHashMap1.put("allocateVehicleNoAdd",collect);
                        stringObjectHashMap1.put("recRevisor",in_modi_person);
                        stringObjectHashMap1.put("recRevisorName",in_modi_person_name);
                        stringObjectHashMap1.put("recReviseTime",DateUtil.curDateTimeStr14());
                        stringObjectHashMap1.put("remark","未装离厂"+DateUtil.curDateTimeStr14());
                        stringObjectHashMap1.put("status","99");
                        //更新子项为完成
                        this.dao.update(LIRL0502.UPDATE_STATUS,stringObjectHashMap1);
                        //更新主项为完成
                        this.dao.update(LIRL0503.UPDATE_STATUS,stringObjectHashMap1);
                    }
                }
            }
                eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg("未装离厂失败！");
        }

        return eiInfo;
    }

    /***
     * 提单校验验证码
     *
     */
    public EiInfo checkVerificationCode(EiInfo inInfo){
        //判断厂内周转开关是否开启
        String ifPdaOpen = new SwitchUtils().getProcessSwitchValue((String) inInfo.get("segNo"), "IF_PDA_OPEN", dao);
        if ("0".equals(ifPdaOpen)){
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            return inInfo;
        }
        EiInfo outInfo = new EiInfo();
        if (null == inInfo.get("segNo") || StringUtils.isBlank(inInfo.get("segNo").toString()) ||
                null == inInfo.get("warehouseCode") || StringUtils.isBlank(inInfo.get("warehouseCode").toString())) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("业务单元号或仓库为空，请重试！");
            return outInfo;
        }
        if (null == inInfo.get("ladingBillIdList") || StringUtils.isBlank(inInfo.get("ladingBillIdList").toString())) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("提货单列表为空，请重试！");
            return outInfo;
        }
        List<String> ladingBillIdList = (List<String>) inInfo.get("ladingBillIdList");
        if (CollectionUtils.isEmpty(ladingBillIdList)) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("提货单列表为空，请重试！");
            return outInfo;
        }

        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }

        inInfo.set(EiConstant.serviceId,"S_UC_PR_0422");
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;

            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }
        return outInfo;
    }

    /***
     * 提单校验验证码
     *
     */
    public EiInfo verifyPasswordBillPda(EiInfo inInfo){
        //增加开关
        //判断厂内周转开关是否开启
        String ifPdaOpen = new SwitchUtils().getProcessSwitchValue((String) inInfo.get("segNo"), "IF_PDA_OPEN", dao);
        if ("0".equals(ifPdaOpen)){
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            return inInfo;
        }
        EiInfo outInfo = new EiInfo();
        if (null == inInfo.get("segNo") || StringUtils.isBlank(inInfo.get("segNo").toString()) ||
                null == inInfo.get("warehouseCode") || StringUtils.isBlank(inInfo.get("warehouseCode").toString())) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("业务单元号或仓库为空，请重试！");
            return outInfo;
        }
        if (null == inInfo.get("rowList") || !(inInfo.get("rowList") instanceof List)
                || CollectionUtils.isEmpty((List) inInfo.get("rowList"))) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("提单明细为空！");
            return outInfo;
        }

        List<Map> rowMapList = (List) inInfo.get("rowList");
        for (Map rowMap : rowMapList) {
            if (null == rowMap.get("ladingBillId") || StringUtils.isBlank(rowMap.get("ladingBillId").toString())) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("提单为空！");
                return outInfo;
            }
            if (null == rowMap.get("logisticPlanId") || StringUtils.isBlank(rowMap.get("logisticPlanId").toString())) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("物流计划号为空！");
                return outInfo;
            }
            if (null == rowMap.get("ebillPassword") || StringUtils.isBlank(rowMap.get("ebillPassword").toString())) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("提单验证码为空！");
                return outInfo;
            }
        }

        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }

        inInfo.set(EiConstant.serviceId,"S_UC_PR_0423");
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;

            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }
        return outInfo;
    }

    /**
     * PDA查询版本
     * S_LI_RL_0112
     * @param inInfo
     * @return
     */
    public EiInfo getPDAVersion(EiInfo inInfo) {
        if (null == inInfo.get("segNo") || StringUtils.isBlank(inInfo.get("segNo").toString())) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("业务单元为空，请重试！");
            return inInfo;
        }
        HashMap<String, String> stringStringHashMap = new HashMap<>();
        stringStringHashMap.put("segNo",inInfo.get("segNo").toString());
        List query = this.dao.query(XTSS11.QUERY, stringStringHashMap);
        if (!query.isEmpty()) {
            inInfo.set("result",query.get(0));
        } else {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("未查询到版本信息");
            return inInfo;
        }
        inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        return inInfo;
    }

    /**
     * 查询车辆登记信息
     * @param inInfo
     * @return
     * S_LI_RL_0117
     */
    public EiInfo queryInfactoryVehicleReg(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        long l = System.currentTimeMillis(); //获取时间戳效率最高
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String format = dateFormat.format(l);
        Date date = new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        if (null == inInfo.get("segNo") || StringUtils.isBlank(inInfo.get("segNo").toString())) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("业务单元为空，请重试！");
            return inInfo;
        }
        try {
            Map queryBlock = new HashMap();
            String segNo = inInfo.get("segNo")==null?"":inInfo.get("segNo").toString();
            String segName = inInfo.get("segName")==null?"":inInfo.get("segName").toString();
            String status = inInfo.get("status")==null?"":inInfo.get("status").toString();//状态
            String vehicleNo = inInfo.get("vehicleNo")==null?"":inInfo.get("vehicleNo").toString();//车牌号
            String businessType = inInfo.get("businessType")==null?"":inInfo.get("businessType").toString();//业务类型
            queryBlock.put("segNo",segNo);
            queryBlock.put("segName",segName);
            queryBlock.put("status",status);
            queryBlock.put("vehicleNo",vehicleNo);
            queryBlock.put("businessType",businessType);
            if (org.apache.commons.lang.StringUtils.isNotBlank(vehicleNo)){
                if (vehicleNo.contains("\n")){
                    String[] split = vehicleNo.split("\n");
                    List<String> strVehicleNo = Arrays.asList(split);
                    queryBlock.put("vehicleNoStr",strVehicleNo);
                    queryBlock.put("vehicleNo","");
                }

            }
            if ("20".equals(businessType)){
                queryBlock.put("businessType","10");
                queryBlock.put("voucherNumN","1");
            }else if ("60".equals(businessType)){
                queryBlock.put("businessType","20");
                queryBlock.put("voucherNumN","1");
            }else if ("40".equals(businessType)){
                queryBlock.put("businessType","30");
                queryBlock.put("voucherNumN","1");
            }else if ("50".equals(businessType)){
                queryBlock.put("businessType","40");
                queryBlock.put("voucherNumN","1");
            }else if ("70".equals(businessType)) {
                queryBlock.put("businessType","50");
                queryBlock.put("voucherNumN","1");
            }else if ("80".equals(businessType)) {
                queryBlock.put("businessType","60");
                queryBlock.put("voucherNumN","1");
            }
            else if ("10".equals(businessType)){
                queryBlock.put("businessType","10");
                queryBlock.put("handType","10");
            }else if ("30".equals(businessType)){
                queryBlock.put("businessType","30");
                queryBlock.put("handType","30");
            }
            if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
                String massage = "缺少业务单元代码不能查询！";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }
            if ("10".equals(status)){
                queryBlock.put("pendingReview","asc");
            }
            String reservationDateRange = inInfo.get("reservationDateRange")==null?"":inInfo.get("reservationDateRange").toString();
            //预约范围(只看本日预约、只看明日预约、查看所有预约) 10-本日、20-明日
            if ("10".equals(reservationDateRange)) {
                queryBlock.put("reservationDate", format);
            } else if ("20".equals(reservationDateRange)) {
                cal.add(Calendar.DATE, 1);
                format = dateFormat.format(cal.getTime());
                queryBlock.put("reservationDate", format);
            }
            List queryResult = dao.queryAll("LIRL0302.queryInfactoryVehicleRegAll",queryBlock);
            outInfo.set("result",queryResult);
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("查询成功！");
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }
        return outInfo;
    }

    /**
     * 删除车辆登记信息.
     * @param inInfo
     * @return
     * @Service:S_LI_RL_0118
     */
    public EiInfo deleteInfactoryVehicleReg(EiInfo inInfo) {
        EiInfo outInfo1 = new EiInfo();
        if (null == inInfo.get("result") || !(inInfo.get("result") instanceof List)
                || CollectionUtils.isEmpty((List) inInfo.get("result"))) {
            outInfo1.setStatus(EiConstant.STATUS_FAILURE);
            outInfo1.setMsg("车辆登记信息明细为空！");
            return outInfo1;
        }
        try {
            List<HashMap> listHashMap = (List<HashMap>)inInfo.get("result");
            //短信接收人信息
            /*List list2 = new ArrayList();*/
            String mobileNum = "";
            for (HashMap hashMap : listHashMap) {
                String telNum = MapUtils.getString(hashMap, "telNum", "");//<!-- 司机电话 -->
                String driverName = MapUtils.getString(hashMap, "driverName", "");//<!-- 司机名称 -->
                String businessType = MapUtils.getString(hashMap, "businessType", "");//<!-- 司机名称 -->

                if ("20".equals(businessType)){
                    hashMap.put("businessType","10");
                }else if ("60".equals(businessType)){
                    hashMap.put("businessType","20");
                }else if ("40".equals(businessType)){
                    hashMap.put("businessType","30");
                }else if ("50".equals(businessType)){
                    hashMap.put("businessType","40");
                }else if ("70".equals(businessType)){
                    hashMap.put("businessType","50");
                }else if ("80".equals(businessType)){
                    hashMap.put("businessType","60");
                }
                mobileNum = telNum;
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                List<LIRL0302> query = dao.query(LIRL0302.QUERY, map);
                for (LIRL0302 lirl0302 : query) {
                    String status = lirl0302.getStatus();
                    //TODO 使用全局变量
                    if (!MesConstant.Status.K10.equals(status)) {
                        String massage = "只能对待审核状态进行修改";
                        inInfo.setStatus(EiConstant.STATUS_FAILURE);
                        inInfo.setMsg(massage);
                        return inInfo;
                    }
                }
                hashMap.put("status", "99");//记录删除标记
                hashMap.put("delFlag", 0);//记录删除标记
                hashMap.put("recReviseTime",DateUtil.curDateTimeStr14());//更新时间
                //RecordUtils.setRevisor(hashMap);
                this.dao.update("LIRL0302.update", hashMap);

            }
            //驳回发送短信
            //短信内容
            EiInfo outInfo = new EiInfo();
            String content = "审批驳回: 您的登记信息已驳回!";
            outInfo.set("content",content);
            outInfo.set("mobileNum",mobileNum);
            outInfo = SmsSendManager.sendMobile(outInfo);
            if (outInfo.getStatus() == -1) {
                // 调用失败
                throw new RuntimeException("调用平台短信发送服务报错:" + outInfo.getMsg());
            }
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /***
     * PDA盘库倒库
     * 物流服务： 查询库位列表
     * ServiceId:S_LI_RL_0122
     */

    public EiInfo externalInterfaceLocationQueryPda(EiInfo inInfo){

        EiInfo outInfo = new EiInfo();
        log("S_LI_RL_0122查询库位列表调用参数" + inInfo.toJSONString());
        try {
            outInfo = validToken(inInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                return outInfo;
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("token无效！");
            return outInfo;
        }


        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }

        inInfo.set(EiConstant.serviceId,"S_UC_PR_0426");//S_UA_CM_201501
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;

            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }
        return outInfo;
    }

    /***
     * PDA盘库倒库
     * 物流服务： 倒库
     * ServiceId:S_LI_RL_0123
     */

    public EiInfo stockTransferPda(EiInfo inInfo){

        EiInfo outInfo = new EiInfo();
        log("S_LI_RL_0123倒库调用参数" + inInfo.toJSONString());
        try {
            outInfo = validToken(inInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                return outInfo;
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("token无效！");
            return outInfo;
        }


        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }

        inInfo.set(EiConstant.serviceId,"S_UC_PR_0425");//S_UC_PR_230612
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;

            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }
        return outInfo;
    }


    /***
     * PDA盘库倒库
     * 物流服务： 查询库存捆包明细
     * ServiceId:S_LI_RL_0124
     */

    public EiInfo queryPackWarehousePda(EiInfo inInfo){

        EiInfo outInfo = new EiInfo();
        log("S_LI_RL_0124查询库存捆包明细调用参数" + inInfo.toJSONString());

        try {
            outInfo = validToken(inInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                return outInfo;
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("token无效！");
            return outInfo;
        }


        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }

        inInfo.set(EiConstant.serviceId,"S_UC_PR_0427");//S_UC_PR_200006
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;

            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }
        return outInfo;
    }

    /***
     * PDA盘库倒库
     * 物流服务： 查询库位信息
     * ServiceId:S_LI_RL_0125
     */

    public EiInfo queryLocationNewPda(EiInfo inInfo){

        EiInfo outInfo = new EiInfo();
        log("S_LI_RL_0125查询库位信息调用参数" + inInfo.toJSONString());

        try {
            outInfo = validToken(inInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                return outInfo;
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("token无效！");
            return outInfo;
        }


        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }

        inInfo.set(EiConstant.serviceId,"S_UC_PR_0401");//S_UC_PR_230623
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;

            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }
        return outInfo;
    }

    /***
     * PDA盘库倒库
     * 物流服务： 查询盘库单S_UC_PR_230613
     * ServiceId:S_LI_RL_0126
     */

    public EiInfo inventoryListQueryPda(EiInfo inInfo){

        EiInfo outInfo = new EiInfo();
        log("S_LI_RL_0126查询盘库单调用参数" + inInfo.toJSONString());

        try {
            outInfo = validToken(inInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                return outInfo;
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("token无效！");
            return outInfo;
        }


        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }

        inInfo.set(EiConstant.serviceId,"S_UC_PR_0430");//S_UC_PR_230613
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;

            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }
        return outInfo;
    }

    /***
     * PDA盘库倒库
     * 物流服务： 查询盘库单明细S_UC_PR_230614
     * ServiceId:S_LI_RL_0127
     */

    public EiInfo inventoryListQueryDetailPda(EiInfo inInfo){

        EiInfo outInfo = new EiInfo();
        log("S_LI_RL_0127查询盘库单明细调用参数" + inInfo.toJSONString());

        try {
            outInfo = validToken(inInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                return outInfo;
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("token无效！");
            return outInfo;
        }


        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }

        inInfo.set(EiConstant.serviceId,"S_UC_PR_0429");//S_UC_PR_230614
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;

            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }
        return outInfo;
    }


    /***
     * PDA盘库倒库
     * 物流服务：盘库查询S_UC_PR_230662
     * ServiceId:S_LI_RL_0128
     */

    public EiInfo queryInventory(EiInfo inInfo){

        EiInfo outInfo = new EiInfo();
        log("S_LI_RL_0128盘库查询调用参数" + inInfo.toJSONString());

        try {
            outInfo = validToken(inInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                return outInfo;
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("token无效！");
            return outInfo;
        }


        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }

        inInfo.set(EiConstant.serviceId,"S_UC_PR_0431");//S_UC_PR_230662
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;

            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }
        return outInfo;
    }

    /***
     * PDA盘库倒库
     * 物流服务： 盘库单完成S_UC_PR_230663
     * ServiceId:S_LI_RL_0129
     */

    public EiInfo inventoryCompleted(EiInfo inInfo){

        EiInfo outInfo = new EiInfo();
        log("S_LI_RL_0129盘库单完成调用参数" + inInfo.toJSONString());

        try {
            outInfo = validToken(inInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                return outInfo;
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("token无效！");
            return outInfo;
        }


        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }

        inInfo.set(EiConstant.serviceId,"S_UC_PR_0432");//S_UC_PR_230663
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;

            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }
        return outInfo;
    }

    /***
     * PDA盘库倒库
     * 物流服务： 盘库S_UC_PR_230615
     * ServiceId:S_LI_RL_0130
     */

    public EiInfo inventoryPda(EiInfo inInfo){

        EiInfo outInfo = new EiInfo();
        log("S_LI_RL_0130盘库调用参数" + inInfo.toJSONString());

        try {
            outInfo = validToken(inInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                return outInfo;
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("token无效！");
            return outInfo;
        }


        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }

        inInfo.set(EiConstant.serviceId,"S_UC_PR_0433");//S_UC_PR_230615
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;

            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }
        return outInfo;
    }

    /***
     * PDA盘库倒库
     * 物流服务： 添加用户缓存S_UC_PR_270101
     * ServiceId:S_LI_RL_0131
     */

    public EiInfo insertUserCache(EiInfo inInfo){

        EiInfo outInfo = new EiInfo();
        log("S_LI_RL_0131添加用户缓存调用参数" + inInfo.toJSONString());

        try {
            outInfo = validToken(inInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                return outInfo;
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("token无效！");
            return outInfo;
        }


        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }

        inInfo.set(EiConstant.serviceId,"S_UC_PR_0434");//S_UC_PR_270101
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;

            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }
        return outInfo;
    }

    /***
     * PDA盘库倒库
     * 物流服务： 查询用户缓存S_UC_PR_270102
     * ServiceId:S_LI_RL_0132
     */

    public EiInfo queryUserCache(EiInfo inInfo){

        EiInfo outInfo = new EiInfo();
        log("S_LI_RL_0132查询用户缓存调用参数" + inInfo.toJSONString());

        try {
            outInfo = validToken(inInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                return outInfo;
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("token无效！");
            return outInfo;
        }


        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }

        inInfo.set(EiConstant.serviceId,"S_UC_PR_0435");//S_UC_PR_270102
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;

            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }
        return outInfo;
    }

    /***
     * PDA盘库倒库
     * 物流服务： 删除用户缓存S_UC_PR_270103
     * ServiceId:S_LI_RL_0133
     */

    public EiInfo deleteUserCache(EiInfo inInfo){

        EiInfo outInfo = new EiInfo();
        log("S_LI_RL_0133删除用户缓存调用参数" + inInfo.toJSONString());

        try {
            outInfo = validToken(inInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                return outInfo;
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("token无效！");
            return outInfo;
        }


        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }

        inInfo.set(EiConstant.serviceId,"S_UC_PR_0436");//S_UC_PR_270103
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;

            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }
        return outInfo;
    }

    /***
     * PDA盘库倒库
     * 物流服务： 查询盘库单信息S_UC_PR_230634
     * ServiceId:S_LI_RL_0134
     */

    public EiInfo queryInventoryPackPda(EiInfo inInfo){

        EiInfo outInfo = new EiInfo();
        log("S_LI_RL_0134查询盘库单信息调用参数" + inInfo.toJSONString());

        try {
            outInfo = validToken(inInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                return outInfo;
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("token无效！");
            return outInfo;
        }


        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }

        inInfo.set(EiConstant.serviceId,"S_UC_PR_0437");//S_UC_PR_230658
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;

            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }
        return outInfo;
    }

    /***
     * PDA盘库倒库
     * 物流服务： 查询盘库单捆包明细S_UC_PR_230658
     * ServiceId:S_LI_RL_0135
     */

    public EiInfo queryNoSingleDiskPack(EiInfo inInfo){

        EiInfo outInfo = new EiInfo();
        log("S_LI_RL_0135查询盘库单捆包明细调用参数" + inInfo.toJSONString());

        try {
            outInfo = validToken(inInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                return outInfo;
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("token无效！");
            return outInfo;
        }


        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }

        inInfo.set(EiConstant.serviceId,"S_UC_PR_0438");//S_UC_PR_230658
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;

            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }
        return outInfo;
    }

    /***
     * PDA盘库倒库
     * 物流服务： 无单盘库S_UC_PR_230619
     * ServiceId:S_LI_RL_0136
     */

    public EiInfo noBillInventory(EiInfo inInfo){

        EiInfo outInfo = new EiInfo();
        log("S_LI_RL_0136无单盘库调用参数" + inInfo.toJSONString());

        try {
            outInfo = validToken(inInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                return outInfo;
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("token无效！");
            return outInfo;
        }


        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }

        inInfo.set(EiConstant.serviceId,"S_UC_PR_0439");//S_UC_PR_230619
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;

            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }
        return outInfo;
    }


    /***
     * pad 操作开始装卸货选择行车工
     *
     * S_LI_RL_0137
     */
    public EiInfo queryPreInfo(EiInfo inInfo){
        String segNo = (String) inInfo.get("segNo");
        if (StringUtils.isBlank(segNo)){
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("账套号不能为空！");
            return inInfo;
        }
        //查询行车工
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("segNo",segNo);
//        hashMap.put("notPreType","2");
        hashMap.put("status","20");
        List<LIRL0504> queryLIRL0504 = this.dao.query(LIRL0504.QUERY, hashMap);
        List<Map> maps = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(queryLIRL0504)){
            for (LIRL0504 lirl0504 : queryLIRL0504) {
                maps.add(lirl0504.toMap());
            }
        }
        inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        inInfo.set("list",maps);
        return inInfo;
    }

    /***
     * PDA出库
     * 物流服务： PDA出库确认
     * ServiceId:	S_LI_RL_0145
     */

    public EiInfo exWarehousePdaNewCQ(EiInfo inInfo){

        EiInfo outInfo = new EiInfo();
        String factoryBuilding = (String) inInfo.get("factoryBuilding");//厂房代码
        String factoryArea = (String) inInfo.get("factoryArea");//厂区代码
        String allocateVehicleNo = (String) inInfo.get("allocateVehicleNo");//配车单号
        if (StringUtils.isBlank(factoryBuilding)){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("厂房不能为空！");
            return outInfo;
        }
        //1厂逻辑
        if ("F1".equals(factoryBuilding)&&"CQBG".equals(factoryArea)){
            inInfo = outputInfoF1(inInfo, outInfo);
            if (inInfo.getStatus() == EiConstant.STATUS_FAILURE){
                return inInfo;
            }
        }else {
            String validString = validExWarehousingParam(inInfo.getAttr(),inInfo);
            if (!StringUtils.isBlank(validString)) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg(validString);
                return outInfo;
            }

            try {
                outInfo = validToken(inInfo);
                if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                    outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                    return outInfo;
                }
            } catch (Exception e) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("token无效！");
                return outInfo;
            }

        }
        //IMC业务出库
        inInfo.set("teamId","10");
        inInfo.set("workingShift","10");
        // 打印标记,不需要自动打印. 自动打印传1或者不传
        String printWhetherToGoImc = new SwitchUtils().getProcessSwitchValue((String) inInfo.get("segNo"), "PRINT_WHETHER_TO_GO_IMC", dao);
        if ("1".equals(printWhetherToGoImc)) {
            inInfo.set("printFlag", "2");
        }
        inInfo.set(EiConstant.serviceId,"S_UC_PR_0404");
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;

            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }
        List<Map> rowMapList = (List) inInfo.get("rowList");
        for (Map map : rowMapList) {
            String packId = String.valueOf(map.get("packId"));
            //更改捆包状态
            HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
            objectObjectHashMap.put("segNo",inInfo.get("segNo"));
//            objectObjectHashMap.put("allocateVehicleNo",allocateVehicleNo);
            objectObjectHashMap.put("packId",packId);
            objectObjectHashMap.put("allocateVehicleNo",allocateVehicleNo);
            this.dao.update(LIRL0503.UPDATE_PACK_STATUS,objectObjectHashMap);
        }

        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }
        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }
        return outInfo;
    }

    /***
     * PDA出库
     * 物流服务： 重庆一厂出库
     *
     */
    public static EiInfo outputInfoF1(EiInfo inInfo, EiInfo outInfo) {
        if (null == inInfo.get("segNo") || StringUtils.isBlank(inInfo.get("segNo").toString())) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("业务单元号为空！");
            return outInfo;
        }

        if (null == inInfo.get("warehouseCode") || StringUtils.isBlank(inInfo.get("warehouseCode").toString()) ||
                null == inInfo.get("warehouseName") || StringUtils.isBlank(inInfo.get("warehouseName").toString())) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("仓库编码或名称为空！");
            return outInfo;
        }

        if (null == inInfo.get("vehicleNo") || StringUtils.isBlank(inInfo.get("vehicleNo").toString())) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("车牌号为空！");
            return outInfo;
        }
        //
        List<HashMap> hashMapList= (List<HashMap>) inInfo.get("rowList");
        if (CollectionUtils.isEmpty(hashMapList)){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("出库参数不能为空！");
            return outInfo;
        }
        List<String> collectVoucherNum = hashMapList.stream().map(map -> (String) map.get("voucherNum")).collect(Collectors.toList());
        List<String> collectPackId = hashMapList.stream().map(map -> (String) map.get("packId")).collect(Collectors.toList());
        //去重
        List<String> distVoucherNum = collectVoucherNum.stream().distinct().collect(Collectors.toList());
        List<String> distPackId = collectPackId.stream().distinct().collect(Collectors.toList());
        EiInfo eiInfo = new EiInfo();
        //调用查询捆包明细服务S_LI_RL_0041
        inInfo.set("segNo", inInfo.get("segNo"));
        inInfo.set("segNo", inInfo.get("segNo"));
        inInfo.set("warehouseCode", inInfo.get("warehouseCode"));
        inInfo.set("ladingBillIdList",distVoucherNum);
        //插入车辆排序表
        inInfo.set(EiConstant.serviceName, "LIRLInterfacePda");
        inInfo.set(EiConstant.methodName, "queryLadingBillWeight");
        eiInfo = XLocalManager.call(inInfo);
        if (eiInfo.getStatus() == EiConstant.STATUS_FAILURE){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(eiInfo.getMsg());
            return outInfo;
        }
        List<Map> rowMapList = new ArrayList<>();
        String outJSON = eiInfo.getString("result");
        if (StringUtils.isNotEmpty(outJSON)) {
            Map map2 = JSONObject.fromObject(outJSON);
            List<HashMap> list1 = (List) map2.get("packList");
            if (list1 != null && list1.size() > 0) {
                for (String packId : distPackId) {
                    for (Map map : list1) {
                        map.put("carTraceNo",inInfo.get("carTraceNo"));
                        map.put("vehicleNo",inInfo.get("vehicleNo"));
                        if (packId.equals(map.get("packId"))) {
                            rowMapList.add(map);
                        }
                    }
                }
            }
        }
        inInfo.set("rowList", rowMapList);
        return inInfo;
    }

    /***
     * 重庆查询IMOM库位信息
     *
     * 	S_LI_RL_0146
     */
    public EiInfo queryImomLocationInfo(EiInfo inInfo){
        String segNo=(String) inInfo.get("segNo");
        String warehouseCode=(String) inInfo.get("warehouseCode");
        String locationId = (String) inInfo.get("locationId");
        if (StringUtils.isBlank(segNo)||StringUtils.isBlank(warehouseCode)){
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("账套号或库位不能为空！");
            return inInfo;
        }
        HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("segNo",segNo);
        objectObjectHashMap.put("warehouseCode",warehouseCode);
        objectObjectHashMap.put("locationId",locationId);
        objectObjectHashMap.put("status","10");
        List<LIDS0601> queryLIDS0601 = this.dao.query(LIDS0601.QUERYInPDA, objectObjectHashMap);
        List<Map> queryLIDS0601Map = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(queryLIDS0601)){
            for (LIDS0601 lids0601 : queryLIDS0601) {
                queryLIDS0601Map.add(lids0601.toMap());
            }
        }
        inInfo.set("result",queryLIDS0601Map);
        inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        return inInfo;
    }

    /***
     * 点击卸货确认判断车辆是否是开始装卸货状态
     * S_LI_RL_0147
     */
    public EiInfo checkVehicleStatus(EiInfo inInfo){
        String segNo = (String) inInfo.get("segNo");
        String vehicleNo = (String) inInfo.get("vehicleNo");
        String carTraceNo = (String) inInfo.get("carTraceNo");
        HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("segNo",segNo);
        objectObjectHashMap.put("vehicleNo",vehicleNo);
        objectObjectHashMap.put("carTraceNo",carTraceNo);
        List<LIRL0301> query = this.dao.query(LIRL0301.QUERY, objectObjectHashMap);
        if (CollectionUtils.isNotEmpty(query)){
            String status = query.get(0).getStatus();
            if (!"30".equals(status)){
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("当前车辆状态不为开始装卸货状态，请先点击开始卸货按钮！");
                return inInfo;
            }
        }else {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("车辆跟踪信息不存在！");
            return inInfo;
        }
        inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        return inInfo;
    }

    /***
     * PDA查询盘库单明细
     * 物流服务： 查询盘库单明细 S_UC_PR_230633
     * ServiceId:S_LI_RL_0149
     */

    public EiInfo inventoryListQueryDetailNoPda(EiInfo inInfo){

        EiInfo outInfo = new EiInfo();
        log("S_LI_RL_0149查询盘库单明细调用参数" + inInfo.toJSONString());

        try {
            outInfo = validToken(inInfo);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                return outInfo;
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("token无效！");
            return outInfo;
        }


        Boolean isRefreshToken = Boolean.FALSE;
        String tokenRefresh = "";
        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
            isRefreshToken = Boolean.TRUE;
            tokenRefresh = outInfo.get("accessToken").toString();
        }

        inInfo.set(EiConstant.serviceId,"S_UC_PR_0440");//S_UC_PR_230633
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;

            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

        if (isRefreshToken) {
            outInfo.setStatus(STATUS_UPDATE_TOKEN);
            outInfo.set("accessToken", tokenRefresh);
        }
        return outInfo;
    }



    /***
     * PDA出库 - 优化版本
     * 物流服务： PDAIMC不出库，生成装载实绩
     * ServiceId:S_LI_RL_0152
     */
    public EiInfo exWarehousePdaCQ(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        String factoryBuilding = (String) inInfo.get("factoryBuilding"); // 厂房代码
        String segNo = String.valueOf(inInfo.get("segNo"));
        String vehicleNo = String.valueOf(inInfo.get("vehicleNo"));

        if (StringUtils.isBlank(factoryBuilding)) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("厂房不能为空不能为空！");
            return outInfo;
        }

        List<HashMap> list = (List) inInfo.getAttr().get("rowList");
        if (CollectionUtils.isEmpty(list)) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("出库明细不能为空！");
            return outInfo;
        }

        // 判断是否是无计划入库的材料
        HashMap<String, Object> lirl0308HashMap = new HashMap<>();
        lirl0308HashMap.put("carTraceNo", list.get(0).get("carTraceNo"));
        lirl0308HashMap.put("vehicleNo", vehicleNo);
        lirl0308HashMap.put("segNo", segNo);
        List<LIRL0301> listLIRL0301 = this.dao.query(LIRL0301.QUERY, lirl0308HashMap);
        if (CollectionUtils.isEmpty(listLIRL0301)) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("车辆跟踪信息不存在，请检查!");
            return inInfo;
        }

        // 按提单号分组并计算重量
        Map<String, BigDecimal> voucherWeightMap = new HashMap<>();
        Map<String, String> billingMethodMap = new HashMap<>(); // 存储提单的计费方式

        // 获取所有不重复的提单号
        List<String> voucherNumList = list.stream()
                .map(map -> (String) map.get("voucherNum"))
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(voucherNumList)) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("提单号不能为空！");
            return outInfo;
        }

        //日志打印
        log("S_LI_RL_0152出库明细调用参数：" + inInfo.toJSONString());
        log("S_LI_RL_0152本次出库所有提单号：" + voucherNumList);

        // 获取每个提单的总重量和计费方式
        String xplatToken = TokenUtils.getXplatToken();
        for (String ladingBillId : voucherNumList) {
            // 首先判断是否以BL开头，不是则跳过
            if (!ladingBillId.startsWith("BL")) {
                continue;
            }

            // 调用服务查询提单信息
            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("segNo", segNo);
            queryParams.put("ladingBillId", ladingBillId);
            queryParams.put("ladingBillStatus", "50");

            EiInfo queryInfo = new EiInfo();
            queryInfo.set(EiConstant.serviceId, "S_UV_SL_9020");
            queryInfo.set("main", queryParams);

            EiInfo billInfo = EServiceManager.call(queryInfo, xplatToken);
            if (billInfo.getStatus() == -1) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("开单中心返回报错:" + billInfo.getMsg());
                return outInfo;
            }

            List<HashMap> billResults = (List<HashMap>) billInfo.get("result");
            if (CollectionUtils.isEmpty(billResults)) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("提单 " + ladingBillId + " 不存在，请检查!");
                return outInfo;
            }

            // 获取提单计费方式
            String billingMethod = (String) billResults.get(0).get("billingMethod");
            billingMethodMap.put(ladingBillId, billingMethod);

            // 只有非按捆包提单才需要检查重量
            if ("10".equals(billingMethod)) {
                continue; // 按捆包提单跳过
            }

            // 执行重量检查
            String totalWeightStr = (String) billResults.get(0).get("totalWeight");
            BigDecimal totalWeight = new BigDecimal(totalWeightStr);

            // 累加当前提单号的所有捆包重量
            BigDecimal currentVoucherWeight = list.stream()
                    .filter(map -> ladingBillId.equals(map.get("voucherNum")))
                    .map(map -> new BigDecimal(String.valueOf(map.get("netWeight"))))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            voucherWeightMap.put(ladingBillId, currentVoucherWeight);

            // 查询行车工已扫捆包重量
            lirl0308HashMap.put("ladingBillId", ladingBillId);
            List<HashMap> queryLIRL0308 = this.dao.query(LIRL0308.QUERY_WEIGHT, lirl0308HashMap);

            BigDecimal previousWeight = BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(queryLIRL0308)) {
                String totalWeight1 = MapUtils.getString(queryLIRL0308.get(0), "weightAdd", "0");
                previousWeight = new BigDecimal(totalWeight1);
            }

            // 检查当前扫描重量 + 已扫描重量是否超过提单总重量
            if (currentVoucherWeight.add(previousWeight).compareTo(totalWeight) > 0) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("提单 " + ladingBillId + " 总重:" + totalWeight
                        + "，已扫描重量:" + previousWeight
                        + "，当前扫描重量:" + currentVoucherWeight
                        + "，累计超过提单总重，请检查!");
                return outInfo;
            }
        }

        // 插入出入库实绩表
        for (HashMap hashMap : list) {
                        Double netWeight = MapUtils.getDouble(hashMap, "netWeight");
            hashMap.put("unitCode", MapUtils.getString(hashMap, "segNo"));
            hashMap.put("putinId", "");
            hashMap.put("putoutId", " ");
            hashMap.put("voucherNum", hashMap.get("voucherNum"));
            hashMap.put("driverName", listLIRL0301.get(0).getDriverName());
            hashMap.put("driverTel", listLIRL0301.get(0).getTelNum());
            hashMap.put("driverIdentity", listLIRL0301.get(0).getIdCard());
            hashMap.put("deliverType", " ");
            hashMap.put("deliverName", " ");
            hashMap.put("matInnerId", hashMap.get("matInnerId"));
            hashMap.put("packId", hashMap.get("packId"));
            hashMap.put("factoryOrderNum", hashMap.get("factoryOrderNum"));
            hashMap.put("prodTypeId", hashMap.get("prodTypeId"));
            hashMap.put("shopsign", hashMap.get("shopsign"));
            hashMap.put("prodTypeName", hashMap.get("prodTypeDesc"));
            hashMap.put("specDesc", hashMap.get("specDesc"));
            hashMap.put("weight", new BigDecimal(netWeight));
            hashMap.put("quantity",  com.baosight.imom.common.utils.MapUtils.getInt(hashMap,"pieceNum"));
            hashMap.put("customerId", hashMap.get("settleUserNum"));
            hashMap.put("customerName", hashMap.get("settleUserName"));
            hashMap.put("vehicleNo", inInfo.get("vehicleNo"));
            hashMap.put("putInOutFlag", "20");
            hashMap.put("recCreator", inInfo.get("userId"));
            hashMap.put("recCreatorName", inInfo.get("userName"));
            hashMap.put("recCreateTime", DateUtil.curDateTimeStr14());
            hashMap.put("recRevisor", inInfo.get("userId"));
            hashMap.put("recRevisorName", inInfo.get("userName"));
            hashMap.put("recReviseTime", DateUtil.curDateTimeStr14());
            hashMap.put("archiveFlag", "0");
            hashMap.put("delFlag", "0");
            hashMap.put("remark", "行车工扫描");
            hashMap.put("sysRemark", "行车工扫描");
            hashMap.put("uuid", UUIDUtils.getUUID());
            hashMap.put("tenantId", " ");
            hashMap.put("putoutDate", DateUtil.curDateTimeStr14());
            hashMap.put("signatureFlag", "0");
            hashMap.put("finalDestination", " ");
            hashMap.put("finalStationLongitude", " ");
            hashMap.put("finalStationLatitude", " ");
            hashMap.put("destSpotAddr", " ");
            hashMap.put("perNo", " ");
            hashMap.put("perName", " ");
            hashMap.put("handPointId", hashMap.get("currentHandPointId"));
            hashMap.put("loadId", hashMap.get("loadId"));
            this.dao.insert(LIRL0308.INSERT, hashMap);

//            //更改捆包状态
//            HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
//            objectObjectHashMap.put("segNo",inInfo.get("segNo"));
////            objectObjectHashMap.put("allocateVehicleNo",allocateVehicleNo);
//            objectObjectHashMap.put("packId",hashMap.get("packId"));
//            this.dao.update(LIRL0503.UPDATE_PACK_STATUS,objectObjectHashMap);
        }

        outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        return outInfo;
    }

    /***
     * 根据配单信息查询提单信息
     * S_LI_RL_0153
     */
    public EiInfo queryCraneOrderInfoByPackId(EiInfo inInfo){

        String segNo = (String) inInfo.get("segNo");
        String carTraceNo = (String) inInfo.get("carTraceNo");
        //配单号
//        String allocateVehicleNo = (String) inInfo.get("allocateVehicleNo");
        String vehicleNo = (String) inInfo.get("vehicleNo");
        String flag = (String) inInfo.get("flag");
//        if (StringUtils.isNotBlank(allocateVehicleNo)) {
            if (StringUtils.isNotBlank(segNo) || StringUtils.isNotBlank(carTraceNo) ) {
                try {
                    Map<String, Object> objectHashMap = new HashMap<>();
                    objectHashMap.put("segNo", segNo);
                    objectHashMap.put("carTraceNo", carTraceNo);
//                objectHashMap.put("allocateVehicleNo", allocateVehicleNo);
                    objectHashMap.put("vehicleNo", vehicleNo);
                    List<LIRL0502> queryLIRL05021 = this.dao.query(LIRL0502.QUERY, objectHashMap);
                    if (CollectionUtils.isNotEmpty(queryLIRL05021)) {
                        String nextAlcVehicleNo = queryLIRL05021.get(0).getNextAlcVehicleNo();
                        if (StringUtils.isNotBlank(nextAlcVehicleNo)) {
                            objectHashMap.put("segNo", segNo);
                            objectHashMap.put("carTraceNo", carTraceNo);
//                objectHashMap.put("allocateVehicleNo", allocateVehicleNo);
                            objectHashMap.put("vehicleNo", vehicleNo);
                            objectHashMap.put("allocType", "10");
                        }
                        objectHashMap.put("allocType", "10");
                    }
                    if ("10".equals(flag)){
                        objectHashMap.put("statusIn", "");
                    }else {
                        objectHashMap.put("statusIn", "20");
                    }
                    List<HashMap> queryLIRL0502 = this.dao.query(LIRL0502.QUERY_ALL_VOUCHER_NUM, objectHashMap);
                    inInfo.set("list", queryLIRL0502);
                } catch (Exception e) {
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg("查询提单信息失败！");
                }
            }
        inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        return inInfo;
    }

    /***
     * 校验行车工扫描捆包与实际出库是否一致
     * S_LI_RL_0155
     */
    public EiInfo validateCraneOrderInfoByPackId(EiInfo inInfo){
        String segNo = (String) inInfo.get("segNo");
        String carTraceNo = (String) inInfo.get("carTraceNo");
        String vehicleNo = (String) inInfo.get("vehicleNo");
        List<HashMap> list = (List) inInfo.getAttr().get("rowList");
        if (CollectionUtils.isEmpty(list)){
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("出库明细为空！");
            return inInfo;
        }
        if (StringUtils.isNotBlank(segNo) || StringUtils.isNotBlank(carTraceNo)|| StringUtils.isNotBlank(vehicleNo)){
            try {
                HashMap<String, Object> objectHashMap = new HashMap<>();
                objectHashMap.put("segNo",segNo);
                objectHashMap.put("carTraceNo",carTraceNo);
                objectHashMap.put("vehicleNo",vehicleNo);
                objectHashMap.put("putInOutFlag","20");
                objectHashMap.put("putoutIdNull","20");
                //查询行车工扫描的捆包
                List<LIRL0308> queryLIRL0308 = this.dao.query(LIRL0308.QUERY, objectHashMap);
                if (CollectionUtils.isNotEmpty(queryLIRL0308)){
                    //实物捆包
                    List<String> packIdS = list.stream().map(map -> (String)map.get("packId")).collect(Collectors.toList());
                    //行车捆包
                    List<String> packIdH = queryLIRL0308.stream().map(lirl0308 -> lirl0308.getPackId()).collect(Collectors.toList());

                    // 找出不同的捆包号
                    List<String> differentPackIds = new ArrayList<>();

                    // 找出在实物捆包中但不在行车捆包中的捆包
                    List<String> inSNotInH = packIdS.stream()
                            .filter(packId -> !packIdH.contains(packId))
                            .collect(Collectors.toList());

                    // 找出在行车捆包中但不在实物捆包中的捆包
                    List<String> inHNotInS = packIdH.stream()
                            .filter(packId -> !packIdS.contains(packId))
                            .collect(Collectors.toList());

                    // 组装不同的捆包信息
                    //更新差异捆包
                    HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
                    objectObjectHashMap.put("segNo",segNo);
                    objectObjectHashMap.put("carTraceNo",carTraceNo);
                    objectObjectHashMap.put("vehicleNo",vehicleNo);

                    if (!inSNotInH.isEmpty()) {
                        differentPackIds.add(String.join(", ", inSNotInH));
                        objectObjectHashMap.put("putoutIdS","1");
                        objectObjectHashMap.put("varianceDetails","仓库扫描多出的捆包");
                        objectObjectHashMap.put("differentPackIds",differentPackIds);
                        this.dao.update(LIRL0308.UPDATE_VARIANCE_DETAILS,objectObjectHashMap);
                    }
                    if (!inHNotInS.isEmpty()) {
                        differentPackIds.add(String.join(", ", inHNotInS));
                        objectObjectHashMap.put("putoutIdH","1");
                        objectObjectHashMap.put("varianceDetails","行车工扫描多出的捆包");
                        objectObjectHashMap.put("differentPackIds",inHNotInS);
                        this.dao.update(LIRL0308.UPDATE_VARIANCE_DETAILS,objectObjectHashMap);
                    }

//                // 将不同的捆包信息设置到返回结果中
//                if (!differentPackIds.isEmpty()) {
//                    inInfo.set("list", differentPackIds);
//                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
//                    inInfo.setMsg("仓库扫描捆包信息与行车工扫描信息不一致！");
//                }
                    inInfo.set("inSNotInH",inSNotInH);//不存在行车中的捆包
                    inInfo.set("inHNotInS",inHNotInS);//不存在实物中的捆包
                    inInfo.set("packIdH",packIdH); //行车中捆包
                    inInfo.set("packIdS",packIdS); //实物中的捆包
                }
                inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            } catch (Exception e) {
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(e.getMessage());
            }
        }
        inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        return inInfo;
    }

    /***
     * 查询排队中的车辆
     * 	S_LI_RL_0161
     */
    public EiInfo queryWaitingVehicle(EiInfo inInfo){
        String segNo = (String) inInfo.get("segNo");
        String factoryArea = (String) inInfo.get("factoryArea");
        String factoryBuilding = (String) inInfo.get("factoryBuilding");
        String handPointId = (String) inInfo.get("handPointId");
        try {
            HashMap<String, Object> objectHashMap = new HashMap<>();
            objectHashMap.put("segNo",segNo);
            objectHashMap.put("factoryArea",factoryArea);
            objectHashMap.put("factoryBuilding",factoryBuilding);
            objectHashMap.put("handPointId",handPointId);
            List<HashMap> queryMap = this.dao.query(LIRL0304.QUERY_WAIT_CALL, objectHashMap);
            //查询车辆所有的装卸点
            // priorityLevel
            if (CollectionUtils.isNotEmpty(queryMap)){
                for (HashMap hashMap : queryMap) {
                    hashMap.put("factoryArea",factoryArea);
                    hashMap.put("factoryBuilding",factoryBuilding);
                    List<String> queryHandPointMap = this.dao.query(LIRL0304.QUERY_WAIT_ALL_CALL_HAND_POINT_ID, hashMap);
                    hashMap.put("handPointList",queryHandPointMap);
                }
            }
            //
            inInfo.set("list",queryMap);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

    /***
     * 获取车辆信息所有装卸点信息
     * S_LI_RL_0162
     */
    public EiInfo queryVehicleLoadPoint(EiInfo inInfo){
        String segNo = (String) inInfo.get("segNo");
        String vehicleNo = (String) inInfo.get("vehicleNo");
        String carTraceNo = (String) inInfo.get("carTraceNo");
        String factoryArea = (String) inInfo.get("factoryArea");
        String factoryBuilding = (String) inInfo.get("factoryBuilding");
        try {
            HashMap<String, Object> objectHashMap = new HashMap<>();
            objectHashMap.put("segNo",segNo);
            objectHashMap.put("vehicleNo",vehicleNo);
            objectHashMap.put("carTraceNo",carTraceNo);
            objectHashMap.put("factoryArea",factoryArea);
            objectHashMap.put("factoryBuilding",factoryBuilding);
            List<HashMap> queryMap = this.dao.query(LIRL0304.QUERY_WAIT_ALL_CALL, objectHashMap);
            inInfo.set("list",queryMap);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

    /***
     * 一二三四长出库，根据车牌号提单号
     * 	S_LI_RL_0163
     */
    public EiInfo queryVehicleOut(EiInfo inInfo){
        String segNo = (String) inInfo.get("segNo");
        String vehicleNo = (String) inInfo.get("vehicleNo");
        String allocateVehicleNo = (String) inInfo.get("allocateVehicleNo"); //配单号
        String carTraceNo = (String) inInfo.get("carTraceNo"); //配单号
        List<String> voucherNumList = (List<String>) inInfo.get("voucherNumList");
        String factoryArea = (String) inInfo.get("factoryArea");
        String factoryBuilding = (String) inInfo.get("factoryBuilding");
        try {
            Map<String, Object> objectHashMap = new HashMap<>();
            objectHashMap.put("segNo", segNo);
            objectHashMap.put("carTraceNo", carTraceNo);
            objectHashMap.put("allocateVehicleNo", allocateVehicleNo);
            objectHashMap.put("vehicleNo", vehicleNo);
            List<LIRL0502> queryLIRL05021 = this.dao.query(LIRL0502.QUERY, objectHashMap);
            if (CollectionUtils.isNotEmpty(queryLIRL05021)) {
                String nextAlcVehicleNo = queryLIRL05021.get(0).getNextAlcVehicleNo();
                if (StringUtils.isNotBlank(nextAlcVehicleNo)) {
                    objectHashMap.put("allocateVehicleNo", nextAlcVehicleNo);
                }else {
                    objectHashMap.put("allocateVehicleNo", allocateVehicleNo);
                }
            }
            objectHashMap.put("voucherNumList",voucherNumList);
            //查询捆包
            List<Map> queryLIRL0502 = this.dao.query(LIRL0502.QUERY_ALL_VOUCHER_NUM_PACK, objectHashMap); //捆包集合
            //行车工已扫捆包
            List<String> queryLIRL0308 = this.dao.query(LIRL0308.QUERY_ALL_VOUCHER_NUM_PACK, objectHashMap); //捆包集合

            if (CollectionUtils.isNotEmpty(queryLIRL0502)){
                if ("CQBG".equals(factoryArea)&& "F1".equals(factoryBuilding)){
                    //d
                    EiInfo sendInfo = new EiInfo();
                    sendInfo.set(EiConstant.serviceName, "LIDSInterfacesPda");
                    sendInfo.set(EiConstant.methodName, "checkPackagePerformance");
                    sendInfo.set("segNo", inInfo.getString("segNo"));
                    sendInfo.set("packList", queryLIRL0502);
                    inInfo = XLocalManager.call(sendInfo);
                    if (inInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                        inInfo.setStatus(EiConstant.STATUS_SUCCESS);
                        inInfo.set("msg", "未查询到实绩捆包信息！");
                        inInfo.setMsg("未查询到实绩捆包信息");
                        log("调用LIDSInterfacesPda.checkPackagePerformance查询到实绩捆包信息失败：" + sendInfo.getMsg());
                        System.out.println("调用LIDSInterfacesPda.checkPackagePerformance查询到实绩捆包信息失败：" + sendInfo.getMsg());
                    }

                    List<HashMap> list = (List<HashMap>) inInfo.get("result");
                    inInfo.set("list",queryLIRL0502); //配单明细 用来比对
                    inInfo.set("listH",list); //行车实绩
//                    inInfo.set("listYS",queryLIRL0308); //行车实绩
                }else {
                    inInfo.set("list", queryLIRL0502);
//                    inInfo.set("listYS",queryLIRL0308); //行车实绩
                }
            }else {
                inInfo.set("list", queryLIRL0502);
            }
            inInfo.set("listYS",queryLIRL0308); //行车实绩
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        }
        catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }


    /***
     * 一二三四长业务出库，根据车牌号提单号
     * 	S_LI_RL_0164
     */
    public EiInfo queryVehicleOutWarehouse(EiInfo inInfo){
        String segNo = (String) inInfo.get("segNo");
        String vehicleNo = (String) inInfo.get("vehicleNo");
        String allocateVehicleNo = (String) inInfo.get("allocateVehicleNo"); //配单号
        String carTraceNo = (String) inInfo.get("carTraceNo"); //配单号
        List<String> voucherNumList = (List<String>) inInfo.get("voucherNumList");
        String factoryArea = (String) inInfo.get("factoryArea");
        String factoryBuilding = (String) inInfo.get("factoryBuilding");
        try {
            Map<String, Object> objectHashMap = new HashMap<>();
            objectHashMap.put("segNo", segNo);
            objectHashMap.put("carTraceNo", carTraceNo);
            objectHashMap.put("allocateVehicleNo", allocateVehicleNo);
            objectHashMap.put("vehicleNo", vehicleNo);
            List<LIRL0502> queryLIRL05021 = this.dao.query(LIRL0502.QUERY, objectHashMap);
            if (CollectionUtils.isNotEmpty(queryLIRL05021)) {
                String nextAlcVehicleNo = queryLIRL05021.get(0).getNextAlcVehicleNo();
                if (StringUtils.isNotBlank(nextAlcVehicleNo)) {
                    objectHashMap.put("allocateVehicleNo", nextAlcVehicleNo);
                }else {
                    objectHashMap.put("allocateVehicleNo", allocateVehicleNo);
                }
            }
            objectHashMap.put("voucherNumList",voucherNumList);
            //查询捆包
            List<Map> queryLIRL0502 = this.dao.query(LIRL0502.QUERY_ALL_VOUCHER_NUM_ALL_PACK, objectHashMap); //捆包集合
//            if (CollectionUtils.isNotEmpty(queryLIRL0502)){
//                if ("CQBG".equals(factoryArea)&& "F1".equals(factoryBuilding)){
//                    //d
//                    EiInfo sendInfo = new EiInfo();
//                    sendInfo.set(EiConstant.serviceName, "LIDSInterfacesPda");
//                    sendInfo.set(EiConstant.methodName, "generateInventoryData");
//                    sendInfo.set("segNo", inInfo.getString("segNo"));
//                    sendInfo.set("packList", queryLIRL0502);
//                    inInfo = XLocalManager.call(sendInfo);
//                    if (inInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
//                        inInfo.setStatus(EiConstant.STATUS_SUCCESS);
//                        inInfo.set("msg", "此捆包未与行吊实绩关联，请后续使用PDA进行补录。");
//                        inInfo.setMsg("此捆包未与行吊实绩关联，请后续使用PDA进行补录。");
//                        log("调用LIDSInterfacesPda.generateInventoryData补充IMOM库存失败：" + sendInfo.getMsg());
//                        System.out.println("调用LIDSInterfacesPda.generateInventoryData补充IMOM库存失败：" + sendInfo.getMsg());
//                    }
//                    inInfo.set("list",inInfo.get("result"));
//                }else {
//                    inInfo.set("list", queryLIRL0502);
//                }
//            }else {
//                inInfo.set("list", queryLIRL0502);
//            }
            inInfo.set("list", queryLIRL0502);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        }
        catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

    /***
     * 查询下一装卸点
     */

    /***
     * PDA 查询当前车辆所有装卸点信息
     *
     * @Service S_LI_RL_0051
     */

    public EiInfo findAllHandPointIdByPda(EiInfo eiInfo)
    {

        String segNo = (String)eiInfo.get("segNo"); //目标装卸点
        String factoryArea = (String)eiInfo.get("factoryArea"); //仓库代码
        String factoryBuilding = (String)eiInfo.get("factoryBuilding"); //仓库代码
        if (StringUtils.isEmpty(segNo)){
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg("账套为空！");
            return eiInfo;
        }

        //根据目标装卸点查询车辆跟踪表
        HashMap<Object, Object> hashMap = new HashMap<>();
        hashMap.put("segNo",segNo);
        hashMap.put("factoryArea",factoryArea);
        hashMap.put("factoryBuilding",factoryBuilding);
        hashMap.put("status","30");
        List<LIRL0301> listLIRL0301 = this.dao.query(LIRL0304.QUERY_HAND_POINT_INFO, hashMap);
        eiInfo.set("list",listLIRL0301);
        eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
        return eiInfo;

    }

    /***
     * PDA 重庆强制叫号查询车辆信息
     *
     * @Service S_LI_RL_0165
     */
    public EiInfo callNumberByPda(EiInfo eiInfo)
    {
        String segNo = (String)eiInfo.get("segNo");
        try {
            //查询排队中的车辆
            Map<String, Object> hashMap = new HashMap<>();
            hashMap.put("segNo",segNo);
            List query = this.dao.query(LIRL0401.QUERY_FORCE_VEHICLE, hashMap);
            eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
            eiInfo.set("list",query);
        }catch (Exception e){
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg(e.getMessage());
        }
        return eiInfo;
    }


    /***
     * 强制叫号
     *
         * @Service S_LI_RL_0166
     */
    public EiInfo forceCallNumber(EiInfo eiInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //获取账套、车牌号、车辆跟踪号
            String segNo = (String) eiInfo.get("segNo"); //账套
            String vehicleNo = (String) eiInfo.get("vehicleNo"); //车牌号
            String carTraceNo = (String) eiInfo.get("carTraceNo"); //车辆跟踪号
            String handPointId = (String) eiInfo.get("handPointId"); //目标装卸点

            //检查是否已经叫号了
            HashMap<Object, Object> hashMap = new HashMap<>();
            hashMap.put("segNo",segNo);
            hashMap.put("vehicleNo",vehicleNo);
            hashMap.put("carTraceNo",carTraceNo);
            List<LIRL0401> LIRL0401List = this.dao.query(LIRL0401.QUERY_HAND_POINT_ID, hashMap);
            if (LIRL0401List.size()<0&& CollectionUtils.isEmpty(LIRL0401List)){
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("此车辆排队序列中不存在,请重新查询数据!");
                return eiInfo;
            }
            //插入排队备份子表
            for (LIRL0401 lirl0401 : LIRL0401List) {
                HashMap<Object, Object> hashMaplirl0402 = new HashMap<>();
                Map map = lirl0401.toMap();
                hashMaplirl0402.putAll(map);
                RecordUtils.setCreator(hashMap);
                hashMaplirl0402.put("uuid", UUIDUtils.getUUID());
                hashMaplirl0402.put("remark", DateUtil.curDateTimeStr14().concat(":强制叫号"));
                hashMaplirl0402.put("sysRemark", DateUtil.curDateTimeStr14().concat(":强制叫号"));
                hashMaplirl0402.put("queueDate", DateUtil.curDateTimeStr14());
                hashMaplirl0402.put("targetHandPointId",handPointId);
                hashMaplirl0402.put("backDate", DateUtil.curDateTimeStr14());
                this.dao.insert(LIRL0410.INSERT,hashMaplirl0402);
            }
            //插入叫号表，
            HashMap<Object, Object> hashMaplirl0402 = new HashMap<>();
            Map map = LIRL0401List.get(0).toMap();
            hashMaplirl0402.putAll(map);
            RecordUtils.setCreator(hashMap);
            hashMaplirl0402.put("uuid", UUIDUtils.getUUID());
            hashMaplirl0402.put("remark", DateUtil.curDateTimeStr14().concat(":强制叫号"));
            hashMaplirl0402.put("sysRemark", DateUtil.curDateTimeStr14().concat(":强制叫号"));
            hashMaplirl0402.put("queueDate", DateUtil.curDateTimeStr14());
            hashMaplirl0402.put("targetHandPointId",handPointId);
            hashMaplirl0402.put("targetHandPointIdEq",handPointId);
            this.dao.insert(LIRL0402.INSERT,hashMaplirl0402);
            //更新车辆跟踪装卸点
            hashMap.put("targetHandPointId",handPointId);
            this.dao.update(LIRL0301.UPDATE_TARGET_HAND_POINT, hashMap);
            this.dao.delete(LIRL0401.DELETE,map);
            this.dao.delete(LIRL0303.DELETE,map);
            this.dao.delete(LIRL0402.DELETE,map);
            //发送短信
            senMessage(hashMaplirl0402);
            eiInfo.setMsg("强制叫号成功!");
            eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
            eiInfo.setMsg("强制叫号成功!");
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }
        return eiInfo;
    }

    /***
     * 查询叫号中车辆
     *
     * @Service S_LI_RL_0167
     */
    public EiInfo queryCallNumber(EiInfo eiInfo) {
        String segNo = (String) eiInfo.get("segNo");
        String factoryBuilding = (String) eiInfo.get("factoryBuilding");
        String handPointId = (String) eiInfo.get("handPointId");
        try {
            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("segNo",segNo);
            hashMap.put("factoryBuilding",factoryBuilding);
            hashMap.put("handPointId",handPointId);
            List query = this.dao.query(LIRL0402.QUERY_CALL_NUM_VEHICLE_NO, hashMap);
            eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
            eiInfo.set("list",query);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return eiInfo;
    }

    /***
     * 其他装卸点
     *
     * @Service S_LI_RL_0168
     */
    public EiInfo queryOtherHandPoint(EiInfo eiInfo) {
        String segNo = (String) eiInfo.get("segNo");
        String VEHICLE_NO = (String) eiInfo.get("vehicleNo");
        String carTraceNo = (String) eiInfo.get("carTraceNo");
        if (StringUtils.isBlank(VEHICLE_NO)||StringUtils.isBlank(carTraceNo)||StringUtils.isBlank(segNo)){
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg("请车牌号、车辆跟踪号、账套为空!");
            return eiInfo;
        }
        try {
            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("segNo",segNo);
            hashMap.put("vehicleNo",VEHICLE_NO);
            hashMap.put("carTraceNo",carTraceNo);
            List<HashMap> query = this.dao.query(LIRL0304.QUERY_OTHER_HAND_POINT, hashMap);
            eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
            eiInfo.set("list",query);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return eiInfo;
    }

    /***
     * PDA 查询所有装卸点信息
     *
     * @Service S_LI_RL_0169
     */

    public EiInfo findHandPointIdByPdaCq(EiInfo eiInfo){

        String segNo = (String)eiInfo.get("segNo"); //目标装卸点
        String factoryArea = (String)eiInfo.get("factoryArea"); //仓库代码
        String  allocateVehicleNo= (String)eiInfo.get("allocateVehicleNo"); //配单号
        String factoryBuilding = (String)eiInfo.get("factoryBuilding"); //仓库代码
        String vehicleNo = (String)eiInfo.get("vehicleNo"); //车牌号
        String carTraceNo = (String)eiInfo.get("carTraceNo"); //车辆跟踪号
        if (StringUtils.isEmpty(segNo)){
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg("账套为空！");
            return eiInfo;
        }

        //根据配单号车辆牌号查询车辆跟踪表查询配单明细是否卸货完成，装货完成
        if (StringUtils.isNotBlank(allocateVehicleNo)&&StringUtils.isNotEmpty(vehicleNo)){
            //查询该车辆是什么类型

            HashMap<String, Object> stringObjectHashMap = new HashMap<>();
            stringObjectHashMap.put("allocateVehicleNo",allocateVehicleNo);
            stringObjectHashMap.put("vehicleNo",vehicleNo);
            stringObjectHashMap.put("segNo",segNo);
            stringObjectHashMap.put("carTraceNo",carTraceNo);

            //查询该车辆是什么类型
            List<LIRL0301> queryLIRL0301 = this.dao.query(LIRL0301.QUERY,stringObjectHashMap);
            if (CollectionUtils.isNotEmpty(queryLIRL0301)){
                String handType = queryLIRL0301.get(0).getHandType();
                List<LIRL0502> queryLIRL0502 = this.dao.query(LIRL0502.QUERY, stringObjectHashMap);
                if ("30".equals(handType)){
                    if (CollectionUtils.isNotEmpty(queryLIRL0502)){
                        //不为空卸货配单
                        String nextAlcVehicleNo = queryLIRL0502.get(0).getNextAlcVehicleNo();
                        if (StringUtils.isNotEmpty(nextAlcVehicleNo)){
                            //卸货配单
                            //判断卸货捆包是否全部出完
                            HashMap<Object, Object> hashMapLirl0502 = new HashMap<>();
                            hashMapLirl0502.put("allocateVehicleNoIn", allocateVehicleNo);
                            hashMapLirl0502.put("outPackFlag", "0");//去除自带货
                            hashMapLirl0502.put("voucherNumFlag", "0"); //去除无计划
                            hashMapLirl0502.put("statusEq", "20"); //去除无计划
                            hashMapLirl0502.put("voucherNumEq", "20"); //去除无计划
                            hashMapLirl0502.put("segNo", segNo); //去除无计划
                            //判断卸货配单是否全部卸完
                            List<HashMap> query = this.dao.query(LIRL0503.QUERY_ALL_PACK, hashMapLirl0502);
                            if (CollectionUtils.isEmpty(query)){
                                //卸完
                                //根据目标装卸点查询车辆跟踪表
                                HashMap<Object, Object> hashMap = new HashMap<>();
                                hashMap.put("segNo",segNo);
                                hashMap.put("factoryArea",factoryArea);
                                if ("JC000000".equals(segNo)){
                                    hashMap.put("vehicleNo",vehicleNo);
                                    hashMap.put("carTraceNo",carTraceNo);
                                }
                                hashMap.put("factoryBuilding",factoryBuilding);
                                hashMap.put("noAllocateVehicleNo",allocateVehicleNo);
                                hashMap.put("status","30");
                                List<LIRL0301> listLIRL0301 = this.dao.query(LIRL0304.QUERY_HAND_POINT_INFO, hashMap);
                                eiInfo.set("list",listLIRL0301);
                            }else {
                                //获取未扫捆包的装卸点
                                List<String> targetHandPointId = query.stream().distinct().map(item -> (String)item.get("targetHandPointId")).collect(Collectors.toList());
                                List<String> targetHandPointIdNew = new ArrayList<>();
                                for (String s : targetHandPointId) {
                                    if (StringUtils.isNotBlank(s)){
                                        targetHandPointIdNew.add(s);
                                    }
                                }
                                if (CollectionUtils.isNotEmpty(targetHandPointIdNew)){
                                    //根据目标装卸点查询车辆跟踪表
                                    HashMap<Object, Object> hashMap = new HashMap<>();
                                    hashMap.put("segNo", segNo);
                                    hashMap.put("factoryArea", factoryArea);
                                    hashMap.put("handPointIdList", targetHandPointId);
                                    hashMap.put("vehicleNo", vehicleNo);
                                    hashMap.put("carTraceNo", carTraceNo);
                                    hashMap.put("factoryBuilding", factoryBuilding);
                                    hashMap.put("status", "30");
                                    List<LIRL0301> listLIRL0301 = this.dao.query(LIRL0304.QUERY_HAND_POINT_INFO_ALL, hashMap);
                                    eiInfo.set("list",listLIRL0301);
                                }else {
                                    //根据目标装卸点查询车辆跟踪表
                                    HashMap<Object, Object> hashMap = new HashMap<>();
                                    hashMap.put("segNo", segNo);
                                    hashMap.put("factoryArea", factoryArea);
//                                    hashMap.put("handPointIdList", targetHandPointId);
                                    hashMap.put("vehicleNo", vehicleNo);
                                    hashMap.put("carTraceNo", carTraceNo);
                                    hashMap.put("factoryBuilding", factoryBuilding);
                                    hashMap.put("status", "30");
                                    List<LIRL0301> listLIRL0301 = this.dao.query(LIRL0304.QUERY_HAND_POINT_INFO, hashMap);
                                    eiInfo.set("list",listLIRL0301);
                                }
                            }
                        }else {
                            //装货配单，先判断卸货配单是否已经卸完
                            HashMap<Object, Object> hashMapLirl0502 = new HashMap<>();
                            String allocType = queryLIRL0502.get(0).getAllocType();
                            if ("10".equals(allocType)){
                                hashMapLirl0502.put("nextAlcVehicleNo",queryLIRL0502.get(0).getAllocateVehicleNo());
                            }else{
                                hashMapLirl0502.put("allocateVehicleNo",queryLIRL0502.get(0).getAllocateVehicleNo());
                            }
//                            hashMapLirl0502.put("nextAlcVehicleNo",queryLIRL0502.get(0).getAllocateVehicleNo());
                            hashMapLirl0502.put("carTraceNo",carTraceNo);
                            hashMapLirl0502.put("segNo",segNo);
                            hashMapLirl0502.put("status","20");
                            List<LIRL0502> query = this.dao.query(LIRL0502.QUERY, hashMapLirl0502);
                            if (CollectionUtils.isNotEmpty(query)){
                                String allocateVehicleNo1 = query.get(0).getAllocateVehicleNo();
                                //卸货配单
                                //判断卸货捆包是否全部出完
                                hashMapLirl0502 = new HashMap<>();
                                hashMapLirl0502.put("allocateVehicleNoIn", allocateVehicleNo1);
                                hashMapLirl0502.put("outPackFlag", "0");//去除自带货
                                hashMapLirl0502.put("voucherNumFlag", "0"); //去除无计划
                                hashMapLirl0502.put("statusEq", "20"); //去除无计划
                                hashMapLirl0502.put("voucherNumEq", "20"); //去除无计划
                                hashMapLirl0502.put("segNo", segNo); //去除无计划
                                //判断卸货配单是否全部卸完
                                List<Map> query1 = this.dao.query(LIRL0503.QUERY_ALL_PACK, hashMapLirl0502);
                                if (CollectionUtils.isEmpty(query1)){
                                    //卸完
                                    //根据目标装卸点查询车辆跟踪表
                                    HashMap<Object, Object> hashMap = new HashMap<>();
                                    hashMap.put("segNo",segNo);
                                    hashMap.put("factoryArea",factoryArea);
                                    if ("JC000000".equals(segNo)){
                                        hashMap.put("vehicleNo",vehicleNo);
                                        hashMap.put("carTraceNo",carTraceNo);
                                    }
                                    hashMap.put("factoryBuilding",factoryBuilding);
                                    hashMap.put("noAllocateVehicleNo",allocateVehicleNo1);
                                    hashMap.put("status","30");
                                    List<LIRL0301> listLIRL0301 = this.dao.query(LIRL0304.QUERY_HAND_POINT_INFO, hashMap);
                                    eiInfo.set("list",listLIRL0301);
                                }else {
                                    //获取未扫捆包的装卸点
                                    List<String> targetHandPointId = query1.stream().distinct().map(item -> (String)item.get("targetHandPointId")).collect(Collectors.toList());
                                    List<String> targetHandPointIdNew = new ArrayList<>();
                                    for (String s : targetHandPointId) {
                                        if (StringUtils.isNotBlank(s)){
                                            targetHandPointIdNew.add(s);
                                        }
                                    }
                                    if (CollectionUtils.isNotEmpty(targetHandPointIdNew)){
                                        //根据目标装卸点查询车辆跟踪表
                                        HashMap<Object, Object> hashMap = new HashMap<>();
                                        hashMap.put("segNo", segNo);
                                        hashMap.put("factoryArea", factoryArea);
                                        hashMap.put("handPointIdList", targetHandPointId);
                                        hashMap.put("vehicleNo", vehicleNo);
                                        hashMap.put("carTraceNo", carTraceNo);
                                        hashMap.put("factoryBuilding", factoryBuilding);
                                        hashMap.put("status", "30");
                                        List<LIRL0301> listLIRL0301 = this.dao.query(LIRL0304.QUERY_HAND_POINT_INFO_ALL, hashMap);
                                        eiInfo.set("list",listLIRL0301);
                                    }else {
                                        //根据目标装卸点查询车辆跟踪表
                                        HashMap<Object, Object> hashMap = new HashMap<>();
                                        hashMap.put("segNo", segNo);
                                        hashMap.put("factoryArea", factoryArea);
//                                    hashMap.put("handPointIdList", targetHandPointId);
                                        hashMap.put("vehicleNo", vehicleNo);
                                        hashMap.put("carTraceNo", carTraceNo);
                                        hashMap.put("factoryBuilding", factoryBuilding);
                                        hashMap.put("status", "30");
                                        List<LIRL0301> listLIRL0301 = this.dao.query(LIRL0304.QUERY_HAND_POINT_INFO, hashMap);
                                        eiInfo.set("list",listLIRL0301);
                                    }
                                }
                            }
                        }

                    }
                }else if ("20".equals(handType)){
                    //根据目标装卸点查询车辆跟踪表
                    HashMap<Object, Object> hashMap = new HashMap<>();
                    hashMap.put("segNo",segNo);
                    hashMap.put("factoryArea",factoryArea);
                    if ("JC000000".equals(segNo)){
                        hashMap.put("vehicleNo",vehicleNo);
                        hashMap.put("carTraceNo",carTraceNo);
                    }
                    hashMap.put("factoryBuilding",factoryBuilding);
                    hashMap.put("status","30");
                    List<LIRL0301> listLIRL0301 = this.dao.query(LIRL0304.QUERY_HAND_POINT_INFO, hashMap);
                    eiInfo.set("list",listLIRL0301);

                }else if ("10".equals(handType)){
                    //判断已扫捆包
                    HashMap<String, Object> stringObjectHashMap1 = new HashMap<>();
//                    stringObjectHashMap1.put("allocateVehicleNo",allocateVehicleNo);
                    stringObjectHashMap1.put("vehicleNo",vehicleNo);
                    stringObjectHashMap1.put("segNo",segNo);
                    stringObjectHashMap1.put("carTraceNo",carTraceNo);
                    stringObjectHashMap1.put("status","20");
                    List<LIRL0502> query = this.dao.query(LIRL0502.QUERY, stringObjectHashMap1);
                    if (CollectionUtils.isNotEmpty(query)){
                        List<String> collect = query.stream().distinct().map(LIRL0502::getAllocateVehicleNo).collect(Collectors.toList());
                        stringObjectHashMap1.put("allocateVehicleNoAdd",collect);
                        //查询已扫捆包的装卸点
                        List<LIRL0503> query1 = this.dao.query(LIRL0503.QUERY, stringObjectHashMap1);
                        if (CollectionUtils.isNotEmpty(query1)){
                            //获取未装装卸点
                            List<String> collect1 = query1.stream().distinct().map(LIRL0503::getTargetHandPointId).collect(Collectors.toList());
                            //根据目标装卸点查询车辆跟踪表
                            HashMap<Object, Object> hashMap = new HashMap<>();
                            hashMap.put("segNo", segNo);
                            hashMap.put("factoryArea", factoryArea);
                            hashMap.put("handPointIdList", collect1);
                            hashMap.put("vehicleNo", vehicleNo);
                            hashMap.put("carTraceNo", carTraceNo);
                            hashMap.put("factoryBuilding", factoryBuilding);
                            hashMap.put("status", "30");
                            List<LIRL0301> listLIRL0301 = this.dao.query(LIRL0304.QUERY_HAND_POINT_INFO, hashMap);
                            eiInfo.set("list",listLIRL0301);
                        }else {
                            List<LIRL0314> objects = new ArrayList<>();
                            eiInfo.set("list",objects);
                        }
                    }
                }else {
                    //根据目标装卸点查询车辆跟踪表
                    HashMap<Object, Object> hashMap = new HashMap<>();
                    hashMap.put("segNo",segNo);
                    hashMap.put("factoryArea",factoryArea);
                    if ("JC000000".equals(segNo)){
                        hashMap.put("vehicleNo",vehicleNo);
                        hashMap.put("carTraceNo",carTraceNo);
                    }
                    hashMap.put("factoryBuilding",factoryBuilding);
                    hashMap.put("status","30");
                    List<LIRL0301> listLIRL0301 = this.dao.query(LIRL0304.QUERY_HAND_POINT_INFO, hashMap);
                    eiInfo.set("list",listLIRL0301);
                }

            }
        }else {
            //查询当前装卸点
            List<LIRL0411> query=new ArrayList<>();
            HashMap<String, Object> stringObjectHashMap = new HashMap<>();
            stringObjectHashMap.put("segNo",segNo);
            stringObjectHashMap.put("carTraceNo",carTraceNo);
            stringObjectHashMap.put("vehicleNo",vehicleNo);
            List<LIRL0301> query1 = this.dao.query(LIRL0301.QUERY, stringObjectHashMap);
            //查询废料提货的点
            if (CollectionUtils.isNotEmpty(query1)) {
                stringObjectHashMap.put("currentHandPointId",query1.get(0).getCurrentHandPointId());
                stringObjectHashMap.put("status","20");
                query = this.dao.query(LIRL0411.QUERY_ALL, stringObjectHashMap);
            }
            eiInfo.set("list",query);
        }
        eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
        return eiInfo;

    }

    /***
     * 开始卸货查询配单明细
     * S_LI_RL_0172
     */
    public EiInfo queryMatchingOrder(EiInfo inInfo) {

            String segNo = (String) inInfo.get("segNo");
            String vehicleNo = (String) inInfo.get("vehicleNo");
            String allocateVehicleNo = (String) inInfo.get("allocateVehicleNo"); //配单号
            String carTraceNo = (String) inInfo.get("carTraceNo"); //配单号
//            String factoryArea = (String) inInfo.get("factoryArea");
//            String factoryBuilding = (String) inInfo.get("factoryBuilding");
            try {
                Map<String, Object> objectHashMap = new HashMap<>();
                objectHashMap.put("segNo", segNo);
                objectHashMap.put("carTraceNo", carTraceNo);
                objectHashMap.put("allocateVehicleNo", allocateVehicleNo);
                objectHashMap.put("vehicleNo", vehicleNo);
                objectHashMap.put("allocType", "20");
                List<LIRL0502> queryLIRL05021 = this.dao.query(LIRL0502.QUERY, objectHashMap);
                if (CollectionUtils.isEmpty(queryLIRL05021)) {
                    objectHashMap.put("nextAlcVehicleNoIn", allocateVehicleNo);
                    objectHashMap.put("allocateVehicleNo", "");
                    queryLIRL05021 = this.dao.query(LIRL0502.QUERY, objectHashMap);
                }
                List<Map> queryLIRL0502 =  new ArrayList<>();
//                //行车工已扫捆包
//                List<String> queryLIRL0308 = this.dao.query(LIRL0308.QUERY_ALL_VOUCHER_NUM_PACK, objectHashMap); //捆包集合

                if (CollectionUtils.isNotEmpty(queryLIRL05021)){
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("segNo", segNo);
                    hashMap.put("allocateVehicleNo", queryLIRL05021.get(0).getAllocateVehicleNo());
                    hashMap.put("status", "20");
                    //查询配单明细
                    queryLIRL0502 = this.dao.query(LIRL0503.QUERY_ALL_ALLOCATE_VEHICLE_NO, hashMap); //捆包集合
                    inInfo.set("list", queryLIRL0502);
                }else {
                    inInfo.set("list", queryLIRL0502);
                }
                inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            }
            catch (Exception e) {
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(e.getMessage());
            }

        return inInfo;
    }

    /***
     * 定时任务安徽宝钢实时更新当前作业数，预计作业数
     *
     */
    public EiInfo updateCurrentHandPoint(EiInfo eiInfo) {
        try {
            //查询所有的预计作业数据
            HashMap<String, Object> stringObjectHashMap = new HashMap<>();
            stringObjectHashMap.put("status", "30");
            stringObjectHashMap.put("segNo", "KF000000");
            //一部分数据
            List<HashMap> list = this.dao.query(LIRL0304.QUERY_ALL_LADING_INFO, stringObjectHashMap);
            //查询所有生效的装卸点
            List<LIRL0304> listHandPointInfo = this.dao.query(LIRL0304.QUERY, stringObjectHashMap);

            //用 handPointId 字段比对
            Set<String> existHandPointIds = new java.util.HashSet<>();
            for (HashMap l : list) {
                existHandPointIds.add((String) l.get("targetHandPointId"));
            }

            List<LIRL0304> notInList = new java.util.ArrayList<>();
            for (LIRL0304 handPoint : listHandPointInfo) {
                    if (!existHandPointIds.contains(handPoint.getHandPointId())) {
                        notInList.add(handPoint);
                    }
            }
            // 未在预计作业数据中的装卸点数量直接把当前作业数、预计作业数设置为0,状态为：20 可以进车状态
            List<String> handPointList = notInList.stream().map(hand -> hand.getHandPointId()).distinct().collect(Collectors.toList());
            //更新车辆动态跟踪表
            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("handPointList", handPointList);
            hashMap.put("segNo", "KF000000");
            hashMap.put("status","20");
            this.dao.update(LIRL0306.UPDATE_JOBS, hashMap);
            System.out.println("未在预计作业数据中的装卸点数量：" + notInList.size());
            // 如需返回可加：eiInfo.set("notInList", notInList);
            //已存在的装卸点数量，根据状态去拆分10，叫号、20等待、30作业
            Map<String, Integer> countMap = new HashMap<>();
            for (HashMap map : list) {
                String handPointId = (String) map.get("targetHandPointId");
                String status = String.valueOf(map.get("status"));
                Integer count = Integer.valueOf(String.valueOf(map.get("count")));
                String key = handPointId + "_" + status;
                countMap.put(key, countMap.getOrDefault(key, 0) + count);
            }

            // 统计每个handPointId的当前作业数和预计作业数
            Map<String, Integer> expectedMap = new HashMap<>(); // handPointId -> 预计作业数
            Map<String, Integer> allMap = new HashMap<>(); // handPointId -> 预计作业数
            Map<String, Integer> currentMap = new HashMap<>();  // handPointId -> 当前作业数
            for (Map.Entry<String, Integer> entry : countMap.entrySet()) {
                String[] arr = entry.getKey().split("_");
                String handPointId = arr[0];
                String status = arr[1];
                Integer totalCount = entry.getValue();

                HashMap<String, Object> updateMap = new HashMap<>();
                updateMap.put("handPointId", handPointId);
                updateMap.put("segNo", "KF000000");
                if ("20".equals(status)) {
                    updateMap.put("expectedCount", totalCount); // 预计作业数
//                    this.dao.update(LIRL0306.UPDATE, updateMap);
                    expectedMap.put(handPointId, totalCount);
                    allMap.put(handPointId, totalCount);
                } else if ("30".equals(status)) {
                    updateMap.put("currentCount", totalCount); // 当前作业数
//                    this.dao.update(LIRL0306.UPDATE, updateMap);
                    currentMap.put(handPointId, totalCount);
                    allMap.put(handPointId, totalCount);
                }else if ("10".equals(status)){
                    updateMap.put("currentCount", totalCount); // 当前作业数
//                    this.dao.update(LIRL0306.UPDATE, updateMap);
                    expectedMap.put(handPointId, totalCount);
                    allMap.put(handPointId, totalCount);
                }
                //判断当前作业数+预计作业数是否大于最大容纳数，并更新状态
                System.out.println("handPointId=" + handPointId + ", status=" + status + ", count=" + totalCount);
            }

            // 统计并判断是否超最大容纳数
            for (String handPointId : allMap.keySet()) {
                int expected = expectedMap.getOrDefault(handPointId, 0);
                int current = currentMap.getOrDefault(handPointId, 0);
                int total = expected + current;
                // TODO: 获取最大容纳数，假设有方法 getMaxCapacity(handPointId)
                int maxCapacity = getMaxCapacity(handPointId); // 你需要实现此方法或替换为实际获取方式
                if (total >= maxCapacity) {
                    // 超过最大容纳数，更新状态（请补充你的SQL）
                    HashMap<String, Object> statusMap = new HashMap<>();
                    statusMap.put("handPointId", handPointId);
                    statusMap.put("currentJobNumber", current);
                    statusMap.put("preJobNumber", expected);
                    statusMap.put("segNo", "KF000000");
                    statusMap.put("status", "10"); // 这里的状态值请替换为你实际的
                    this.dao.update(LIRL0306.UPDATE_JOB, statusMap);
                    System.out.println("handPointId=" + handPointId + " 超过最大容纳数，已更新状态");
                }else {
                    // 超过最大容纳数，更新状态（请补充你的SQL）
                    HashMap<String, Object> statusMap = new HashMap<>();
                    statusMap.put("handPointId", handPointId);
                    statusMap.put("currentJobNumber", current);
                    statusMap.put("preJobNumber", expected);
                    statusMap.put("segNo", "KF000000");
                    statusMap.put("status", "20"); // 这里的状态值请替换为你实际的
                    this.dao.update(LIRL0306.UPDATE_JOB, statusMap);
                    System.out.println("handPointId=" + handPointId + " 超过最大容纳数，已更新状态");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return eiInfo;
    }

    // 你需要实现或替换此方法
    private int getMaxCapacity(String handPointId) {
        //查询装卸点状态跟踪表
        Map<String, String> hashMap = new HashMap<>();
        hashMap.put("segNo", "KF000000");
        hashMap.put("handPointId", handPointId);
        hashMap.put("status", "30");
        List<LIRL0304> queryLIRL0306 = this.dao.query(LIRL0304.QUERY, hashMap);
        return Integer.valueOf(queryLIRL0306.get(0).getVehicleNumer());
    }

    /***
     * 打印入库质量确认单
     * 	S_LI_RL_0183
     */
    public EiInfo printPutinQualityConfirm(EiInfo eiInfo) {
        String segNo = (String) eiInfo.get("segNo");
        String allocateVehicleNo = (String) eiInfo.get("allocateVehicleNo");
        String carTraceNo = (String) eiInfo.get("carTraceNo");
        String factoryBuilding = (String) eiInfo.get("factoryBuilding");
        if (StringUtils.isNotBlank(allocateVehicleNo)){
            try {
                ArrayList<String> collect = new ArrayList<>();
//            组装附件信息
                List<HashMap> resultFile = new ArrayList<>();
                String reportUrl = PlatApplicationContext.getProperty("billPrint.Cq") + PlatApplicationContext.getProperty("P.printParam.Quality.Cq") + "&allocateVehicleNo=" + allocateVehicleNo + "&segNo=" + segNo + "&format=PDF";
                collect.add(reportUrl);
                //接入自动打印
                //查询生效的出库单打印机配置
                String printName1 = findPrintName(segNo,"20",factoryBuilding);
                for (String s : collect) {
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("printName",printName1);
                    hashMap.put("uploadFilePath",s);
                    resultFile.add(hashMap);
                }

                // 待新增的附件记录
                LIRL0312 lirl0312 = new LIRL0312();
                // lirl0312.setRelevanceId(id);
                // 文件信息
                lirl0312.setUploadFileName("入库质量确认书"+DateUtil.curDateTimeStr14()+".pdf");
                lirl0312.setFifleType(".pdf");
                lirl0312.setFifleSize(new BigDecimal(10000));
                lirl0312.setFileId(UUIDUtils.getUUID());
                // 设置文件下载路径
                lirl0312.setUploadFilePath(reportUrl);
                lirl0312.setRecCreator("System");
                lirl0312.setRecCreatorName("System");
                lirl0312.setRecRevisor("System");
                lirl0312.setRecRevisorName("System");
                lirl0312.setSignatureMark(" ");
                lirl0312.setSegNo(segNo);
                lirl0312.setUnitCode(segNo);
                lirl0312.setRecCreateTime(DateUtil.curDateTimeStr14());
                lirl0312.setRecReviseTime(DateUtil.curDateTimeStr14());
                lirl0312.setUuid(UUIDUtils.getUUID());
                lirl0312.setRelevanceType(UUIDUtils.getUUID());
                lirl0312.setRelevanceId(carTraceNo);
                Map insMap = lirl0312.toMap();
                dao.insert(LIRL0312.INSERT, insMap);

                String ifRemotePrint = new SwitchUtils().getProcessSwitchValue(segNo, "IF_QUALITY_REMOTE_PRINT", dao);
                String printName = "";
                String printPort = "";
                if ("1".equals(ifRemotePrint)) {
                    for (HashMap url : resultFile) {
//                        String printNameAll = (String) url.get("printName");
//                        System.out.println(printNameAll);
//                        String[] split = printNameAll.split("-");
//                        printName= split[0];
//                        System.out.println("IP地址："+printName);
//                        printPort=split[1];
//                        System.out.println("IP端口："+printPort);
                        uploadPrintFile(MapUtils.getString(url, "uploadFilePath"), "/" + segNo + ".pdf", segNo, "URLTC", printName, printPort,factoryBuilding);
                    }
                } else {
                    eiInfo.set("docUrlList", collect);
                }
                eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
            } catch (Exception e) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg(e.getMessage());
            }
        }
        eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
        return eiInfo;
    }

    /***
     * 打印机配置查询
     * @param segNo
     * @return
     */
    private String findPrintName(String segNo,String billPrintType) {
        String printerName="";
        String printerPort="";
        HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("segNo", segNo);
        objectObjectHashMap.put("billPrintType",billPrintType);
        objectObjectHashMap.put("status","20");
        List<LIRL0506> queryLIRL0506 = this.dao.query(LIRL0506.QUERY, objectObjectHashMap);
        if (CollectionUtils.isNotEmpty(queryLIRL0506)){
            printerName = queryLIRL0506.get(0).getPrinterIpAddr();
            printerPort = queryLIRL0506.get(0).getPrinterPort();
        }
        return printerName+"-"+printerPort;
    }


    /***
     * 打印机配置查询(到厂区)
     * @param segNo
     * @return
     */
    private String findPrintName(String segNo,String billPrintType,String factoryBuilding) {
        String printerName="";
        String printerPort="";
        HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("segNo", segNo);
        objectObjectHashMap.put("billPrintType",billPrintType);
        objectObjectHashMap.put("status","20");
        if (StringUtils.isNotBlank(factoryBuilding)){
            objectObjectHashMap.put("factoryBuilding",factoryBuilding);
        }
        List<LIRL0506> queryLIRL0506 = this.dao.query(LIRL0506.QUERY, objectObjectHashMap);
        if (CollectionUtils.isNotEmpty(queryLIRL0506)){
            printerName = queryLIRL0506.get(0).getPrinterIpAddr();
            printerPort = queryLIRL0506.get(0).getPrinterPort();
        }else {
            objectObjectHashMap.put("factoryBuilding","");
            queryLIRL0506 = this.dao.query(LIRL0506.QUERY, objectObjectHashMap);
            if (CollectionUtils.isNotEmpty(queryLIRL0506)){
                printerName = queryLIRL0506.get(0).getPrinterIpAddr();
                printerPort = queryLIRL0506.get(0).getPrinterPort();
            }
        }
        return printerName+"-"+printerPort;
    }


    /**
     * 下载并上传文件
     *
     * @param url
     * @param fileName
     */
    private void uploadPrintFile(String url, String fileName, String segNo, String itemCode,String printName,String printPort,String factoryArea) {
        //通过驱动打印报表
        String otherProjectUrl = "";
        String printerName = "";
        //查询配置表 获取打印机地址
        Map querytlirl0314 = new HashMap();
        querytlirl0314.put("segNo",segNo);
        querytlirl0314.put("itemCode",itemCode);
        querytlirl0314.put("factoryArea",factoryArea);
        List<LIRL0314> lirl0314s = this.dao.query(LIRL0314.QUERY, querytlirl0314);
        if (lirl0314s.size()>0){
            LIRL0314 lirl0314 = lirl0314s.get(0);
            otherProjectUrl = lirl0314.getItemCname();
            printerName = lirl0314.getWarehouseName();
        }
        if (otherProjectUrl.length()>0&&StringUtils.isBlank(printPort)){
            // 调用其他项目的 POST 接口，假设接口地址为 otherProjectUrl
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            // 构建请求体，这里传入 URL
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("url", url);
            requestBody.put("fileName", fileName);
            requestBody.put("printerName", printerName);
            requestBody.put("putFlag", null);
            System.out.println("————————————————————————调用接口中————————————————————————");
            logger.info("————————————————————————调用接口中————————————————————————");
            HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);
            ResponseEntity<String> response = restTemplate.exchange(otherProjectUrl, HttpMethod.POST, requestEntity, String.class);
            if (response.getStatusCode() == HttpStatus.OK) {
                System.out.println("成功调用其他项目的 POST 接口，响应内容: " + response.getBody());
            } else {
                System.err.println("调用其他项目的 POST 接口失败，状态码: " + response.getStatusCode());
            }
        }

//        try {
//            //通过IP 访问办公室打印机
//            if (StringUtils.isNotBlank(printPort)){
//
//                //调用方法
////                PrintIPUtils.printMethod(url, printerName, Integer.valueOf(printPort),10000);
//                new ServiceLIRLInterface();
//                ServiceLIRLInterface.printPDF(url, printName,Integer.valueOf(printPort),500000);
//                System.out.println("打印报表URL:"+url);
//            }
//        } catch (HttpClientErrorException e) {
//            // 处理客户端错误，比如 400、404 等错误码对应的情况
//            System.err.println("客户端错误: " + e.getStatusCode() + ", 错误信息: " + e.getMessage());
//        } catch (HttpServerErrorException e) {
//            // 处理服务器端错误，比如 500 等错误码对应的情况
//            System.err.println("服务器端错误: " + e.getStatusCode() + ", 错误信息: " + e.getMessage());
//        } catch (Exception e) {
//            // 处理其他通用异常情况
//            System.err.println("其他异常: " + e.getMessage());
//        }
    }

    /***
     * 重庆出库自动打印
     *
     *S_LI_RL_0180
     *
     */
    public EiInfo printCQ(EiInfo inInfo) {

        String segNo = (String) inInfo.get("segNo");
        List<String> putoutIdList = (List<String>) inInfo.get("putoutIdList");
        String putoutStackingRecNum = (String) inInfo.get("putoutStackingRecNum");
        inInfo.set("putoutStackingRecNum", putoutStackingRecNum);
        inInfo.set("putoutIdList", putoutIdList);
        inInfo.set("segNo",segNo);
        //增加开关，是否走IMC配置打印
        String printWhetherToGoImc = new SwitchUtils().getProcessSwitchValue(segNo, "PRINT_WHETHER_TO_GO_IMC", dao);
        if ("1".equals(printWhetherToGoImc)){
            inInfo.set(EiConstant.serviceId,"S_UC_PR_0441");
            EiInfo outInfo=new EiInfo();
            try {
                outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
                if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                    return outInfo;
                }
            } catch (Exception e) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg(e.getMessage());
            }
        }else {
            List<Map> resultList = new ArrayList<>();
            List<String> addUrlList = new ArrayList<>();
            //根据捆包号、材料管理号、提单号查询出库服务
            EiInfo outInfo1 = new EiInfo();
            outInfo1.set("segNo", segNo);
            outInfo1.set("putoutId", putoutStackingRecNum);//提单号
            // outInfo1.set("packId", packId);//捆包
            outInfo1.set("offset", "0");
            outInfo1.set("limit", "100");
            outInfo1.set("redFlag", "0");
            outInfo1.set(EiConstant.serviceId, "S_UC_EW_0287");
            EiInfo outInfo0287 = EServiceManager.call(outInfo1, TokenUtils.getXplatToken());
            if (outInfo0287.getStatus() == -1) {
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("调用物流中心返回报错,出库单查询服务失败:" + outInfo0287.getMsg());
//                throw new RuntimeException(outInfo0287.getMsg());
            }
            List<Map> result0287 = (List<Map>) outInfo0287.get("result");
            if (CollectionUtils.isNotEmpty(result0287)) {
                for (Map map0287 : result0287) {
                    resultList.add(map0287);
                }
            }
//            EiInfo outInfo2 = new EiInfo();
//            outInfo2.set("segNo", segNo);
//            outInfo2.set("result", putoutStackingRecNum);//提单号
//            outInfo2.addBlock(EiConstant.resultBlock).setRows(result0287);
//            outInfo2.set(EiConstant.serviceId, "S_UC_EW_0332");
//            EiInfo outInfo0332 = EServiceManager.call(outInfo2, TokenUtils.getXplatToken());
//            if (outInfo0332.getStatus() == -1) {
//                inInfo.setStatus(EiConstant.STATUS_FAILURE);
//                inInfo.setMsg("调用物流中心返回报错,出库单查询服务失败:" + outInfo0287.getMsg());
////                throw new RuntimeException(outInfo0287.getMsg());
//            }
            //出库单打印
            if (CollectionUtils.isNotEmpty(resultList)) {
                String uuids = "";
                String putOutIds = "";
                int size = resultList.size();
                int count = 1;
                for (Map map1 : resultList) {
                    String uuid = MapUtils.getString(map1, "uuid", "");//出库单子项号
                    String putOutId = MapUtils.getString(map1, "putoutId", "");//出库单子项号
                    segNo = MapUtils.getString(map1, "segNo", "");//出库单子项号
                    if (count < size) {
                        uuids += uuid.concat("','");
                        putOutIds += putOutId.concat("','");
                    } else {
                        uuids += uuid;
                        putOutIds += putOutId;
                    }
                    count++;
                }

                outInfo1.set("segNo", segNo);
                outInfo1.set("putoutId", putoutStackingRecNum);//提单号
                // outInfo1.set("packId", packId);//捆包
                outInfo1.set("offset", "0");
                outInfo1.set("limit", "100");
                outInfo1.set("redFlag", "0");
                outInfo1.set(EiConstant.serviceId, "S_UC_EW_0287");

                String reportUrl = PlatApplicationContext.getProperty("billPrint") + PlatApplicationContext.getProperty("P.printParam.Cq") + "&uuid=" + uuids + "&putoutStackingRecNum=" + putOutIds + "&segNo=" + segNo + "&format=PDF";
                addUrlList.add(reportUrl);
            }

            List<HashMap> resultFile = new ArrayList<>();
            List<String> collect = addUrlList.stream().distinct().collect(Collectors.toList());
            //接入自动打印
            //查询生效的出库单打印机配置
            String printName1 = findPrintName(segNo, "20");
            for (String s : collect) {
                HashMap<String, Object> hashMap = new HashMap<>();
                hashMap.put("printName", printName1);
                hashMap.put("uploadFilePath", s);
                resultFile.add(hashMap);
            }

            String ifRemotePrint = new SwitchUtils().getProcessSwitchValue(segNo, "IF_REMOTE_PRINT", dao);
            String printName="";
            String printPort="";
            if ("1".equals(ifRemotePrint)){
                for (HashMap url : resultFile) {
                    printName= (String) url.get("printName");
                    printPort= (String) url.get("printPort");
                    if (StringUtils.isNotBlank(printName)) {
                        printName = removeLastHyphen(printName);
                        System.out.println("出库单打印打印机名称：" + printName);
                    }
                    uploadPrintFilePut((String) url.get("uploadFilePath"), "/"+segNo + ".pdf", segNo, "URLTC",printName,printPort);
                    inInfo.set("docUrlList",resultFile);
                }
            }else {
                inInfo.set("docUrlList",resultFile);
            }
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        }
        return inInfo;
    }

    /***
     * pda打印入库质量确认单车辆查询
     *
     * S_LI_RL_0187
     */
    public EiInfo queryVehicleLoadInfo(EiInfo inInfo) {
        String segNo = (String) inInfo.get("segNo");
        if (StringUtils.isBlank(segNo)){
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("账套不能为空！");
            return inInfo;
        }
        HashMap<Object, Object> hashMap = new HashMap<>();
        hashMap.put("segNo", segNo);
        List<HashMap> query = this.dao.query(LIRL0301.QUERY_LOADING_CAR, hashMap);
        inInfo.set("list", query);
        inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        return inInfo;
    }


    public void uploadPrintFilePut(String url, String fileName, String segNo, String itemCode,String printName,String printPort) {
        try {
            String otherProjectUrl = "";
            String printerName = "";
            //查询配置表 获取打印机地址
            Map querytlirl0314 = new HashMap();
            querytlirl0314.put("segNo",segNo);
            querytlirl0314.put("itemCode",itemCode);
            List<LIRL0314> lirl0314s = this.dao.query(LIRL0314.QUERY, querytlirl0314);
            if (lirl0314s.size()>0){
                LIRL0314 lirl0314 = lirl0314s.get(0);
                otherProjectUrl = lirl0314.getItemCname();
                printerName = lirl0314.getWarehouseName();
                System.out.println("打印打印机名称1："+printerName);
                if (StringUtils.isNotBlank(printName)&&StringUtils.isNotBlank(printPort)){ //附近制定打印机
                    printerName=printName;
                    System.out.println("打印打印机名称2："+printerName);
                }
            }

            if (otherProjectUrl.length()>0&&StringUtils.isBlank(printPort)){
                // 调用其他项目的 POST 接口，假设接口地址为 otherProjectUrl
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                // 构建请求体，这里传入 URL
                Map<String, String> requestBody = new HashMap<>();
                requestBody.put("url", url);
                requestBody.put("fileName", fileName);
                requestBody.put("printerName", printerName);
                if ("URLTC".equals(itemCode)){
                    requestBody.put("putFlag", "10");
                }else {
                    requestBody.put("putFlag", null);
                }
                HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);
                ResponseEntity<String> response = restTemplate.exchange(otherProjectUrl, HttpMethod.POST, requestEntity, String.class);
                if (response.getStatusCode() == HttpStatus.OK) {
                    System.out.println("成功调用其他项目的 POST 接口，响应内容: " + response.getBody());
                } else {
                    System.err.println("调用其他项目的 POST 接口失败，状态码: " + response.getStatusCode());
                }
            }
            //通过IP 访问办公室打印机
            if (StringUtils.isNotBlank(printPort)){
                //调用方法
                PrintIPUtils.printMethod(url, printerName, Integer.valueOf(printPort),10000);
            }
            // 发起 HTTP 请求获取文件内容
            /*ResponseEntity<byte[]> rsp = restTemplate.getForEntity(url, byte[].class);*/


            /*byte[] fileContent = rsp.getBody();
            // 获取当前工作目录，并使用 File.separator 作为文件分隔符
            String directoryPath = "/apps/upload";
            File directory = new File(directoryPath);
            if (!directory.exists()) {
                if (directory.mkdirs()) {
                    System.out.println("目录创建成功");
                } else {
                    System.out.println("目录创建失败");
                }
            }
            // 构建完整的文件存储路径
            fileName = directoryPath + fileName;
            // 将文件内容保存到本地
            saveFile(fileName, rsp.getBody());
            String printerName = "Brother HL-2260D";
            //printPDF(fileName, printerName,LANDSCAPE);
            //查询配置表 获取打印机地址
            Map querytlirl0314 = new HashMap();
            querytlirl0314.put("segNo",segNo);
            querytlirl0314.put("itemCode",itemCode);
            List<LIRL0314> lirl0314s = dao.query(LIRL0314.QUERY, querytlirl0314);
            if (lirl0314s.size()>0){
                LIRL0314 lirl0314 = lirl0314s.get(0);
                printPDF(fileName, lirl0314.getItemCname(), 9100,10000);
            }*/
        } catch (HttpClientErrorException e) {
            // 处理客户端错误，比如 400、404 等错误码对应的情况
            System.err.println("客户端错误: " + e.getStatusCode() + ", 错误信息: " + e.getMessage());
        } catch (HttpServerErrorException e) {
            // 处理服务器端错误，比如 500 等错误码对应的情况
            System.err.println("服务器端错误: " + e.getStatusCode() + ", 错误信息: " + e.getMessage());
        } catch (Exception e) {
            // 处理其他通用异常情况
            System.err.println("其他异常: " + e.getMessage());
        }
    }


    /***
     * PDA装货临时加单
     *
     * S_LI_RL_190
     */
    public EiInfo addPutoutCar(EiInfo inInfo) {
        String segNo = (String) inInfo.get("segNo");
        String carTraceNo = (String) inInfo.get("carTraceNo");
        String userId = (String) inInfo.get("userId");
        String userName = (String) inInfo.get("userName");
        String warehouseCode = (String) inInfo.get("warehouseCode");
        String warehouseName = (String) inInfo.get("warehouseName");
        String allocateVehicleNo = (String) inInfo.get("allocateVehicleNo");

        if (StringUtils.isNotBlank(allocateVehicleNo)) {
            // 参数检查
            if (StringUtils.isBlank(segNo)) {
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("系统账套不能为空");
                return inInfo;
            }

            List<String> warehouseCodeList = new ArrayList<>();
            warehouseCodeList.add("A18113105");
            warehouseCodeList.add("*********");
            warehouseCodeList.add("494668005");

            // 提单号列表
            List<String> ladingBillList = (List<String>) inInfo.get("ladingBillIds");
            if (CollectionUtils.isEmpty(ladingBillList)) {
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("提单信息不能为空");
                return inInfo;
            }

            try {
                // 1. 处理普通提单
                processNormalBills(segNo, ladingBillList, allocateVehicleNo, warehouseCodeList, userId, userName);

                // 2. 处理转库单
                processTransferBills(segNo, ladingBillList, allocateVehicleNo, warehouseCode, warehouseName, userId, userName);

//            // 3. 处理配单子表信息
//            processAllocateDetails(segNo, carTraceNo, allocateVehicleNo,userId,userName);


                inInfo.setStatus(EiConstant.STATUS_SUCCESS);
                inInfo.setMsg("临时加单成功");
            } catch (Exception e) {
                logger.error("临时加单异常", e);
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("临时加单异常: " + e.getMessage());
            }
        }
        inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        return inInfo;
    }

    /**
     * 处理普通提单
     */
    private void processNormalBills(String segNo, List<String> ladingBillList, String allocateVehicleNo, List<String> warehouseCodeList, String userId, String userName) {
        // 提取所有提单号

        if (CollectionUtils.isEmpty(ladingBillList)) {
            return;
        }

        for (String ladingBillId : ladingBillList) {
            // 调用开单中心查询提单信息
            EiInfo eiInfo = new EiInfo();
            eiInfo.set("segNo", segNo);
            eiInfo.set("ladingBillStatus", "50");  //生效
            eiInfo.set("ladingSpotIds", warehouseCodeList);
            eiInfo.set("ladingBillId", ladingBillId);
            eiInfo.set(EiConstant.serviceId, "S_UV_SL_9018");

            EiInfo outInfo = EServiceManager.call(eiInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == -1) {
                logger.error("调用开单中心返回报错: {}", outInfo.getMsg());
                throw new RuntimeException("调用开单中心返回报错:" + outInfo.getMsg());
            }

            List<HashMap> billResults = (List<HashMap>) outInfo.get("result");
            List<HashMap> packResults = (List<HashMap>) outInfo.get("result2");

            logger.info("总提单数量: {}", String.valueOf(billResults.size()));

            // 处理提单信息
            for (HashMap bill : billResults) {
                String billingMethod = MapUtils.getString(bill, "billingMethod", "");  //开单方式 10-按捆包 20-按重量 30-按件数

                // 按重量或件数的普通提单(非按捆包)
                if (!"10".equals(billingMethod) && ladingBillId.startsWith("BL")) {
                    processWeightOrPieceBill(segNo, bill, allocateVehicleNo,userId,userName);
                } else if ("10".equals(billingMethod)) {
                    // 按捆包的提单
                    processPackageBill(segNo, bill, packResults, allocateVehicleNo,userId,userName);
                }
            }
        }

    }

    /**
     * 处理按重量或件数提单
     */
    private void processWeightOrPieceBill(String segNo, HashMap bill, String allocateVehicleNo, String userId, String userName) {
        String ladingBillId = MapUtils.getString(bill, "ladingBillId", "");
        String ladingSpotName = MapUtils.getString(bill, "ladingSpotName", "");
        String recCreator = MapUtils.getString(bill, "recCreator", "");
        String deliveryType = MapUtils.getString(bill, "deliveryType", "");
        String customerId = MapUtils.getString(bill, "userId", "");
        String customerName = MapUtils.getString(bill, "userName", "");
        String remark = MapUtils.getString(bill, "remark", "");
        String billingMethod = MapUtils.getString(bill, "billingMethod", "");

        // 调用物流服务获取提货单重量件数信息
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("segNo", segNo);
        eiInfo.set("warehouseCode", "*********");

        List<String> ladingBillIdList = new ArrayList<>();
        ladingBillIdList.add(ladingBillId);
        eiInfo.set("ladingBillIdList", ladingBillIdList);
        eiInfo.set(EiConstant.serviceId, "S_UC_PR_0411");

        EiInfo outInfo = EServiceManager.call(eiInfo, TokenUtils.getXplatToken());
        Map attr = outInfo.getAttr();
        Map result = MapUtils.getMap(attr, "result");
        List<Map> packList = (List) result.get("packList");

        if (CollectionUtils.isNotEmpty(packList)) {
            for (Map pack : packList) {
                insertLIRL0503Record(segNo, allocateVehicleNo, ladingBillId, billingMethod,
                        deliveryType, customerId, customerName, recCreator, remark, pack,userId,userName);
            }
        }
    }

    /**
     * 处理按捆包提单
     */
    private void processPackageBill(String segNo, HashMap bill, List<HashMap> packResults, String allocateVehicleNo, String userId, String userName) {
        String ladingBillId = MapUtils.getString(bill, "ladingBillId", "");
        String recCreator = MapUtils.getString(bill, "recCreator", "");
        String deliveryType = MapUtils.getString(bill, "deliveryType", "");
        String customerId = MapUtils.getString(bill, "userId", "");
        String customerName = MapUtils.getString(bill, "userName", "");
        String remark = MapUtils.getString(bill, "remark", "");
        String billingMethod = MapUtils.getString(bill, "billingMethod", "");

        if (CollectionUtils.isNotEmpty(packResults)) {
            for (HashMap pack : packResults) {
                String returnStatus = MapUtils.getString(pack, "returnStatus", "");

                // 跳过已出库的捆包
                if ("Y".equals(returnStatus)) {
                    continue;
                }

                // 普通的插入0503表
                insertLIRL0503Record(segNo, allocateVehicleNo, ladingBillId, billingMethod,
                        deliveryType, customerId, customerName, recCreator, remark, pack, userId, userName);
            }
        }
    }

    /**
     * 处理转库单
     */
    private void processTransferBills(String segNo, List<String> ladingBillList, String allocateVehicleNo, String warehouseCode, String warehouseName, String userId, String userName) {
        for (String bill : ladingBillList) {
            String transBillId = bill;

            // 只处理转库单
            if (StringUtils.isNotBlank(transBillId) && transBillId.startsWith("ZK")) {

                // 查询转库单详情
                EiInfo eiInfo = new EiInfo();
                eiInfo.set("segNo", segNo);
                eiInfo.set("transBillId", transBillId);
                eiInfo.set("transBillType", "10");
                eiInfo.set("packPutOutFlag", "0");
                eiInfo.set(EiConstant.serviceId, "S_UC_EP_0045");

                EiInfo outInfo = EServiceManager.call(eiInfo, TokenUtils.getXplatToken());
                if (outInfo.getStatus() == -1) {
                    logger.error("调用物流中心返回错误: {}", outInfo.getMsg());
                    continue;
                }

                List<HashMap> packList = (List<HashMap>) outInfo.get("result");
                if (CollectionUtils.isNotEmpty(packList)) {
                    for (HashMap pack : packList) {
                        String ladingSpotName = MapUtils.getString(pack, "ladingSpotName", "");
                        String destSpotName = MapUtils.getString(pack, "destSpotName", "");
                        String sumNetWeight = MapUtils.getString(pack, "sumNetWeight", "");
                        String sumNtotalQty = MapUtils.getString(pack, "sumNtotalQty", "");
                        String deliveryType = MapUtils.getString(pack, "deliveryType", "");
                        String customerName = MapUtils.getString(pack, "customerName", "");
                        String customerId = MapUtils.getString(pack, "userNum", "");
                        String ladingSpotId = MapUtils.getString(pack, "ladingSpotId", "");
                        // 补充转库单信息插入
                        Map<String, Object> packInfo = new HashMap<>(pack);
                        packInfo.put("warehouseCode", warehouseCode);
                        packInfo.put("warehouseName", warehouseName);

                        // 插入LIRL0503记录
                        insertLIRL0503Record(segNo, allocateVehicleNo, transBillId, "10",
                                deliveryType, customerId, customerName, "", "", packInfo, userId, userName);
                    }
                }
            }
        }
    }

    /**
     * 处理配单子表信息
     */
    private void processAllocateDetails(String segNo, String carTraceNo, String allocateVehicleNo,String userId,String userName) {
        // 查询配单子表最大号
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("segNo", segNo);
        queryParams.put("allocateVehicleNo", allocateVehicleNo);
        List<LIRL0503> details = this.dao.query(LIRL0503.QUERY_MAXALLOC_VEHICLE_S, queryParams);

        if (details.isEmpty()) {
            // 无配单明细，创建默认形式提单记录
            Map<String, Object> defaultRecord = new HashMap<>();
            defaultRecord.put("segNo", segNo);
            defaultRecord.put("allocateVehicleNo", allocateVehicleNo);
            defaultRecord.put("carTraceNo", carTraceNo);
            defaultRecord.put("status", "10");  // 初始状态
            defaultRecord.put("billingMethod", "20");  // 按重量
            defaultRecord.put("recCreator", userId);
            defaultRecord.put("recCreatorName", userName);
            defaultRecord.put("recCreateTime", DateUtil.curDateTimeStr14());
            defaultRecord.put("recRevisor", "SYSTEM");
            defaultRecord.put("recRevisorName", "系统自动");
            defaultRecord.put("recReviseTime", DateUtil.curDateTimeStr14());
            defaultRecord.put("uuid", UUIDUtils.getUUID());
            defaultRecord.put("delFlag", "0");
            defaultRecord.put("archiveFlag", "0");
            defaultRecord.put("remark", "临时加单自动生成");

            dao.insert(LIRL0503.INSERT, defaultRecord);

            logger.info("创建默认形式提单记录成功");
        } else {
            // 有配单明细，更新状态
            Map<String, Object> updateParams = new HashMap<>();
            updateParams.put("segNo", segNo);
            updateParams.put("allocateVehicleNo", allocateVehicleNo);
            updateParams.put("status", "20");  // 已配车
            updateParams.put("recRevisor", "SYSTEM");
            updateParams.put("recRevisorName", "系统自动");
            updateParams.put("recReviseTime", DateUtil.curDateTimeStr14());

            dao.update(LIRL0503.UPDATE_STATUS, updateParams);

            logger.info("更新配单明细状态成功, 总计: {}", String.valueOf(details.size()));
        }
    }

    /**
     * 插入LIRL0503记录
     */
    private void insertLIRL0503Record(String segNo, String allocateVehicleNo, String voucherNum,
                                      String billingMethod, String deliveryType, String customerId,
                                      String customerName, String recCreator, String ladingBillRemark,
                                      Map<String, Object> packInfo, String userId, String userName) {

        String packId = MapUtils.getString(packInfo, "packId", "");
        String warehouseCode = MapUtils.getString(packInfo, "warehouseCode", "");
        String warehouseName = MapUtils.getString(packInfo, "warehouseName", "");
        String locationId = MapUtils.getString(packInfo, "locationId", "");
        String locationName = MapUtils.getString(packInfo, "locationName", "");
        String matInnerId = MapUtils.getString(packInfo, "matInnerId", "");
        String specsDesc = MapUtils.getString(packInfo, "specsDesc", "");
        String pieceNum = MapUtils.getString(packInfo, "pieceNum", "");
        String netWeight = MapUtils.getString(packInfo, "netWeight", "");
        String factoryOrderNum = MapUtils.getString(packInfo, "factoryOrderNum", "");
        String purOrderNum = MapUtils.getString(packInfo, "orderNum", "");
        String custPartId = MapUtils.getString(packInfo, "custPartId", "");
        String custPartName = MapUtils.getString(packInfo, "custPartName", "");
        String m_packId = MapUtils.getString(packInfo, "m_packId", "");

        String allocVehicleSeq="";
        String allocVehicleSeqMax="";

        String[] arg = {allocateVehicleNo, "", "", ""};
        allocVehicleSeq = SequenceGenerator.getNextSequence("TMELI0503_SEQ01", arg);

        Map<String, Object> insertLIRL0503 = new HashMap<>();
        insertLIRL0503.put("allocVehicleSeq", allocVehicleSeq);
        insertLIRL0503.put("allocateVehicleNo", allocateVehicleNo);
        insertLIRL0503.put("segNo", segNo);
        insertLIRL0503.put("unitCode", segNo);
        insertLIRL0503.put("status", 20);
        insertLIRL0503.put("billingMethod", billingMethod);
        insertLIRL0503.put("locationName", locationName);
        insertLIRL0503.put("voucherNum", voucherNum);
        insertLIRL0503.put("packId", packId);
        insertLIRL0503.put("outPackFlag", "");
        insertLIRL0503.put("warehouseCode", warehouseCode);
        insertLIRL0503.put("warehouseName", warehouseName);
        insertLIRL0503.put("deliveryType", deliveryType);
        insertLIRL0503.put("mPackId", m_packId);
        insertLIRL0503.put("custPartId", custPartId);
        insertLIRL0503.put("custPartName", custPartName);
        insertLIRL0503.put("putinType", MapUtils.getString(packInfo, "putinType", ""));
        insertLIRL0503.put("innerDiameter", 0);
        insertLIRL0503.put("prodDensity", 0);
        insertLIRL0503.put("productProcessId", "");
        insertLIRL0503.put("netWeight", netWeight);
        insertLIRL0503.put("customerId", customerId);
        insertLIRL0503.put("customerName", customerName);
        insertLIRL0503.put("matInnerId", matInnerId);
        insertLIRL0503.put("specsDesc", specsDesc);
        insertLIRL0503.put("piceNum", pieceNum);
        insertLIRL0503.put("factoryOrderNum", factoryOrderNum);
        insertLIRL0503.put("locationId", locationId);
        insertLIRL0503.put("prodTypeId", StringUtils.isNotBlank(MapUtils.getString(packInfo, "prodTypeId", "")) ? MapUtils.getString(packInfo, "prodTypeId", "") : " ");
        insertLIRL0503.put("factoryArea", StringUtils.isNotBlank(MapUtils.getString(packInfo, "factoryArea", "")) ? MapUtils.getString(packInfo, "factoryArea", "") : "");
        insertLIRL0503.put("tradeCode", StringUtils.isNotBlank(MapUtils.getString(packInfo, "tradeCode", "")) ? MapUtils.getString(packInfo, "tradeCode", "") : " ");

        // 查询装卸点信息
        String targetHandPointId = getLoadingPointInfo(segNo, warehouseCode, locationId);
        insertLIRL0503.put("targetHandPointId", targetHandPointId);

        // 设置基础信息
        if (StringUtils.isBlank(userId)) {
            recCreator = "SYSTEM";
        }

        insertLIRL0503.put("recCreator", userId);
        insertLIRL0503.put("recCreatorName", userName);
        insertLIRL0503.put("recCreateTime", DateUtil.curDateTimeStr14());
        insertLIRL0503.put("recRevisor", recCreator);
        insertLIRL0503.put("recRevisorName", recCreator);
        insertLIRL0503.put("recReviseTime", DateUtil.curDateTimeStr14());
        insertLIRL0503.put("uuid", UUIDUtils.getUUID());
        insertLIRL0503.put("delFlag", "0");
        insertLIRL0503.put("archiveFlag", "0");
        insertLIRL0503.put("tenantId", " ");
        insertLIRL0503.put("remark", "现场临时增加提单："+DateUtil.curDateTimeStr14());
        insertLIRL0503.put("purOrderNum", purOrderNum);
        insertLIRL0503.put("ladingBillRemark", ladingBillRemark);

        if (!allocateVehicleNo.contains("undefined")) {
            dao.insert(LIRL0503.INSERT, insertLIRL0503);
        }
    }

    /**
     * 获取装卸点信息
     */
    private String getLoadingPointInfo(String segNo, String warehouseCode, String locationId) {
        // 查询库位附属信息表获取装卸点
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("segNo", segNo);
        queryParams.put("warehouseCode", warehouseCode);
        queryParams.put("locationId", locationId);
        queryParams.put("useStatus", 10);

        List<LIDS0601> locationInfoList = dao.query(LIDS0601.QUERY, queryParams);
        if (CollectionUtils.isEmpty(locationInfoList)) {
            throw new RuntimeException("查询不到库位附属信息");
        }

        LIDS0601 locationInfo = locationInfoList.get(0);
        String targetHandPointId = locationInfo.getLoadingPointNo();

        if (StringUtils.isBlank(targetHandPointId)) {
            throw new RuntimeException("库位名称：" + locationInfo.getLocationName() + "查询不到对应的装卸点，请维护装卸点！");
        }

        // 判断装卸点是否启用
        Map<String, Object> handPointParams = new HashMap<>();
        handPointParams.put("segNo", segNo);
        handPointParams.put("handPointId", targetHandPointId);
        handPointParams.put("status", "30");

        int count = super.count(LIRL0304.COUNT, handPointParams);
        if (count < 0) {
            throw new RuntimeException("根据提单未找到对应的装卸点，请及时联系仓库人员！");
        }

        return targetHandPointId;
    }

    /***
     * 入库质量确认书附件上传
     *
     * @ServiceId S_LI_RL_0197
     */
    public EiInfo uploadQualityConfirmation(EiInfo inInfo) {

        EiInfo outInfo = new EiInfo();
        try {
            // 获取前端参数
            String base64String = (String) inInfo.get("file");
            String type = (String) inInfo.get("type"); //1，发货人，2，领用人，3，收货人
            CommonsMultipartFile file = Base64ToMultipartFileConverter.convertToMultipartFile(base64String);
            /*CommonsMultipartFile file = (CommonsMultipartFile) inInfo.get("file");*/
            String id = inInfo.getString("id");
            if ("3".equals(type)){
                if (id.contains("[")){
                    // 先去除两边的中括号
                    id = id.substring(1, id.length() - 1).replaceAll(" ","");
                }
            }
            List<String> id2 = (List) inInfo.get("id2");
            String segNo = inInfo.getString("segNo");
            String signatureMark = (String) inInfo.get("signatureMark");
            String relevanceType = "";
            // 上传文件
            String fileName = file.getOriginalFilename();
            if (StrUtil.isBlank(fileName)) {
                fileName = "";
            }
            // 生成文件ID
            String fileId = StrUtil.getUUID();
            // 获取文件后缀
            String suffix = fileName.substring(fileName.lastIndexOf("."));
            // 转换文件名防止文件重复
            // String storeName = fileId + suffix;
            // 上传文件
            // String downloadUrl = FtpUtils.uploadFile("JC000000", file, "customerSignOff" + "/" + storeName);
            //1,2 PDA , 3,Mini programs
            String directory ="";
            if ("1".equals(type)){
                directory = "/PDA/"+segNo;
            }else if ("2".equals(type)){
                directory = "/PDA/"+segNo;
            }else if ("3".equals(type)){
                directory = "/miniPrograms/"+segNo;
            }else {
                directory = "/PDA/"+segNo;
            }
            String downloadUrl = FileUtils.uploadFile(file, directory);
            if (downloadUrl == null) {
                throw new PlatException("文件上传失败");
            }
            // 待新增的附件记录
            LIRL0312 lirl0312 = new LIRL0312();
            // 文件信息
            lirl0312.setUploadFileName(fileName);
            lirl0312.setFifleType(suffix);
            lirl0312.setFifleSize(new BigDecimal(file.getSize()));
            lirl0312.setFileId(fileId);
            // 设置文件下载路径
            lirl0312.setUploadFilePath(downloadUrl);
            lirl0312.setRecCreator("System");
            lirl0312.setRecCreatorName("System");
            lirl0312.setRecRevisor("System");
            lirl0312.setRecRevisorName("System");
            lirl0312.setSignatureMark(signatureMark);
            lirl0312.setSegNo(segNo);
            lirl0312.setUnitCode(segNo);
            lirl0312.setRecCreateTime(DateUtil.curDateTimeStr14());
            lirl0312.setRecReviseTime(DateUtil.curDateTimeStr14());
            //查询车辆作业实绩表 出库单号
            Map queryMap1 = new HashMap();
            queryMap1.put("carTraceNo", id);
            queryMap1.put("segNo", segNo);
            if ("JC000000".equals(segNo)) {
                queryMap1.put("putInOutFlag", "10");
            }
            List<LIRL0308> lirl0308s1 = dao.query(LIRL0308.QUERY, queryMap1);
            if (CollectionUtils.isNotEmpty(lirl0308s1)) {
                for (LIRL0308 lirl0308 : lirl0308s1) {
                    lirl0312.setUuid(StrUtil.getUUID());
                    lirl0312.setRelevanceType(lirl0308.getPackId());
                    lirl0312.setRelevanceId(id);
                    Map insMap = lirl0312.toMap();
                    dao.insert(LIRL0312.INSERT, insMap);
                }
                if ("JC000000".equals(segNo)) {
                    if (lirl0308s1.size() >= 1) {
                        for (LIRL0308 lirl0308 : lirl0308s1) {
                            //文件上传到物流服务器
                            EiInfo eiInfo1 = new EiInfo();
                            eiInfo1.set("userId", "System");
                            eiInfo1.set("userName", "System");
                            Map pictureMap = new HashMap();
                            pictureMap.put("billNo", lirl0308.getPutinId());
                            pictureMap.put("segNo", segNo);
                            pictureMap.put("billSubid", lirl0308.getPackId());
                            //查询账套中文名称
                            Map querySegNoName = new HashMap();
                            querySegNoName.put("segNo", segNo);
                            List<LIRL0312> queryName = dao.query(LIRL0312.QUERY_SEG_NO_NAME, querySegNoName);
                            if (queryName.size() > 0) {
                                LIRL0312 lirl03121 = queryName.get(0);
                                Map map = lirl03121.toMap();
                                pictureMap.put("segCname", MapUtils.getString(map, "segName"));
                            }
                            pictureMap.put("affixType", "312");
                            pictureMap.put("type", ".png");
                            //如果type为1,2 PDA上传 根据车辆跟踪号查询该车的出库单号，出库单单号与mes绑定回写imc附件信息表
                            List<String> fileList = new ArrayList();
//            pictureMap.put("pictureList", fileList);
                            pictureMap.put("uuid", fileId);
                            pictureMap.put("url", downloadUrl);
                            eiInfo1.set("pictureMap", pictureMap);
                            eiInfo1.set(EiConstant.serviceId, "S_UC_PR_0443");
                            //调post请求
                            outInfo = EServiceManager.call(eiInfo1, TokenUtils.getXplatToken());
                            //打印日志到elk
                            if (outInfo.getStatus() == -1) {
                                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                outInfo.setMsg("调用文件上传物流报错:" + outInfo.getMsg());
                                throw new RuntimeException(outInfo.getMsg());
                            }
                        }
                    }
                }
            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("附件上传成功!");
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
        }
        return outInfo;
    }
}



