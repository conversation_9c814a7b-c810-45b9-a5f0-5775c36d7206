package com.baosight.imom.li.rl.service;


import com.baosight.imc.common.utils.ImcGlobalUtils;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.EasyExcelUtil;
import com.baosight.imom.common.utils.StrUtil;
import com.baosight.imom.li.rl.dao.LIRL0101;
import com.baosight.imom.li.rl.dao.LIRL0308;
import com.baosight.iplat4j.core.ei.*;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import net.sf.json.JSONObject;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.ResponseHandler;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.URL;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @Author: 韩亚宁
 * @Description: ${行车工装卸点工时查询}
 * @Date: 2024/10/30 09:37
 * @Version: 1.0
 */
public class ServiceLIRL0223 extends ServiceBase {

    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIRL0308().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     * @param inInfo
     * @return
     */
    public EiInfo query(EiInfo inInfo) {
        Map queryBlock = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
        String segNo = MapUtils.getString(queryBlock, "segNo", "");
        String segName = MapUtils.getString(queryBlock, "segName", "");
        String putInOutFlag = MapUtils.getString(queryBlock, "putInOutFlag", "");
        if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
//            throw new PlatException("缺少业务单元代码不能查询！");
            String massage = "缺少业务单元代码不能查询！";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        List<HashMap> list=new ArrayList<>();
        EiInfo outInfo = new EiInfo();
        if ("10".equals(putInOutFlag)&&"KF000000".equals(segNo)){
            queryBlock.put("putInOutFlagP", "10");
            queryBlock.put("putInOutFlag", "");
        }else if ("20".equals(putInOutFlag)&&"KF000000".equals(segNo)){
            queryBlock.put("putInOutFlagX", "20");
        }
        if ("JC000000".equals(segNo)){
            inInfo = super.query(inInfo, LIRL0308.QUERY_TOTAL_QUATITY_WEIGHT_CQ);
        }else {
            inInfo = super.query(inInfo, LIRL0308.QUERY_TOTAL_QUATITY_WEIGHT);
        }
        return inInfo;
    }

    /***
     * 后端导出
     * @param inInfo
     * @return
     */
    public EiInfo postExport(EiInfo inInfo) {
        Map<String,Object> loginMap = new HashMap();
        loginMap.put("userId",UserSession.getUserId());
        loginMap.put("userName",UserSession.getLoginCName());
        loginMap.put("loginName",UserSession.getLoginName());

        Map queryBlock = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
        String segNo = MapUtils.getString(queryBlock, "segNo", "");
        String segName = MapUtils.getString(queryBlock, "segName", "");
        String putInOutFlag = MapUtils.getString(queryBlock, "putInOutFlag", "");
        if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
//            throw new PlatException("缺少业务单元代码不能查询！");
            String massage = "缺少业务单元代码不能查询！";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        if ("10".equals(putInOutFlag)&&"KF000000".equals(segNo)){
            queryBlock.put("putInOutFlagP", "10");
            queryBlock.put("putInOutFlag", "");
        }else if ("20".equals(putInOutFlag)&&"KF000000".equals(segNo)){
            queryBlock.put("putInOutFlagX", "20");
        }
        /*inInfo = super.query(inInfo,"LIRL0220.query");*/
        List<Map<String,Object>> list = this.dao.queryAll(LIRL0308.QUERY_TOTAL_QUATITY_WEIGHT,queryBlock);
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(getExportBlockMeta());
        extracted(list);
        Map resultMap = EasyExcelUtil.export2FileStorageForClent(inInfo, list, loginMap);
        inInfo.setBlock(new EiBlock("excelDoc")).setAttr(resultMap);
        return inInfo;
    }

    /**
     * 新增.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo insert(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashmap : listHashMap) {
                hashmap.put("status", 10);//状态
                hashmap.put("delFlag", 0);//记录删除标记

                hashmap.put("remark", MapUtils.getString(hashmap, "remark", "").trim());//备注
                hashmap.put("uuid", StrUtil.getUUID());//UUID
                hashmap.put("recCreate", UserSession.getUserId());//创建人
                hashmap.put("recCreateTime", DateUtil.curDateTimeStr14());//创建人时间
                hashmap.put("recRevise", UserSession.getUserId());//修改人
                hashmap.put("recReviseTime", DateUtil.curDateTimeStr14());//修改人时间
            }
            inInfo = super.insert(inInfo, LIRL0101.INSERT);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 修改.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo update(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashmap : listHashMap) {
                //后台查询状态判断
                List<LIRL0101> query = dao.query(LIRL0101.QUERY, hashmap);
                for (LIRL0101 lirl0101:query){
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, lirl0101);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashmap.put("recRevise", UserSession.getUserId());//修改人
                hashmap.put("recReviseTime", DateUtil.curDateTimeStr14());//修改人时间
            }
            inInfo = super.update(inInfo, LIRL0101.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 删除.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo delete(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashmap : listHashMap) {
                //后台查询状态判断
                List<LIRL0101> query = dao.query(LIRL0101.QUERY, hashmap);
                for (LIRL0101 lirl0101:query){
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, lirl0101);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashmap.put("status", "00");//记录删除标记
                hashmap.put("delFlag", 1);//记录删除标记
                hashmap.put("recRevise", UserSession.getUserId());//修改人
                hashmap.put("recReviseTime", DateUtil.curDateTimeStr14());//修改人时间
            }
            inInfo = super.update(inInfo, LIRL0101.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 确认.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo CONFIRM(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashmap : listHashMap) {
                //后台查询状态判断
                List<LIRL0101> query = dao.query(LIRL0101.QUERY, hashmap);
                for (LIRL0101 lirl0101:query){
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, lirl0101);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashmap.put("status", 20);//状态
                hashmap.put("recRevise", UserSession.getUserId());//修改人
                hashmap.put("recReviseTime", DateUtil.curDateTimeStr14());//修改人时间
            }
            inInfo = super.update(inInfo, LIRL0101.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 反确认.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo CONFIRMNO(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashmap : listHashMap) {
                //后台查询状态判断
                List<LIRL0101> query = dao.query(LIRL0101.QUERY, hashmap);
                for (LIRL0101 lirl0101:query){
                    EiInfo outInfo = DaoUtils.isThereConfirmStatusAdded(inInfo, lirl0101);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashmap.put("status", 10);//状态
                hashmap.put("recRevise", UserSession.getUserId());//修改人
                hashmap.put("recReviseTime", DateUtil.curDateTimeStr14());//修改人时间
            }
            inInfo = super.update(inInfo, LIRL0101.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 承运商和客户调用IMC客商的服务.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo subQuery(EiInfo inInfo) {
        try {
            //购买方税号
            EiInfo outInfo = new EiInfo();
            EiInfo enterInfo = new EiInfo();
            JSONObject jsonObject = new JSONObject();
            Map queryMap = inInfo.getBlock("sub_query_status").getRow(0);
            Map hashMap = inInfo.getBlock("sub_result").getAttr();
            int limit = MapUtils.getInteger(hashMap, "limit");
            int offset = MapUtils.getInteger(hashMap, "offset");
            String userNum = (String) queryMap.get("userNum");
            if (StringUtils.isNotEmpty(userNum)) {
                enterInfo.set("userNum", userNum);
            }
            String chineseUserName = (String) queryMap.get("chineseUserName");
            if (StringUtils.isNotEmpty(chineseUserName)) {
                enterInfo.set("chineseUserName", chineseUserName);
            }
            /*enterInfo.set("limit", limit);
            enterInfo.set("offset", offset);
            enterInfo.set(EiConstant.serviceId, "S_UN_BI_0017");
            String xplatToken = ImcGlobalUtils.getToken();
            outInfo = EServiceManager.call(enterInfo, xplatToken);
            List list = outInfo.getBlock(EiConstant.resultBlock).getRows();
            if (CollectionUtils.isNotEmpty(list)) {
                // 返回成功状态和消息
                inInfo.setStatus(EiConstant.STATUS_DEFAULT);
                inInfo.setMsgByKey(MesConstant.EPResource.EP_2008);
                inInfo.setRows("sub_result",list);
                return inInfo;
            } else {
                inInfo.setStatus(EiConstant.STATUS_SUCCESS);
                inInfo.setMsgByKey(MesConstant.EPResource.EP_1001);
            }*/
            PrintWriter out = null;
            BufferedReader in = null;
            StringBuilder result = new StringBuilder();
            //下面五个参数是本服务的参数
            //shareServiceId参数是调用的必填参数  shareServiceId为需要调用的共享服务ID
            jsonObject.put("shareServiceId", "S_UN_BI_0017");
            //resProjectEname参数是调用的必填参数，resProjectEname为本次调用方的项目英文名
            jsonObject.put("resProjectEname", "elim");
            //resAppEname参数是调用的必填参数，resAppEname为本次调用方的应用英文名
            jsonObject.put("resAppEname", "elim-bc");
            String returnValue = "";
            CloseableHttpClient httpClient = HttpClients.createDefault();
            ResponseHandler<String> responseHandler = new BasicResponseHandler();
            //传入接口参数
            String url = "http://eplattest.baogang.info/service/S_UN_BI_0017";
            System.out.println(jsonObject);
            String urlNameString = url;
            URL realUrl = new URL(urlNameString);
            URLConnection conn = realUrl.openConnection();
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            conn.setRequestProperty("Accept-Charset", "UTF-8");
            conn.setRequestProperty("contentType", "UTF-8");
            conn.setRequestProperty("Content-Type", "application/json"); // 设置发送数据的格式
            String xplatToken = ImcGlobalUtils.getToken();
            conn.setRequestProperty("Xplat-Token", xplatToken); // 设置发送数据的token
            conn.setRequestProperty("token", xplatToken); // 设置发送数据的token
            conn.setDoOutput(true);
            conn.setDoInput(true);
            out = new PrintWriter(conn.getOutputStream());
            out.print(jsonObject);
            out.flush();
            in = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0001, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 查询
     * @param inInfo
     * @return
     */
    public EiInfo queryLoadingPerformance(EiInfo inInfo) {
        Map queryBlock = inInfo.getBlock(MesConstant.Iplat.INQU2_STATUS_BLOCK).getRow(0);
        String segNo = MapUtils.getString(queryBlock, "segNo", "");
        String segName = MapUtils.getString(queryBlock, "segName", "");

        if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
//            throw new PlatException("缺少业务单元代码不能查询！");
            String massage = "缺少业务单元代码不能查询！";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        String tab = MapUtils.getString(queryBlock, "tab", "");
        if("3".equals(tab)){
            queryBlock.put("tab", "");
            EiInfo outInfo = new EiInfo();
            outInfo = super.query(inInfo, LIRL0308.QUERY_LOADING_PERFORMANCE, null, false, null,
                    MesConstant.Iplat.INQU2_STATUS_BLOCK, MesConstant.Iplat.RESULT3_BLOCK, MesConstant.Iplat.RESULT3_BLOCK);
            return outInfo;

        }else{
            EiInfo outInfo = new EiInfo();
            outInfo = super.query(inInfo, LIRL0308.QUERY_LOADING_PERFORMANCE_ALL, null, false, null,
                    MesConstant.Iplat.INQU2_STATUS_BLOCK, MesConstant.Iplat.RESULT2_BLOCK, MesConstant.Iplat.RESULT2_BLOCK);
            // List<HashMap> rows = outInfo.getBlock("result2").getRows();
            // if (CollectionUtils.isNotEmpty(rows)){
            //     for (HashMap row : rows) {
            //         //取出提单信息，查询提单的交货方式
            //         String ladingBillId = MapUtils.getString(row, "voucherNum", segName);
            //         if (StringUtils.isNotBlank(ladingBillId)) {
            //             if (ladingBillId.startsWith("ZK")){
            //                 EiInfo eiInfo1 = new EiInfo();
            //                 eiInfo1.set("segNo", segNo);
            //                 eiInfo1.set("transBillId", ladingBillId);
            //                 eiInfo1.set(EiConstant.serviceId, "S_UC_EP_0044");
            //                 //调post请求
            //                 outInfo = EServiceManager.call(eiInfo1, TokenUtils.getXplatToken());
            //                 if (outInfo.getStatus() == -1) {
            //                     outInfo.setStatus(EiConstant.STATUS_FAILURE);
            //                     outInfo.setMsg("调用开单中心返回报错:" + outInfo.getMsg());
            //                     throw new RuntimeException(outInfo.getMsg());
            //                 }
            //                 List<HashMap> rusult = (List<HashMap>) outInfo.get("result");
            //                 if (CollectionUtils.isEmpty(rusult) && rusult.size() <= 0) {
            //                     outInfo.setStatus(EiConstant.STATUS_FAILURE);
            //                     outInfo.setMsg("未匹配到转库单信息!");
            //                     return outInfo;
            //                 }
            //                 List<HashMap> list = (List<HashMap>) outInfo.get("result");
            //                 if (CollectionUtils.isNotEmpty(list)) {
            //                     String deliveryType = (String) list.get(0).get("deliveryType");
            //                     if ("10".equals(deliveryType)) {
            //                         row.put("deliverTypeName", "自提");
            //                     } else if ("20".equals(deliveryType)) {
            //                         row.put("deliverTypeName", "代运");
            //                     }
            //                 }
            //             }else {
            //                 Map<String, String> hashMap = new HashMap<>();
            //                 hashMap.put("segNo", segNo);
            //                 hashMap.put("ladingBillId", ladingBillId);
            //                 //先查询提单是否存在，不存在调服务写入车辆登记提单信息表
            //                 String xplatToken = TokenUtils.getXplatToken();
            //                 inInfo.set(EiConstant.serviceId, "S_UV_SL_9020");//查询提单服务
            //                 inInfo.set("main", hashMap);
            //                 outInfo = EServiceManager.call(inInfo, xplatToken);
            //                 if (outInfo.getStatus() == -1) {
            //                     inInfo.setStatus(EiConstant.STATUS_FAILURE);
            //                     inInfo.setMsg("开单中心返回报错:" + outInfo.getMsg());
            //                     throw new RuntimeException(inInfo.getMsg());
            //                 }
            //                 List<HashMap> list = (List<HashMap>) outInfo.get("result");
            //                 if (CollectionUtils.isNotEmpty(list)) {
            //                     String deliveryType = (String) list.get(0).get("deliveryType");
            //                     if ("10".equals(deliveryType)) {
            //                         row.put("deliverTypeName", "自提");
            //                     } else if ("20".equals(deliveryType)) {
            //                         row.put("deliverTypeName", "代运");
            //                     }
            //
            //                 }
            //             }
            //
            //         }
            //     }
            //
            // }
            // outInfo.addBlock("result2").addRows(rows);
            return outInfo;

        }

    }
    public EiBlockMeta getExportBlockMeta() {
        EiBlockMeta eiMetadata = new EiBlockMeta();
        EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("业务单元名称");
        eiColumn.setFieldLength(23);
        eiColumn.setDescName("车辆跟踪号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("totalQuantity");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("装货/卸货总数量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("totalWeight");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("装货/卸货总重量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("putInOutFlag");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("装卸货标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("perName");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("行车工姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("handPointName");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("装卸点名称");
        eiMetadata.addMeta(eiColumn);

        return eiMetadata;
    }

    private static void extracted(List<Map<String,Object>> hashMapList) {
        for (Map hashMap: hashMapList){
                String putInOutFlag = MapUtils.getString(hashMap, "putInOutFlag", "");
                switch (putInOutFlag) {
                    case "10":
                        hashMap.put("putInOutFlag", "卸货");
                        break;
                    case "20":
                        hashMap.put("putInOutFlag", "装货");
                        break;
                }
            }
    }
}
