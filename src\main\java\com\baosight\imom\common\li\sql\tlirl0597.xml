<?xml version="1.0" encoding="UTF-8"?>
<!DOC<PERSON><PERSON><PERSON> sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2025-07-04 21:44:04
   		Version :  1.0
		tableName :${meliSchema}.tlirl0597 
		 MEASURE_ID  VARCHAR, 
		 LADING_SPOT_ADDR  VARCHAR, 
		 LADING_SPOT_CONTACTOR  VARCHAR, 
		 SETTLE_USER_NAME  VARCHAR, 
		 TOTAL_QTY  DECIMAL, 
		 REC_REVISE_TIME  VARCHAR, 
		 ARCHIVE_FLAG  VARCHAR, 
		 REQ_EXECUTE_PERIOD  DECIMAL, 
		 UUID  VARCHAR   NOT NULL   primarykey, 
		 REC_REVISOR  VARCHAR, 
		 TENANT_USER  VARCHAR, 
		 DEST_SPOT_ADDR  VARCHAR, 
		 DEST_SPOT_CONTACTOR  VARCHAR, 
		 REC_CREATOR_NAME  VARCHAR, 
		 UNIT_CODE  VARCHAR, 
		 REQ_EXECUTE_DATE  VARCHAR, 
		 ACTIVE_END_DATE  VARCHAR, 
		 COMPANY_OUT_BUSINESS_TYPE  VARCHAR, 
		 RED_FLAG  VARCHAR, 
		 REC_REVISOR_NAME  VARCHAR, 
		 STORE_FEE_SETTLE_TYPE  VARCHAR, 
		 DEST_SPOT_NAME  VARCHAR, 
		 CONSIGNEE_ADDR  VARCHAR, 
		 TRADE_CODE  VARCHAR, 
		 FIN_USER_ID  VARCHAR, 
		 CONSIGNEE_CNAME  VARCHAR, 
		 LADING_SPOT_ID  VARCHAR, 
		 VEHICLE_NO  VARCHAR, 
		 REC_CREATOR  VARCHAR, 
		 TRANS_TYPE  VARCHAR, 
		 DRIVER_ID  VARCHAR, 
		 RAIN_CLOTH  VARCHAR, 
		 CONSIGNEE_CODE  VARCHAR, 
		 DRIVER_NAME  VARCHAR, 
		 FIN_USER_NAME  VARCHAR, 
		 DELEIVERY_TYPE  VARCHAR, 
		 TPROVIDER_ID  VARCHAR, 
		 SUM_NET_WEIGHT  DECIMAL, 
		 TRANS_FEE_SETTLE_TYPE  VARCHAR, 
		 TRANS_BILL_ID  VARCHAR, 
		 DEST_SPOT_TELE  VARCHAR, 
		 D_USER_NAME  VARCHAR, 
		 ACTIVE_START_DATE  VARCHAR, 
		 DEST_SPOT_ADDR_ID  VARCHAR, 
		 OUT_FEE_SETTLE_TYPE  VARCHAR, 
		 REMARK  VARCHAR, 
		 TRANS_APPLY_ID  VARCHAR, 
		 DEL_FLAG  VARCHAR, 
		 BILL_PASSWORD  VARCHAR, 
		 PLAN_GROSS_WEIGHT  DECIMAL, 
		 DEST_SPOT_ID  VARCHAR, 
		 LADING_SPOT_ADDR_ID  VARCHAR, 
		 REC_CREATE_TIME  VARCHAR, 
		 FREE_STORE_BEGIN_DATE  VARCHAR, 
		 CONSIGNEE_PHONE  VARCHAR, 
		 AREA_LGS_CENTER_CODE  VARCHAR, 
		 EBILL_MARK  VARCHAR, 
		 DELIVERY_DATE  VARCHAR, 
		 CONSIGNEE_CONTACTOR  VARCHAR, 
		 DIRECT_USER_NUM  VARCHAR, 
		 RECORD_SOURCE  VARCHAR, 
		 LGS_SEG_NO  VARCHAR, 
		 CONSIGNEE_TYPE  VARCHAR, 
		 D_USER_NUM  VARCHAR, 
		 TRANS_BILL_TYPE  VARCHAR, 
		 BILLING_TIME  VARCHAR, 
		 DELIVERY_TYPE  VARCHAR, 
		 DIRECT_USER_NAME  VARCHAR, 
		 LADING_SPOT_NAME  VARCHAR, 
		 COMPANY_BUSINESS_TYPE  VARCHAR, 
		 LADING_SPOT_TELE  VARCHAR, 
		 TPROVIDER_NAME  VARCHAR, 
		 SEG_NO  VARCHAR, 
		 HAIER_ORDER_NUMBER  VARCHAR, 
		 SETTLE_USER_NUM  VARCHAR, 
		 PLAN_NET_WEIGHT  DECIMAL, 
		 DRIVER_PHONE  VARCHAR, 
		 SUM_NTOTAL_QTY  DECIMAL
	-->
<sqlMap namespace="tlirl0597">

	<select id="query" parameterClass="java.util.Map"
			resultClass="com.baosight.imom.common.li.domain.Tlirl0597">
		SELECT
				MEASURE_ID	as "measureId",  <!-- 测量ID -->
				LADING_SPOT_ADDR	as "ladingSpotAddr",  <!-- 装货地点地址 -->
				LADING_SPOT_CONTACTOR	as "ladingSpotContactor",  <!-- 装货地联系人 -->
				SETTLE_USER_NAME	as "settleUserName",  
				TOTAL_QTY	as "totalQty",  <!-- 总数量 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  
				REQ_EXECUTE_PERIOD	as "reqExecutePeriod",  <!-- 请求执行周期 -->
				UUID	as "uuid",  <!-- 唯一标识（主键） -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人ID -->
				TENANT_USER	as "tenantUser",  
				DEST_SPOT_ADDR	as "destSpotAddr",  <!-- 目的地地址 -->
				DEST_SPOT_CONTACTOR	as "destSpotContactor",  <!-- 目的地联系人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				UNIT_CODE	as "unitCode",  <!-- 单位编码 -->
				REQ_EXECUTE_DATE	as "reqExecuteDate",  
				ACTIVE_END_DATE	as "activeEndDate",  
				COMPANY_OUT_BUSINESS_TYPE	as "companyOutBusinessType",  
				RED_FLAG	as "redFlag",  <!-- 红标标志 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人 -->
				STORE_FEE_SETTLE_TYPE	as "storeFeeSettleType",  <!-- 仓储费结算类型 -->
				DEST_SPOT_NAME	as "destSpotName",  <!-- 目的地名称 -->
				CONSIGNEE_ADDR	as "consigneeAddr",  <!-- 收货方地址 -->
				TRADE_CODE	as "tradeCode",  
				FIN_USER_ID	as "finUserId",  
				CONSIGNEE_CNAME	as "consigneeCname",  <!-- 收货方中文名 -->
				LADING_SPOT_ID	as "ladingSpotId",  <!-- 装货地ID -->
				VEHICLE_NO	as "vehicleNo",  <!-- 车牌号 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人ID -->
				TRANS_TYPE	as "transType",  <!-- 运输类型 -->
				DRIVER_ID	as "driverId",  
				RAIN_CLOTH	as "rainCloth",  
				CONSIGNEE_CODE	as "consigneeCode",  <!-- 收货方代码 -->
				DRIVER_NAME	as "driverName",  
				FIN_USER_NAME	as "finUserName",  
				DELEIVERY_TYPE	as "deleiveryType",  <!-- 交付类型 -->
				TPROVIDER_ID	as "tproviderId",  <!-- 运输商ID -->
				SUM_NET_WEIGHT	as "sumNetWeight",  <!-- 总净重 -->
				TRANS_FEE_SETTLE_TYPE	as "transFeeSettleType",  <!-- 运输费结算类型 -->
				TRANS_BILL_ID	as "transBillId",  <!-- 运输单据ID -->
				DEST_SPOT_TELE	as "destSpotTele",  <!-- 目的地电话 -->
				D_USER_NAME	as "d_userName",  
				ACTIVE_START_DATE	as "activeStartDate",  
				DEST_SPOT_ADDR_ID	as "destSpotAddrId",  
				OUT_FEE_SETTLE_TYPE	as "outFeeSettleType",  <!-- 出库费结算类型 -->
				REMARK	as "remark",  <!-- 备注 -->
				TRANS_APPLY_ID	as "transApplyId",  
				DEL_FLAG	as "delFlag",  <!-- 删除标志（0未删除） -->
				BILL_PASSWORD	as "billPassword",  
				PLAN_GROSS_WEIGHT	as "planGrossWeight",  <!-- 计划毛重 -->
				DEST_SPOT_ID	as "destSpotId",  <!-- 目的地ID -->
				LADING_SPOT_ADDR_ID	as "ladingSpotAddrId",  
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				FREE_STORE_BEGIN_DATE	as "freeStoreBeginDate",  
				CONSIGNEE_PHONE	as "consigneePhone",  
				AREA_LGS_CENTER_CODE	as "areaLgsCenterCode",  <!-- 物流中心代码 -->
				EBILL_MARK	as "ebillMark",  
				DELIVERY_DATE	as "deliveryDate",  
				CONSIGNEE_CONTACTOR	as "consigneeContactor",  
				DIRECT_USER_NUM	as "directUserNum",  
				RECORD_SOURCE	as "recordSource",  <!-- 记录来源 -->
				LGS_SEG_NO	as "lgsSegNo",  <!-- 物流段号 -->
				CONSIGNEE_TYPE	as "consigneeType",  <!-- 收货方类型 -->
				D_USER_NUM	as "d_userNum",  
				TRANS_BILL_TYPE	as "transBillType",  <!-- 运输单据类型 -->
				BILLING_TIME	as "billingTime",  
				DELIVERY_TYPE	as "deliveryType",  <!-- 交付类型 -->
				DIRECT_USER_NAME	as "directUserName",  
				LADING_SPOT_NAME	as "ladingSpotName",  <!-- 装货地名称 -->
				COMPANY_BUSINESS_TYPE	as "companyBusinessType",  <!-- 公司业务类型 -->
				LADING_SPOT_TELE	as "ladingSpotTele",  <!-- 装货地电话 -->
				TPROVIDER_NAME	as "tproviderName",  <!-- 运输商名称 -->
				SEG_NO	as "segNo",  <!-- 段号 -->
				HAIER_ORDER_NUMBER	as "haierOrderNumber",  
				SETTLE_USER_NUM	as "settleUserNum",  
				PLAN_NET_WEIGHT	as "planNetWeight",  <!-- 计划净重 -->
				DRIVER_PHONE	as "driverPhone",  
				SUM_NTOTAL_QTY	as "sumNtotalQty" <!-- 总数量合计 -->
		FROM ${meliSchema}.tlirl0597 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
    		  UUID asc
		</isEmpty>
  		</dynamic>
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM ${meliSchema}.tlirl0597 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
	</select>

	<insert id="insert">
		INSERT INTO ${meliSchema}.tlirl0597 (MEASURE_ID,  <!-- 测量ID -->
										LADING_SPOT_ADDR,  <!-- 装货地点地址 -->
										LADING_SPOT_CONTACTOR,  <!-- 装货地联系人 -->
										SETTLE_USER_NAME,
										TOTAL_QTY,  <!-- 总数量 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,
										REQ_EXECUTE_PERIOD,  <!-- 请求执行周期 -->
										UUID,  <!-- 唯一标识（主键） -->
										REC_REVISOR,  <!-- 记录修改人ID -->
										TENANT_USER,
										DEST_SPOT_ADDR,  <!-- 目的地地址 -->
										DEST_SPOT_CONTACTOR,  <!-- 目的地联系人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										UNIT_CODE,  <!-- 单位编码 -->
										REQ_EXECUTE_DATE,
										ACTIVE_END_DATE,
										COMPANY_OUT_BUSINESS_TYPE,
										RED_FLAG,  <!-- 红标标志 -->
										REC_REVISOR_NAME,  <!-- 记录修改人 -->
										STORE_FEE_SETTLE_TYPE,  <!-- 仓储费结算类型 -->
										DEST_SPOT_NAME,  <!-- 目的地名称 -->
										CONSIGNEE_ADDR,  <!-- 收货方地址 -->
										TRADE_CODE,
										FIN_USER_ID,
										CONSIGNEE_CNAME,  <!-- 收货方中文名 -->
										LADING_SPOT_ID,  <!-- 装货地ID -->
										VEHICLE_NO,  <!-- 车牌号 -->
										REC_CREATOR,  <!-- 记录创建人ID -->
										TRANS_TYPE,  <!-- 运输类型 -->
										DRIVER_ID,
										RAIN_CLOTH,
										CONSIGNEE_CODE,  <!-- 收货方代码 -->
										DRIVER_NAME,
										FIN_USER_NAME,
										DELEIVERY_TYPE,  <!-- 交付类型 -->
										TPROVIDER_ID,  <!-- 运输商ID -->
										SUM_NET_WEIGHT,  <!-- 总净重 -->
										TRANS_FEE_SETTLE_TYPE,  <!-- 运输费结算类型 -->
										TRANS_BILL_ID,  <!-- 运输单据ID -->
										DEST_SPOT_TELE,  <!-- 目的地电话 -->
										D_USER_NAME,
										ACTIVE_START_DATE,
										DEST_SPOT_ADDR_ID,
										OUT_FEE_SETTLE_TYPE,  <!-- 出库费结算类型 -->
										REMARK,  <!-- 备注 -->
										TRANS_APPLY_ID,
										DEL_FLAG,  <!-- 删除标志（0未删除） -->
										BILL_PASSWORD,
										PLAN_GROSS_WEIGHT,  <!-- 计划毛重 -->
										DEST_SPOT_ID,  <!-- 目的地ID -->
										LADING_SPOT_ADDR_ID,
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										FREE_STORE_BEGIN_DATE,
										CONSIGNEE_PHONE,
										AREA_LGS_CENTER_CODE,  <!-- 物流中心代码 -->
										EBILL_MARK,
										DELIVERY_DATE,
										CONSIGNEE_CONTACTOR,
										DIRECT_USER_NUM,
										RECORD_SOURCE,  <!-- 记录来源 -->
										LGS_SEG_NO,  <!-- 物流段号 -->
										CONSIGNEE_TYPE,  <!-- 收货方类型 -->
										D_USER_NUM,
										TRANS_BILL_TYPE,  <!-- 运输单据类型 -->
										BILLING_TIME,
										DELIVERY_TYPE,  <!-- 交付类型 -->
										DIRECT_USER_NAME,
										LADING_SPOT_NAME,  <!-- 装货地名称 -->
										COMPANY_BUSINESS_TYPE,  <!-- 公司业务类型 -->
										LADING_SPOT_TELE,  <!-- 装货地电话 -->
										TPROVIDER_NAME,  <!-- 运输商名称 -->
										SEG_NO,  <!-- 段号 -->
										HAIER_ORDER_NUMBER,
										SETTLE_USER_NUM,
										PLAN_NET_WEIGHT,  <!-- 计划净重 -->
										DRIVER_PHONE,
										SUM_NTOTAL_QTY  <!-- 总数量合计 -->
										)		 
	    VALUES (#measureId#, #ladingSpotAddr#, #ladingSpotContactor#, #settleUserName#, #totalQty#, #recReviseTime#, #archiveFlag#, #reqExecutePeriod#, #uuid#, #recRevisor#, #tenantUser#, #destSpotAddr#, #destSpotContactor#, #recCreatorName#, #unitCode#, #reqExecuteDate#, #activeEndDate#, #companyOutBusinessType#, #redFlag#, #recRevisorName#, #storeFeeSettleType#, #destSpotName#, #consigneeAddr#, #tradeCode#, #finUserId#, #consigneeCname#, #ladingSpotId#, #vehicleNo#, #recCreator#, #transType#, #driverId#, #rainCloth#, #consigneeCode#, #driverName#, #finUserName#, #deleiveryType#, #tproviderId#, #sumNetWeight#, #transFeeSettleType#, #transBillId#, #destSpotTele#, #d_userName#, #activeStartDate#, #destSpotAddrId#, #outFeeSettleType#, #remark#, #transApplyId#, #delFlag#, #billPassword#, #planGrossWeight#, #destSpotId#, #ladingSpotAddrId#, #recCreateTime#, #freeStoreBeginDate#, #consigneePhone#, #areaLgsCenterCode#, #ebillMark#, #deliveryDate#, #consigneeContactor#, #directUserNum#, #recordSource#, #lgsSegNo#, #consigneeType#, #d_userNum#, #transBillType#, #billingTime#, #deliveryType#, #directUserName#, #ladingSpotName#, #companyBusinessType#, #ladingSpotTele#, #tproviderName#, #segNo#, #haierOrderNumber#, #settleUserNum#, #planNetWeight#, #driverPhone#, #sumNtotalQty#) 
	</insert>
  
	<delete id="delete">
		DELETE FROM ${meliSchema}.tlirl0597 WHERE 
			1=1
		AND SEG_NO = #segNo#
	</delete>

	<update id="update">
		UPDATE ${meliSchema}.tlirl0597 
		SET 
		MEASURE_ID	= #measureId#,   <!-- 测量ID -->  
					LADING_SPOT_ADDR	= #ladingSpotAddr#,   <!-- 装货地点地址 -->  
					LADING_SPOT_CONTACTOR	= #ladingSpotContactor#,   <!-- 装货地联系人 -->  
					SETTLE_USER_NAME	= #settleUserName#,   
					TOTAL_QTY	= #totalQty#,   <!-- 总数量 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   
					REQ_EXECUTE_PERIOD	= #reqExecutePeriod#,   <!-- 请求执行周期 -->  
								REC_REVISOR	= #recRevisor#,   <!-- 记录修改人ID -->  
					TENANT_USER	= #tenantUser#,   
					DEST_SPOT_ADDR	= #destSpotAddr#,   <!-- 目的地地址 -->  
					DEST_SPOT_CONTACTOR	= #destSpotContactor#,   <!-- 目的地联系人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					UNIT_CODE	= #unitCode#,   <!-- 单位编码 -->  
					REQ_EXECUTE_DATE	= #reqExecuteDate#,   
					ACTIVE_END_DATE	= #activeEndDate#,   
					COMPANY_OUT_BUSINESS_TYPE	= #companyOutBusinessType#,   
					RED_FLAG	= #redFlag#,   <!-- 红标标志 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人 -->  
					STORE_FEE_SETTLE_TYPE	= #storeFeeSettleType#,   <!-- 仓储费结算类型 -->  
					DEST_SPOT_NAME	= #destSpotName#,   <!-- 目的地名称 -->  
					CONSIGNEE_ADDR	= #consigneeAddr#,   <!-- 收货方地址 -->  
					TRADE_CODE	= #tradeCode#,   
					FIN_USER_ID	= #finUserId#,   
					CONSIGNEE_CNAME	= #consigneeCname#,   <!-- 收货方中文名 -->  
					LADING_SPOT_ID	= #ladingSpotId#,   <!-- 装货地ID -->  
					VEHICLE_NO	= #vehicleNo#,   <!-- 车牌号 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人ID -->  
					TRANS_TYPE	= #transType#,   <!-- 运输类型 -->  
					DRIVER_ID	= #driverId#,   
					RAIN_CLOTH	= #rainCloth#,   
					CONSIGNEE_CODE	= #consigneeCode#,   <!-- 收货方代码 -->  
					DRIVER_NAME	= #driverName#,   
					FIN_USER_NAME	= #finUserName#,   
					DELEIVERY_TYPE	= #deleiveryType#,   <!-- 交付类型 -->  
					TPROVIDER_ID	= #tproviderId#,   <!-- 运输商ID -->  
					SUM_NET_WEIGHT	= #sumNetWeight#,   <!-- 总净重 -->  
					TRANS_FEE_SETTLE_TYPE	= #transFeeSettleType#,   <!-- 运输费结算类型 -->  
					TRANS_BILL_ID	= #transBillId#,   <!-- 运输单据ID -->  
					DEST_SPOT_TELE	= #destSpotTele#,   <!-- 目的地电话 -->  
					D_USER_NAME	= #d_userName#,   
					ACTIVE_START_DATE	= #activeStartDate#,   
					DEST_SPOT_ADDR_ID	= #destSpotAddrId#,   
					OUT_FEE_SETTLE_TYPE	= #outFeeSettleType#,   <!-- 出库费结算类型 -->  
					REMARK	= #remark#,   <!-- 备注 -->  
					TRANS_APPLY_ID	= #transApplyId#,   
					DEL_FLAG	= #delFlag#,   <!-- 删除标志（0未删除） -->  
					BILL_PASSWORD	= #billPassword#,   
					PLAN_GROSS_WEIGHT	= #planGrossWeight#,   <!-- 计划毛重 -->  
					DEST_SPOT_ID	= #destSpotId#,   <!-- 目的地ID -->  
					LADING_SPOT_ADDR_ID	= #ladingSpotAddrId#,   
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					FREE_STORE_BEGIN_DATE	= #freeStoreBeginDate#,   
					CONSIGNEE_PHONE	= #consigneePhone#,   
					AREA_LGS_CENTER_CODE	= #areaLgsCenterCode#,   <!-- 物流中心代码 -->  
					EBILL_MARK	= #ebillMark#,   
					DELIVERY_DATE	= #deliveryDate#,   
					CONSIGNEE_CONTACTOR	= #consigneeContactor#,   
					DIRECT_USER_NUM	= #directUserNum#,   
					RECORD_SOURCE	= #recordSource#,   <!-- 记录来源 -->  
					LGS_SEG_NO	= #lgsSegNo#,   <!-- 物流段号 -->  
					CONSIGNEE_TYPE	= #consigneeType#,   <!-- 收货方类型 -->  
					D_USER_NUM	= #d_userNum#,   
					TRANS_BILL_TYPE	= #transBillType#,   <!-- 运输单据类型 -->  
					BILLING_TIME	= #billingTime#,   
					DELIVERY_TYPE	= #deliveryType#,   <!-- 交付类型 -->  
					DIRECT_USER_NAME	= #directUserName#,   
					LADING_SPOT_NAME	= #ladingSpotName#,   <!-- 装货地名称 -->  
					COMPANY_BUSINESS_TYPE	= #companyBusinessType#,   <!-- 公司业务类型 -->  
					LADING_SPOT_TELE	= #ladingSpotTele#,   <!-- 装货地电话 -->  
					TPROVIDER_NAME	= #tproviderName#,   <!-- 运输商名称 -->  
					SEG_NO	= #segNo#,   <!-- 段号 -->  
					HAIER_ORDER_NUMBER	= #haierOrderNumber#,   
					SETTLE_USER_NUM	= #settleUserNum#,   
					PLAN_NET_WEIGHT	= #planNetWeight#,   <!-- 计划净重 -->  
					DRIVER_PHONE	= #driverPhone#,   
					SUM_NTOTAL_QTY	= #sumNtotalQty#  <!-- 总数量合计 -->  
			WHERE 	
			UUID = #uuid#
	</update>
  
</sqlMap>